<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة AI Chat Bot</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .icon-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        
        .icon-preview {
            width: 256px;
            height: 256px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 15px 30px rgba(79, 172, 254, 0.3);
        }
        
        .icon-content {
            color: white;
            font-size: 80px;
            font-weight: bold;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .icon-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: right;
            direction: rtl;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-right: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="icon-container">
        <div class="icon-preview" id="iconPreview">
            <div class="icon-content">🤖</div>
            <div class="icon-badge">AI</div>
        </div>
        
        <h1 class="title">AI Chat Bot</h1>
        <p class="subtitle">بوت الدردشة الذكي</p>
        
        <button class="download-btn" onclick="downloadIcon()">
            📥 تحميل الأيقونة
        </button>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <div class="step">
                <strong>1.</strong> اضغط على "تحميل الأيقونة" لحفظ الأيقونة
            </div>
            <div class="step">
                <strong>2.</strong> احفظ الملف باسم "icon.png" في مجلد assets
            </div>
            <div class="step">
                <strong>3.</strong> استخدم أداة تحويل لإنشاء icon.ico
            </div>
            <div class="step">
                <strong>4.</strong> ضع الأيقونة في مجلد المشروع
            </div>
        </div>
    </div>

    <script>
        function downloadIcon() {
            const canvas = document.createElement('canvas');
            canvas.width = 256;
            canvas.height = 256;
            const ctx = canvas.getContext('2d');
            
            // رسم الخلفية المتدرجة
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#4facfe');
            gradient.addColorStop(1, '#00f2fe');
            
            ctx.fillStyle = gradient;
            ctx.roundRect(0, 0, 256, 256, 50);
            ctx.fill();
            
            // رسم الروبوت
            ctx.font = 'bold 120px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🤖', 128, 128);
            
            // رسم شارة AI
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(200, 56, 25, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = 'white';
            ctx.fillText('AI', 200, 56);
            
            // تحميل الأيقونة
            const link = document.createElement('a');
            link.download = 'ai-chat-bot-icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // إضافة دعم roundRect للمتصفحات القديمة
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
