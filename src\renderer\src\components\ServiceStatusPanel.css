/* لوحة حالة الخدمات */
.service-status-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.service-status-panel {
  background: var(--bg-primary);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 90vw;
  max-width: 1000px;
  height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border-color);
  animation: slideUp 0.3s ease-out;
}

/* رأس اللوحة */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
}

.status-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-title p {
  margin: 4px 0 0 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.refresh-btn {
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-btn.refreshing {
  animation: pulse 1.5s infinite;
}

.close-button {
  background: var(--bg-tertiary);
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 18px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.05);
}

/* إحصائيات عامة */
.status-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px 32px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border-radius: 10px;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--accent-color);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* قائمة الخدمات */
.services-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 32px;
}

.services-list h3 {
  margin: 0 0 20px 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.service-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.service-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.service-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.service-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  border-radius: 10px;
}

.service-details h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.service-type {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--bg-primary);
  padding: 2px 8px;
  border-radius: 10px;
  margin-top: 4px;
  display: inline-block;
}

.status-badge {
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.service-content {
  padding: 20px;
}

.service-section {
  margin-bottom: 16px;
}

.service-section:last-child {
  margin-bottom: 0;
}

.service-section h5 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
}

.models-list,
.capabilities-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.model-tag,
.capability-tag {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.model-tag.more {
  background: var(--accent-color);
  color: white;
}

.capability-tag {
  background: linear-gradient(135deg, var(--accent-color), #8b5cf6);
  color: white;
}

.no-models {
  color: var(--text-secondary);
  font-size: 12px;
  font-style: italic;
  margin: 0;
}

.service-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 4px 8px;
  border-radius: 8px;
}

/* حالة عدم وجود خدمات */
.no-services {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.no-services-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-services h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.no-services p {
  margin: 0;
  font-size: 14px;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
  .service-status-panel {
    width: 95vw;
    height: 90vh;
  }

  .status-header,
  .services-list,
  .status-footer {
    padding: 16px 20px;
  }

  .status-stats {
    padding: 16px 20px;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
}
