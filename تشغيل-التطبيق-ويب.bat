@echo off
chcp 65001 >nul
echo ======================================================
echo تشغيل تطبيق الكتابة الإبداعية - نسخة الويب
echo ======================================================
echo.
echo جاري التحقق من متطلبات التشغيل...

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] لم يتم العثور على Node.js. يرجى تثبيته أولاً.
    pause
    exit /b 1
)

REM التحقق من وجود npm
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] لم يتم العثور على npm. يرجى تثبيت Node.js بشكل صحيح.
    pause
    exit /b 1
)

echo [✓] تم العثور على Node.js و npm
echo.

REM التحقق من وجود حزم التطبيق
if not exist node_modules (
    echo [!] لم يتم العثور على حزم التطبيق. جاري تثبيتها...
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo [!] فشل في تثبيت حزم التطبيق.
        pause
        exit /b 1
    )
    echo [✓] تم تثبيت حزم التطبيق بنجاح
) else (
    echo [✓] حزم التطبيق موجودة
)

echo.
echo جاري إنشاء ملف تكوين الويب...

REM إنشاء ملف vite-web.config.js
echo import { defineConfig } from 'vite'; > vite-web.config.js
echo import react from '@vitejs/plugin-react'; >> vite-web.config.js
echo. >> vite-web.config.js
echo // تكوين Vite لنسخة الويب >> vite-web.config.js
echo export default defineConfig({ >> vite-web.config.js
echo   plugins: [react()], >> vite-web.config.js
echo   root: 'src/renderer', >> vite-web.config.js
echo   base: './', >> vite-web.config.js
echo   server: { >> vite-web.config.js
echo     port: 3000, >> vite-web.config.js
echo     open: true, >> vite-web.config.js
echo   }, >> vite-web.config.js
echo   build: { >> vite-web.config.js
echo     outDir: '../../dist/web', >> vite-web.config.js
echo     emptyOutDir: true, >> vite-web.config.js
echo   }, >> vite-web.config.js
echo   define: { >> vite-web.config.js
echo     'process.env.IS_WEB': JSON.stringify(true), >> vite-web.config.js
echo   }, >> vite-web.config.js
echo }); >> vite-web.config.js

echo.
echo جاري إنشاء ملف تهيئة الويب...

REM إنشاء ملف web-init.js
echo // ملف تهيئة نسخة الويب > src/renderer/web-init.js
echo window.electronAPI = { >> src/renderer/web-init.js
echo   // محاكاة واجهة Electron API >> src/renderer/web-init.js
echo   sendMessage: async (data) => { >> src/renderer/web-init.js
echo     console.log('Web mode: sendMessage', data); >> src/renderer/web-init.js
echo     // محاكاة استجابة الذكاء الاصطناعي >> src/renderer/web-init.js
echo     return { >> src/renderer/web-init.js
echo       success: true, >> src/renderer/web-init.js
echo       response: 'هذه استجابة تجريبية في وضع الويب. في الإصدار الكامل، سيتم استخدام نماذج الذكاء الاصطناعي الحقيقية.', >> src/renderer/web-init.js
echo       conversation: { >> src/renderer/web-init.js
echo         id: 'web-conversation', >> src/renderer/web-init.js
echo         title: 'محادثة تجريبية', >> src/renderer/web-init.js
echo         messages: [ >> src/renderer/web-init.js
echo           { id: '1', role: 'user', content: data.message, timestamp: new Date().toISOString() }, >> src/renderer/web-init.js
echo           { id: '2', role: 'assistant', content: 'هذه استجابة تجريبية في وضع الويب.', timestamp: new Date().toISOString() } >> src/renderer/web-init.js
echo         ], >> src/renderer/web-init.js
echo         createdAt: new Date().toISOString(), >> src/renderer/web-init.js
echo         updatedAt: new Date().toISOString(), >> src/renderer/web-init.js
echo         model: data.model, >> src/renderer/web-init.js
echo         isEncrypted: false >> src/renderer/web-init.js
echo       }, >> src/renderer/web-init.js
echo       usedProvider: 'web', >> src/renderer/web-init.js
echo       modelUsed: data.model >> src/renderer/web-init.js
echo     }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   getAvailableModels: async () => { >> src/renderer/web-init.js
echo     console.log('Web mode: getAvailableModels'); >> src/renderer/web-init.js
echo     // محاكاة قائمة النماذج المتاحة >> src/renderer/web-init.js
echo     return { >> src/renderer/web-init.js
echo       success: true, >> src/renderer/web-init.js
echo       models: { >> src/renderer/web-init.js
echo         local: [ >> src/renderer/web-init.js
echo           { >> src/renderer/web-init.js
echo             id: 'local/novel-assistant', >> src/renderer/web-init.js
echo             name: '📚 مساعد الروايات', >> src/renderer/web-init.js
echo             description: 'نموذج متخصص في كتابة وتطوير الروايات', >> src/renderer/web-init.js
echo             context_length: 8192, >> src/renderer/web-init.js
echo             pricing: { prompt: 'مجاني', completion: 'مجاني' }, >> src/renderer/web-init.js
echo             architecture: { modality: 'text', tokenizer: 'local' }, >> src/renderer/web-init.js
echo             top_provider: { is_moderated: true } >> src/renderer/web-init.js
echo           }, >> src/renderer/web-init.js
echo           { >> src/renderer/web-init.js
echo             id: 'local/poetry-master', >> src/renderer/web-init.js
echo             name: '🎵 سيد الشعر', >> src/renderer/web-init.js
echo             description: 'نموذج متخصص في كتابة وتحليل الشعر', >> src/renderer/web-init.js
echo             context_length: 4096, >> src/renderer/web-init.js
echo             pricing: { prompt: 'مجاني', completion: 'مجاني' }, >> src/renderer/web-init.js
echo             architecture: { modality: 'text', tokenizer: 'local' }, >> src/renderer/web-init.js
echo             top_provider: { is_moderated: true } >> src/renderer/web-init.js
echo           } >> src/renderer/web-init.js
echo         ], >> src/renderer/web-init.js
echo         online: [ >> src/renderer/web-init.js
echo           { >> src/renderer/web-init.js
echo             id: 'literary/naguib-mahfouz:custom', >> src/renderer/web-init.js
echo             name: '📚 نجيب محفوظ - الروائي', >> src/renderer/web-init.js
echo             description: 'يحاكي أسلوب نجيب محفوظ في الواقعية الاجتماعية', >> src/renderer/web-init.js
echo             context_length: 4096, >> src/renderer/web-init.js
echo             pricing: { prompt: 'مجاني', completion: 'مجاني' }, >> src/renderer/web-init.js
echo             architecture: { modality: 'text', tokenizer: 'literary' }, >> src/renderer/web-init.js
echo             top_provider: { is_moderated: false } >> src/renderer/web-init.js
echo           }, >> src/renderer/web-init.js
echo           { >> src/renderer/web-init.js
echo             id: 'literary/ahmed-fouad-negm:custom', >> src/renderer/web-init.js
echo             name: '🎵 أحمد فؤاد نجم - الشاعر', >> src/renderer/web-init.js
echo             description: 'يحاكي أسلوب أحمد فؤاد نجم في الشعر الثوري', >> src/renderer/web-init.js
echo             context_length: 4096, >> src/renderer/web-init.js
echo             pricing: { prompt: 'مجاني', completion: 'مجاني' }, >> src/renderer/web-init.js
echo             architecture: { modality: 'text', tokenizer: 'literary' }, >> src/renderer/web-init.js
echo             top_provider: { is_moderated: false } >> src/renderer/web-init.js
echo           } >> src/renderer/web-init.js
echo         ], >> src/renderer/web-init.js
echo         combined: [] // سيتم ملؤها تلقائياً >> src/renderer/web-init.js
echo       } >> src/renderer/web-init.js
echo     }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   createConversation: async (title) => { >> src/renderer/web-init.js
echo     console.log('Web mode: createConversation', title); >> src/renderer/web-init.js
echo     return { >> src/renderer/web-init.js
echo       success: true, >> src/renderer/web-init.js
echo       conversation: { >> src/renderer/web-init.js
echo         id: 'web-conversation-' + Date.now(), >> src/renderer/web-init.js
echo         title: title || 'محادثة جديدة', >> src/renderer/web-init.js
echo         messages: [], >> src/renderer/web-init.js
echo         createdAt: new Date().toISOString(), >> src/renderer/web-init.js
echo         updatedAt: new Date().toISOString(), >> src/renderer/web-init.js
echo         model: 'local/novel-assistant', >> src/renderer/web-init.js
echo         isEncrypted: false >> src/renderer/web-init.js
echo       } >> src/renderer/web-init.js
echo     }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   getConversations: async () => { >> src/renderer/web-init.js
echo     console.log('Web mode: getConversations'); >> src/renderer/web-init.js
echo     return { >> src/renderer/web-init.js
echo       success: true, >> src/renderer/web-init.js
echo       conversations: [] >> src/renderer/web-init.js
echo     }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   getConversation: async (conversationId) => { >> src/renderer/web-init.js
echo     console.log('Web mode: getConversation', conversationId); >> src/renderer/web-init.js
echo     return { >> src/renderer/web-init.js
echo       success: true, >> src/renderer/web-init.js
echo       conversation: { >> src/renderer/web-init.js
echo         id: conversationId, >> src/renderer/web-init.js
echo         title: 'محادثة تجريبية', >> src/renderer/web-init.js
echo         messages: [], >> src/renderer/web-init.js
echo         createdAt: new Date().toISOString(), >> src/renderer/web-init.js
echo         updatedAt: new Date().toISOString(), >> src/renderer/web-init.js
echo         model: 'local/novel-assistant', >> src/renderer/web-init.js
echo         isEncrypted: false >> src/renderer/web-init.js
echo       } >> src/renderer/web-init.js
echo     }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   deleteConversation: async (conversationId) => { >> src/renderer/web-init.js
echo     console.log('Web mode: deleteConversation', conversationId); >> src/renderer/web-init.js
echo     return { success: true }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   exportConversation: async (data) => { >> src/renderer/web-init.js
echo     console.log('Web mode: exportConversation', data); >> src/renderer/web-init.js
echo     return { success: true, filePath: 'web-export.txt' }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   saveSettings: async (settings) => { >> src/renderer/web-init.js
echo     console.log('Web mode: saveSettings', settings); >> src/renderer/web-init.js
echo     return { success: true }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   getSettings: async () => { >> src/renderer/web-init.js
echo     console.log('Web mode: getSettings'); >> src/renderer/web-init.js
echo     return { >> src/renderer/web-init.js
echo       success: true, >> src/renderer/web-init.js
echo       settings: { >> src/renderer/web-init.js
echo         openrouter_api_key: '', >> src/renderer/web-init.js
echo         default_model: 'local/novel-assistant', >> src/renderer/web-init.js
echo         theme: 'dark', >> src/renderer/web-init.js
echo         language: 'ar', >> src/renderer/web-init.js
echo         auto_save: true, >> src/renderer/web-init.js
echo         font_size: 'medium', >> src/renderer/web-init.js
echo         show_timestamps: true, >> src/renderer/web-init.js
echo         enable_sound: false, >> src/renderer/web-init.js
echo         max_tokens: 2048, >> src/renderer/web-init.js
echo         temperature: 0.7, >> src/renderer/web-init.js
echo         ollama_base_url: 'http://localhost:11434/v1' >> src/renderer/web-init.js
echo       } >> src/renderer/web-init.js
echo     }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   updateAISettings: async (settings) => { >> src/renderer/web-init.js
echo     console.log('Web mode: updateAISettings', settings); >> src/renderer/web-init.js
echo     return { success: true }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   getServicesStatus: async () => { >> src/renderer/web-init.js
echo     console.log('Web mode: getServicesStatus'); >> src/renderer/web-init.js
echo     return { >> src/renderer/web-init.js
echo       success: true, >> src/renderer/web-init.js
echo       status: { >> src/renderer/web-init.js
echo         lmStudio: { >> src/renderer/web-init.js
echo           available: true, >> src/renderer/web-init.js
echo           modelsCount: 3, >> src/renderer/web-init.js
echo           status: 'متصل (وضع الويب)' >> src/renderer/web-init.js
echo         }, >> src/renderer/web-init.js
echo         msty: { >> src/renderer/web-init.js
echo           available: true, >> src/renderer/web-init.js
echo           modelsCount: 5, >> src/renderer/web-init.js
echo           status: 'متصل (وضع الويب)' >> src/renderer/web-init.js
echo         }, >> src/renderer/web-init.js
echo         openRouter: { >> src/renderer/web-init.js
echo           available: true, >> src/renderer/web-init.js
echo           hasApiKey: true, >> src/renderer/web-init.js
echo           status: 'متصل (وضع الويب)' >> src/renderer/web-init.js
echo         } >> src/renderer/web-init.js
echo       } >> src/renderer/web-init.js
echo     }; >> src/renderer/web-init.js
echo   }, >> src/renderer/web-init.js
echo   launchLMStudio: async () => { >> src/renderer/web-init.js
echo     console.log('Web mode: launchLMStudio'); >> src/renderer/web-init.js
echo     return { success: true }; >> src/renderer/web-init.js
echo   } >> src/renderer/web-init.js
echo }; >> src/renderer/web-init.js

echo.
echo جاري تعديل ملف index.html...

REM إضافة سطر لتضمين ملف web-init.js في index.html
echo ^<script type="module" src="./web-init.js"^>^</script^> > web-init-script.tmp
findstr /v "</head>" src/renderer/index.html > index.html.tmp
type web-init-script.tmp >> index.html.tmp
echo ^</head^> >> index.html.tmp
findstr /v "<head>" index.html.tmp > src/renderer/index.html.new
move /y src/renderer/index.html.new src/renderer/index.html
del index.html.tmp
del web-init-script.tmp

echo.
echo جاري تشغيل التطبيق في وضع الويب...
echo.
echo [i] نصائح للاستخدام الأمثل:
echo  - هذه نسخة ويب تجريبية للتطبيق
echo  - يمكنك تجربة واجهة المستخدم والتفاعل معها
echo  - استخدم قوالب الكتابة الإبداعية للبدء بسرعة
echo.
echo [i] للخروج من التطبيق، أغلق هذه النافذة أو اضغط Ctrl+C
echo.

REM تشغيل التطبيق في وضع الويب
call npx vite --config vite-web.config.js

echo.
if %ERRORLEVEL% NEQ 0 (
    echo [!] حدث خطأ أثناء تشغيل التطبيق.
) else (
    echo [✓] تم إغلاق التطبيق بنجاح.
)

pause