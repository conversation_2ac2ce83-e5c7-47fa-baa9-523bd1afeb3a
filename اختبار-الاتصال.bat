@echo off
chcp 65001 >nul
echo ======================================================
echo اختبار اتصال خدمات الذكاء الاصطناعي
echo ======================================================
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] لم يتم العثور على Node.js. يرجى تثبيته أولاً.
    pause
    exit /b 1
)

echo [✓] تم العثور على Node.js
echo.
echo جاري إنشاء ملف اختبار مؤقت...

REM إنشاء ملف JavaScript مؤقت لاختبار الاتصال
echo const axios = require('axios'); > test-connection.js
echo. >> test-connection.js
echo // اختبار الاتصال بـ Ollama >> test-connection.js
echo async function testOllama() { >> test-connection.js
echo   try { >> test-connection.js
echo     console.log('جاري اختبار الاتصال بـ Ollama...'); >> test-connection.js
echo     const response = await axios.get('http://localhost:11434/v1/models', { timeout: 5000 }); >> test-connection.js
echo     if (response.status === 200) { >> test-connection.js
echo       console.log('\x1b[32m[✓] تم الاتصال بـ Ollama بنجاح\x1b[0m'); >> test-connection.js
echo       console.log('النماذج المتاحة:'); >> test-connection.js
echo       if (response.data && response.data.models) { >> test-connection.js
echo         response.data.models.forEach(model => { >> test-connection.js
echo           console.log(`  - ${model.name}`); >> test-connection.js
echo         }); >> test-connection.js
echo       } else { >> test-connection.js
echo         console.log('  لا توجد نماذج متاحة. يرجى تنزيل النماذج باستخدام أمر: ollama pull llama2'); >> test-connection.js
echo       } >> test-connection.js
echo     } >> test-connection.js
echo     return true; >> test-connection.js
echo   } catch (error) { >> test-connection.js
echo     console.log('\x1b[31m[✗] فشل الاتصال بـ Ollama\x1b[0m'); >> test-connection.js
echo     console.log(`  السبب: ${error.message}`); >> test-connection.js
echo     console.log('  تأكد من تشغيل Ollama على جهازك وأنه يعمل على المنفذ 11434'); >> test-connection.js
echo     return false; >> test-connection.js
echo   } >> test-connection.js
echo } >> test-connection.js
echo. >> test-connection.js
echo // اختبار الاتصال بـ OpenRouter >> test-connection.js
echo async function testOpenRouter(apiKey) { >> test-connection.js
echo   if (!apiKey) { >> test-connection.js
echo     console.log('\x1b[33m[!] لم يتم توفير مفتاح API لـ OpenRouter\x1b[0m'); >> test-connection.js
echo     console.log('  يمكنك الحصول على مفتاح API من https://openrouter.ai'); >> test-connection.js
echo     return false; >> test-connection.js
echo   } >> test-connection.js
echo. >> test-connection.js
echo   try { >> test-connection.js
echo     console.log('جاري اختبار الاتصال بـ OpenRouter...'); >> test-connection.js
echo     const response = await axios.get('https://openrouter.ai/api/v1/models', { >> test-connection.js
echo       headers: { >> test-connection.js
echo         'Authorization': `Bearer ${apiKey}`, >> test-connection.js
echo         'HTTP-Referer': 'https://ai-chat-bot.local', >> test-connection.js
echo         'X-Title': 'AI Chat Bot' >> test-connection.js
echo       }, >> test-connection.js
echo       timeout: 10000 >> test-connection.js
echo     }); >> test-connection.js
echo. >> test-connection.js
echo     if (response.status === 200) { >> test-connection.js
echo       console.log('\x1b[32m[✓] تم الاتصال بـ OpenRouter بنجاح\x1b[0m'); >> test-connection.js
echo       console.log('النماذج المتاحة (عينة):'); >> test-connection.js
echo       if (response.data && response.data.data) { >> test-connection.js
echo         const freeModels = response.data.data.filter(model => >> test-connection.js
echo           model.id.includes(':free') || >> test-connection.js
echo           (model.pricing && model.pricing.prompt === '0' && model.pricing.completion === '0') >> test-connection.js
echo         ).slice(0, 5); >> test-connection.js
echo. >> test-connection.js
echo         freeModels.forEach(model => { >> test-connection.js
echo           console.log(`  - ${model.id}`); >> test-connection.js
echo         }); >> test-connection.js
echo. >> test-connection.js
echo         if (freeModels.length === 0) { >> test-connection.js
echo           console.log('  لم يتم العثور على نماذج مجانية'); >> test-connection.js
echo         } >> test-connection.js
echo       } >> test-connection.js
echo       return true; >> test-connection.js
echo     } >> test-connection.js
echo   } catch (error) { >> test-connection.js
echo     console.log('\x1b[31m[✗] فشل الاتصال بـ OpenRouter\x1b[0m'); >> test-connection.js
echo     if (error.response && error.response.status === 401) { >> test-connection.js
echo       console.log('  السبب: مفتاح API غير صالح'); >> test-connection.js
echo     } else { >> test-connection.js
echo       console.log(`  السبب: ${error.message}`); >> test-connection.js
echo     } >> test-connection.js
echo     return false; >> test-connection.js
echo   } >> test-connection.js
echo } >> test-connection.js
echo. >> test-connection.js
echo // تشغيل الاختبارات >> test-connection.js
echo async function runTests() { >> test-connection.js
echo   console.log('======================================================'); >> test-connection.js
echo   console.log('اختبار اتصال خدمات الذكاء الاصطناعي'); >> test-connection.js
echo   console.log('======================================================'); >> test-connection.js
echo   console.log(); >> test-connection.js
echo. >> test-connection.js
echo   const ollamaResult = await testOllama(); >> test-connection.js
echo   console.log(); >> test-connection.js
echo. >> test-connection.js
echo   // اطلب من المستخدم إدخال مفتاح API لـ OpenRouter >> test-connection.js
echo   const readline = require('readline').createInterface({ >> test-connection.js
echo     input: process.stdin, >> test-connection.js
echo     output: process.stdout >> test-connection.js
echo   }); >> test-connection.js
echo. >> test-connection.js
echo   readline.question('أدخل مفتاح API لـ OpenRouter (اضغط Enter للتخطي): ', async (apiKey) => { >> test-connection.js
echo     if (apiKey.trim()) { >> test-connection.js
echo       await testOpenRouter(apiKey.trim()); >> test-connection.js
echo     } else { >> test-connection.js
echo       console.log('\x1b[33m[!] تم تخطي اختبار OpenRouter\x1b[0m'); >> test-connection.js
echo     } >> test-connection.js
echo. >> test-connection.js
echo     console.log(); >> test-connection.js
echo     console.log('======================================================'); >> test-connection.js
echo     if (ollamaResult) { >> test-connection.js
echo       console.log('\x1b[32m[✓] يمكنك الآن تشغيل التطبيق باستخدام النماذج المحلية\x1b[0m'); >> test-connection.js
echo     } else { >> test-connection.js
echo       console.log('\x1b[33m[!] قد تواجه مشكلات في استخدام النماذج المحلية\x1b[0m'); >> test-connection.js
echo     } >> test-connection.js
echo     console.log('======================================================'); >> test-connection.js
echo. >> test-connection.js
echo     readline.close(); >> test-connection.js
echo     // حذف الملف المؤقت بعد الانتهاء >> test-connection.js
echo     setTimeout(() => { >> test-connection.js
echo       require('fs').unlinkSync('test-connection.js'); >> test-connection.js
echo     }, 1000); >> test-connection.js
echo   }); >> test-connection.js
echo } >> test-connection.js
echo. >> test-connection.js
echo runTests(); >> test-connection.js

echo [✓] تم إنشاء ملف الاختبار
echo.
echo جاري تثبيت الحزم المطلوبة...
call npm install axios --no-save

echo.
echo جاري تشغيل اختبار الاتصال...
echo.
node test-connection.js

pause