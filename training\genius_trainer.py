#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 Genius AI Trainer - مدرب الذكاء الاصطناعي العبقري
تدريب نماذج ذكية مخصصة باستخدام LLaMA-Factory
"""

import os
import sys
import json
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training/logs/genius_trainer.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class GeniusTrainer:
    """مدرب النماذج العبقرية"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.llama_factory_dir = self.base_dir / "LLaMA-Factory"
        self.data_dir = self.base_dir / "data"
        self.models_dir = self.base_dir / "models"
        self.configs_dir = self.base_dir / "configs"
        self.logs_dir = self.base_dir / "logs"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        for dir_path in [self.data_dir, self.models_dir, self.configs_dir, self.logs_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def check_environment(self) -> bool:
        """فحص البيئة والمتطلبات"""
        try:
            # فحص Python
            python_version = sys.version_info
            if python_version.major < 3 or python_version.minor < 8:
                logger.error("يتطلب Python 3.8 أو أحدث")
                return False
            
            # فحص LLaMA-Factory
            if not self.llama_factory_dir.exists():
                logger.error("LLaMA-Factory غير موجود")
                return False
            
            # فحص المكتبات المطلوبة
            required_packages = ['torch', 'transformers', 'datasets', 'peft']
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    logger.error(f"المكتبة المطلوبة غير موجودة: {package}")
                    return False
            
            logger.info("✅ البيئة جاهزة للتدريب")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في فحص البيئة: {e}")
            return False
    
    def prepare_dataset(self, files: List[str], config: Dict[str, Any]) -> Optional[str]:
        """تحضير بيانات التدريب"""
        try:
            dataset_name = f"{config['modelName']}_dataset_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            dataset_path = self.data_dir / f"{dataset_name}.json"
            
            # تحويل الملفات إلى تنسيق LLaMA-Factory
            conversations = []
            
            for file_path in files:
                file_path = Path(file_path)
                if not file_path.exists():
                    logger.warning(f"الملف غير موجود: {file_path}")
                    continue
                
                if file_path.suffix.lower() == '.txt':
                    conversations.extend(self._process_text_file(file_path, config))
                elif file_path.suffix.lower() == '.json':
                    conversations.extend(self._process_json_file(file_path, config))
                elif file_path.suffix.lower() == '.csv':
                    conversations.extend(self._process_csv_file(file_path, config))
            
            if not conversations:
                logger.error("لا توجد بيانات صالحة للتدريب")
                return None
            
            # حفظ البيانات
            with open(dataset_path, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, ensure_ascii=False, indent=2)
            
            # تحديث dataset_info.json
            self._update_dataset_info(dataset_name, dataset_path)
            
            logger.info(f"✅ تم تحضير البيانات: {len(conversations)} محادثة")
            return dataset_name
            
        except Exception as e:
            logger.error(f"خطأ في تحضير البيانات: {e}")
            return None
    
    def _process_text_file(self, file_path: Path, config: Dict[str, Any]) -> List[Dict]:
        """معالجة ملف نصي"""
        conversations = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # تقسيم النص إلى محادثات
            sections = content.split('\n\n')
            for section in sections:
                if len(section.strip()) > 10:  # تجاهل النصوص القصيرة جداً
                    conversation = {
                        "conversations": [
                            {
                                "from": "human",
                                "value": "اشرح لي هذا الموضوع بطريقة مفصلة"
                            },
                            {
                                "from": "gpt",
                                "value": section.strip()
                            }
                        ]
                    }
                    conversations.append(conversation)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الملف النصي {file_path}: {e}")
        
        return conversations
    
    def _process_json_file(self, file_path: Path, config: Dict[str, Any]) -> List[Dict]:
        """معالجة ملف JSON"""
        conversations = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and 'conversations' in item:
                        conversations.append(item)
                    elif isinstance(item, dict) and 'question' in item and 'answer' in item:
                        conversation = {
                            "conversations": [
                                {"from": "human", "value": item['question']},
                                {"from": "gpt", "value": item['answer']}
                            ]
                        }
                        conversations.append(conversation)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة ملف JSON {file_path}: {e}")
        
        return conversations
    
    def _process_csv_file(self, file_path: Path, config: Dict[str, Any]) -> List[Dict]:
        """معالجة ملف CSV"""
        conversations = []
        try:
            import pandas as pd
            df = pd.read_csv(file_path)
            
            # البحث عن أعمدة السؤال والجواب
            question_cols = [col for col in df.columns if 'question' in col.lower() or 'سؤال' in col]
            answer_cols = [col for col in df.columns if 'answer' in col.lower() or 'جواب' in col or 'إجابة' in col]
            
            if question_cols and answer_cols:
                for _, row in df.iterrows():
                    conversation = {
                        "conversations": [
                            {"from": "human", "value": str(row[question_cols[0]])},
                            {"from": "gpt", "value": str(row[answer_cols[0]])}
                        ]
                    }
                    conversations.append(conversation)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة ملف CSV {file_path}: {e}")
        
        return conversations
    
    def _update_dataset_info(self, dataset_name: str, dataset_path: Path):
        """تحديث معلومات البيانات"""
        dataset_info_path = self.llama_factory_dir / "data" / "dataset_info.json"
        
        try:
            # قراءة الملف الحالي
            if dataset_info_path.exists():
                with open(dataset_info_path, 'r', encoding='utf-8') as f:
                    dataset_info = json.load(f)
            else:
                dataset_info = {}
            
            # إضافة البيانات الجديدة
            dataset_info[dataset_name] = {
                "file_name": str(dataset_path.relative_to(self.llama_factory_dir)),
                "formatting": "sharegpt",
                "columns": {
                    "messages": "conversations"
                },
                "tags": {
                    "role_tag": "from",
                    "content_tag": "value",
                    "user_tag": "human",
                    "assistant_tag": "gpt"
                }
            }
            
            # حفظ الملف المحدث
            with open(dataset_info_path, 'w', encoding='utf-8') as f:
                json.dump(dataset_info, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"خطأ في تحديث معلومات البيانات: {e}")
    
    def create_training_config(self, config: Dict[str, Any], dataset_name: str) -> Optional[str]:
        """إنشاء ملف تكوين التدريب"""
        try:
            config_name = f"{config['modelName']}_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            config_path = self.configs_dir / f"{config_name}.yaml"
            
            # تحديد النموذج الأساسي حسب التخصص
            base_models = {
                'general': 'microsoft/DialoGPT-medium',
                'business': 'microsoft/DialoGPT-medium',
                'education': 'microsoft/DialoGPT-medium',
                'creative': 'microsoft/DialoGPT-medium',
                'technical': 'microsoft/DialoGPT-medium',
                'medical': 'microsoft/DialoGPT-medium'
            }
            
            base_model = base_models.get(config['specialization'], 'microsoft/DialoGPT-medium')
            
            training_config = f"""### نموذج التدريب العبقري
model_name_or_path: {base_model}
stage: sft
do_train: true
finetuning_type: lora
lora_target: all

### بيانات التدريب
dataset: {dataset_name}
template: default
cutoff_len: 1024
max_samples: 1000
overwrite_cache: true
preprocessing_num_workers: 16

### معاملات التدريب
per_device_train_batch_size: {config['batchSize']}
gradient_accumulation_steps: 4
lr_scheduler_type: cosine
logging_steps: 10
warmup_steps: 20
save_steps: 100
eval_steps: 50
evaluation_strategy: steps
load_best_model_at_end: true

### معاملات التحسين
learning_rate: {config['learningRate']}
num_train_epochs: {config['epochs']}
max_grad_norm: 1.0
weight_decay: 0.1
adam_beta2: 0.95
warmup_ratio: 0.1
bf16: true
ddp_timeout: 180000000

### LoRA
lora_alpha: 16
lora_dropout: 0.1
lora_rank: 8

### الحفظ
output_dir: training/models/{config['modelName']}
logging_dir: training/logs/{config['modelName']}
save_total_limit: 2
hub_model_id: {config['modelName']}

### التقييم
eval_dataset: {dataset_name}
per_device_eval_batch_size: 1
predict_with_generate: true

### إعدادات خاصة بالشخصية
# الشخصية: {config['personality']}
# التخصص: {config['specialization']}
"""
            
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(training_config)
            
            logger.info(f"✅ تم إنشاء ملف التكوين: {config_path}")
            return str(config_path)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء ملف التكوين: {e}")
            return None
    
    def start_training(self, config_path: str) -> bool:
        """بدء عملية التدريب"""
        try:
            # الانتقال إلى مجلد LLaMA-Factory
            os.chdir(self.llama_factory_dir)
            
            # تشغيل التدريب
            cmd = [
                sys.executable, "-m", "llamafactory.train",
                config_path
            ]
            
            logger.info(f"🚀 بدء التدريب: {' '.join(cmd)}")
            
            # تشغيل العملية
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # قراءة المخرجات
            for line in process.stdout:
                logger.info(line.strip())
            
            process.wait()
            
            if process.returncode == 0:
                logger.info("🎉 تم إكمال التدريب بنجاح!")
                return True
            else:
                logger.error(f"❌ فشل التدريب برمز الخطأ: {process.returncode}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في التدريب: {e}")
            return False
        finally:
            # العودة إلى المجلد الأصلي
            os.chdir(self.base_dir.parent)

def main():
    """الدالة الرئيسية للاختبار"""
    trainer = GeniusTrainer()
    
    if not trainer.check_environment():
        print("❌ البيئة غير جاهزة للتدريب")
        return
    
    # مثال على التدريب
    config = {
        'modelName': 'arabic-genius-test',
        'learningRate': 0.0001,
        'epochs': 3,
        'batchSize': 2,
        'specialization': 'general',
        'personality': 'helpful',
        'language': 'arabic'
    }
    
    # تحضير البيانات (مثال)
    sample_data = [
        {
            "conversations": [
                {"from": "human", "value": "مرحبا، كيف حالك؟"},
                {"from": "gpt", "value": "مرحبا! أنا بخير، شكراً لك. كيف يمكنني مساعدتك اليوم؟"}
            ]
        }
    ]
    
    # حفظ بيانات تجريبية
    test_data_path = trainer.data_dir / "test_data.json"
    with open(test_data_path, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    # تدريب النموذج
    dataset_name = trainer.prepare_dataset([str(test_data_path)], config)
    if dataset_name:
        config_path = trainer.create_training_config(config, dataset_name)
        if config_path:
            trainer.start_training(config_path)

if __name__ == "__main__":
    main()
