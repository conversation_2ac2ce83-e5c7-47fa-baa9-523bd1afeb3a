/**
 * خدمة الذكاء الاصطناعي المتقدمة
 * تجمع جميع الخدمات والمزودين في واجهة موحدة ومتطورة
 */

import { OpenRouterAPI, AIModel, ChatMessage } from './OpenRouterAPI'
import { MstyAPI } from './MstyAPI'
import { OllamaAPI } from './OllamaAPI'
import { promptsLibrary, PromptTemplate } from './PromptsLibrary'
import { emotionalAI } from './EmotionalAI'
import { intelligentEngine } from './IntelligentAIEngine'

export interface ServiceProvider {
  id: string
  name: string
  type: 'local' | 'cloud'
  status: 'connected' | 'disconnected' | 'error'
  models: AIModel[]
  capabilities: string[]
  priority: number
}

export interface ConversationContext {
  id: string
  title: string
  messages: ChatMessage[]
  currentModel: string
  provider: string
  settings: ConversationSettings
  metadata: {
    createdAt: string
    updatedAt: string
    totalTokens: number
    emotionalProfile: any
  }
}

export interface ConversationSettings {
  temperature: number
  maxTokens: number
  systemPrompt?: string
  useEmotionalAI: boolean
  autoSwitchModels: boolean
  preferredProvider: 'local' | 'cloud' | 'auto'
}

export class AdvancedAIService {
  private providers: Map<string, ServiceProvider> = new Map()
  private openRouterAPI: OpenRouterAPI | null = null
  private mstyAPI: MstyAPI
  private ollamaAPI: OllamaAPI
  private isInitialized = false
  private currentConversation: ConversationContext | null = null

  constructor() {
    this.mstyAPI = new MstyAPI()
    this.ollamaAPI = new OllamaAPI('http://localhost:11434')
    this.initializeProviders()
  }

  // تهيئة جميع المزودين
  private async initializeProviders(): Promise<void> {
    console.log('🚀 تهيئة خدمة الذكاء الاصطناعي المتقدمة...')

    // تهيئة المحرك الذكي المدمج (الأولوية الأولى)
    await this.initializeIntelligentEngine()

    // تهيئة المزودين المحليين
    await this.initializeLocalProviders()

    // تهيئة المزودين السحابيين
    await this.initializeCloudProviders()

    this.isInitialized = true
    console.log('✅ تم تهيئة الخدمة بنجاح')
  }

  // تهيئة المحرك الذكي المدمج (كنظام احتياطي فقط)
  private async initializeIntelligentEngine(): Promise<void> {
    try {
      // المحرك الذكي كنظام احتياطي عندما لا تتوفر نماذج حقيقية
      const fallbackModels = [
        {
          id: 'fallback-assistant',
          name: '🤖 مساعد احتياطي',
          description: 'مساعد بسيط عندما لا تتوفر نماذج أخرى',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 4096,
          architecture: { modality: 'text', tokenizer: 'fallback' },
          top_provider: { is_moderated: false }
        }
      ]

      this.providers.set('fallback-engine', {
        id: 'fallback-engine',
        name: '🤖 النظام الاحتياطي',
        type: 'local',
        status: 'connected',
        models: fallbackModels,
        capabilities: ['chat', 'basic-responses'],
        priority: 999 // أقل أولوية - يستخدم فقط عند عدم توفر نماذج أخرى
      })

      console.log(`🤖 النظام الاحتياطي: متاح`)

    } catch (error) {
      console.error('❌ خطأ في تهيئة النظام الاحتياطي:', error)
    }
  }

  // تهيئة المزودين المحليين
  private async initializeLocalProviders(): Promise<void> {
    // Msty Provider
    try {
      const mstyAvailable = await this.mstyAPI.checkAvailability()
      const mstyModels = await this.mstyAPI.getModels()

      this.providers.set('msty', {
        id: 'msty',
        name: 'Msty (محلي)',
        type: 'local',
        status: mstyAvailable ? 'connected' : 'disconnected',
        models: mstyModels,
        capabilities: ['chat', 'streaming', 'local-privacy', 'custom-models'],
        priority: 1
      })

      console.log(`🟣 Msty: ${mstyAvailable ? 'متصل' : 'غير متصل'} (${mstyModels.length} نموذج)`)
    } catch (error) {
      console.error('❌ خطأ في تهيئة Msty:', error)
    }

    // Ollama Provider - النماذج الحقيقية مفتوحة المصدر
    try {
      const ollamaAvailable = await this.ollamaAPI.checkAvailability()
      const ollamaModels = await this.ollamaAPI.getModels()

      // قائمة النماذج المُوصى بها مفتوحة المصدر - محدثة ومحسنة
      const popularOpenSourceModels = [
        {
          id: 'llama3.1:8b',
          name: '🦙 Llama 3.1 8B',
          description: 'الأفضل للاستخدام العام - نموذج Meta المتطور (8B parameters)',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 131072, // 128K context
          architecture: { modality: 'text', tokenizer: 'llama' },
          top_provider: { is_moderated: false },
          downloadCommand: 'ollama pull llama3.1:8b',
          size: '4.7GB',
          available: false,
          category: 'general',
          performance: 'high',
          speed: 'fast',
          memoryRequired: '8GB',
          recommended: true,
          features: ['محادثة عامة', 'تحليل النصوص', 'الإجابة على الأسئلة', 'الكتابة الإبداعية']
        },
        {
          id: 'codellama:13b',
          name: '💻 CodeLlama 13B',
          description: 'متخصص في البرمجة والكود - الأفضل للمطورين',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 16384,
          architecture: { modality: 'text', tokenizer: 'llama' },
          top_provider: { is_moderated: false },
          downloadCommand: 'ollama pull codellama:13b',
          size: '7.3GB',
          available: false,
          category: 'coding',
          performance: 'very_high',
          speed: 'medium',
          memoryRequired: '16GB',
          recommended: true,
          features: ['كتابة الكود', 'شرح البرمجة', 'مراجعة الكود', 'حل المشاكل البرمجية']
        },
        {
          id: 'mistral:7b',
          name: '🌟 Mistral 7B',
          description: 'سريع ومتقدم - نموذج فرنسي عالي الأداء',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 32768,
          architecture: { modality: 'text', tokenizer: 'mistral' },
          top_provider: { is_moderated: false },
          downloadCommand: 'ollama pull mistral:7b',
          size: '4.1GB',
          available: false,
          category: 'general',
          performance: 'high',
          speed: 'very_fast',
          memoryRequired: '8GB',
          recommended: true,
          features: ['استجابة سريعة', 'تحليل متقدم', 'محادثة ذكية', 'كفاءة عالية']
        },
        {
          id: 'gemma2:9b',
          name: '💎 Gemma 2 9B',
          description: 'نموذج Google الجديد - متوازن وذكي',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 8192,
          architecture: { modality: 'text', tokenizer: 'gemma' },
          top_provider: { is_moderated: false },
          downloadCommand: 'ollama pull gemma2:9b',
          size: '5.4GB',
          available: false,
          category: 'general',
          performance: 'high',
          speed: 'fast',
          memoryRequired: '12GB',
          recommended: true,
          features: ['ذكاء متوازن', 'فهم عميق', 'استجابات دقيقة', 'تقنية Google']
        },
        {
          id: 'phi3:mini',
          name: '⚡ Phi-3 Mini',
          description: 'خفيف للأجهزة المحدودة - نموذج Microsoft المحسن',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 128000,
          architecture: { modality: 'text', tokenizer: 'phi' },
          top_provider: { is_moderated: false },
          downloadCommand: 'ollama pull phi3:mini',
          size: '2.3GB',
          available: false,
          category: 'lightweight',
          performance: 'medium',
          speed: 'very_fast',
          memoryRequired: '4GB',
          recommended: true,
          features: ['سريع جداً', 'استهلاك قليل للذاكرة', 'مثالي للأجهزة الضعيفة', 'كفاءة عالية']
        },
        {
          id: 'qwen2.5:7b',
          name: '🐉 Qwen 2.5 7B',
          description: 'متعدد اللغات - نموذج صيني متقدم يدعم العربية بقوة',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 32768,
          architecture: { modality: 'text', tokenizer: 'qwen' },
          top_provider: { is_moderated: false },
          downloadCommand: 'ollama pull qwen2.5:7b',
          size: '4.4GB',
          available: false,
          category: 'multilingual',
          performance: 'high',
          speed: 'fast',
          memoryRequired: '8GB',
          recommended: true,
          features: ['دعم ممتاز للعربية', 'متعدد اللغات', 'فهم ثقافي', 'ترجمة متقدمة']
        },
        {
          id: 'llama3.1:70b',
          name: '🦙 Llama 3.1 70B',
          description: 'للأجهزة القوية - أقوى نموذج مفتوح المصدر متاح',
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          context_length: 131072,
          architecture: { modality: 'text', tokenizer: 'llama' },
          top_provider: { is_moderated: false },
          downloadCommand: 'ollama pull llama3.1:70b',
          size: '40GB',
          available: false,
          category: 'professional',
          performance: 'exceptional',
          speed: 'medium',
          memoryRequired: '64GB',
          recommended: false, // للأجهزة القوية فقط
          features: ['أداء استثنائي', 'فهم عميق جداً', 'قدرات متقدمة', 'للاستخدام الاحترافي'],
          warning: 'يتطلب جهاز قوي جداً مع 64GB RAM على الأقل'
        }
      ]

      // دمج النماذج المتوفرة مع النماذج الشائعة
      const allModels = [...ollamaModels]

      // تحديد النماذج المتوفرة
      popularOpenSourceModels.forEach(popularModel => {
        const existingModel = ollamaModels.find(model => model.id === popularModel.id)
        if (existingModel) {
          // النموذج متوفر - تحديث المعلومات
          existingModel.available = true
          existingModel.downloadCommand = popularModel.downloadCommand
          existingModel.size = popularModel.size
        } else {
          // النموذج غير متوفر - إضافته كخيار للتحميل
          allModels.push(popularModel)
        }
      })

      this.providers.set('ollama', {
        id: 'ollama',
        name: '🦙 Ollama - النماذج مفتوحة المصدر',
        type: 'local',
        status: ollamaAvailable ? 'connected' : 'disconnected',
        models: allModels,
        capabilities: ['chat', 'streaming', 'local-privacy', 'open-source', 'offline-capable'],
        priority: ollamaAvailable ? 1 : 3, // أولوية عالية إذا كان متصل
        downloadInfo: {
          available: true,
          installCommand: 'ollama pull <model-name>',
          modelsPath: typeof window !== 'undefined' ?
            'C:\\Users\\<USER>\\.ollama\\models' :
            '/Users/<USER>/.ollama/models',
          helpUrl: 'https://ollama.ai/'
        }
      })

      if (ollamaAvailable) {
        console.log(`🦙 Ollama: متصل (${ollamaModels.length} نموذج محمل، ${allModels.length} نموذج متاح)`)
        if (ollamaModels.length === 0) {
          console.log('💡 لا توجد نماذج محملة. جرب: ollama pull llama3.1:8b')
        }
      } else {
        console.log('🦙 Ollama: غير متصل - يرجى تثبيت Ollama من https://ollama.ai/')
        console.log('📥 بعد التثبيت، شغل: ollama pull llama3.1:8b لتحميل نموذج')
      }
    } catch (error) {
      console.error('❌ خطأ في تهيئة Ollama:', error)
    }
  }

  // تهيئة المزودين السحابيين
  private async initializeCloudProviders(): Promise<void> {
    // OpenRouter Provider - قراءة المفتاح من الإعدادات المحفوظة
    let apiKey = null

    try {
      // محاولة قراءة الإعدادات المحفوظة
      const savedSettings = localStorage.getItem('ai-chat-settings')
      if (savedSettings) {
        const settings = JSON.parse(savedSettings)
        apiKey = settings.openrouter_api_key
      }

      // إذا لم يوجد في الإعدادات، جرب المفتاح القديم
      if (!apiKey) {
        apiKey = localStorage.getItem('openrouter-api-key')
      }

      // مفتاح افتراضي للاختبار
      if (!apiKey) {
        apiKey = '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'
      }
    } catch (error) {
      console.warn('⚠️ خطأ في قراءة إعدادات OpenRouter:', error)
      apiKey = '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'
    }

    if (apiKey) {
      try {
        console.log('🔑 محاولة الاتصال بـ OpenRouter...')
        this.openRouterAPI = new OpenRouterAPI(apiKey)
        const connectionTest = await this.openRouterAPI.testConnection()
        const cloudModels = await this.openRouterAPI.getAvailableModels()

        this.providers.set('openrouter', {
          id: 'openrouter',
          name: 'OpenRouter (سحابي)',
          type: 'cloud',
          status: connectionTest ? 'connected' : 'disconnected',
          models: cloudModels,
          capabilities: ['chat', 'streaming', 'multiple-models', 'high-quality'],
          priority: connectionTest ? 2 : 4 // أولوية أعلى إذا كان متصل
        })

        if (connectionTest) {
          console.log(`🌐 OpenRouter: متصل بنجاح (${cloudModels.length} نموذج متاح)`)
        } else {
          console.log('🌐 OpenRouter: فشل الاتصال - تحقق من مفتاح API')
        }
      } catch (error) {
        console.error('❌ خطأ في تهيئة OpenRouter:', error)
        // إضافة مزود غير متصل للإشارة إلى المشكلة
        this.providers.set('openrouter', {
          id: 'openrouter',
          name: 'OpenRouter (غير متصل)',
          type: 'cloud',
          status: 'error',
          models: [],
          capabilities: ['chat', 'streaming', 'multiple-models', 'high-quality'],
          priority: 5
        })
      }
    } else {
      console.log('🌐 OpenRouter: لا يوجد مفتاح API - أضف مفتاح في الإعدادات')
    }
  }

  // الحصول على جميع المزودين
  getProviders(): ServiceProvider[] {
    return Array.from(this.providers.values()).sort((a, b) => a.priority - b.priority)
  }

  // الحصول على المزودين المتصلين
  getConnectedProviders(): ServiceProvider[] {
    return this.getProviders().filter(provider => provider.status === 'connected')
  }

  // الحصول على جميع النماذج المتاحة
  getAllAvailableModels(): AIModel[] {
    const allModels: AIModel[] = []

    this.providers.forEach(provider => {
      if (provider.status === 'connected') {
        allModels.push(...provider.models)
      }
    })

    return allModels
  }

  // اختيار أفضل مزود للمهمة
  selectBestProvider(
    task: 'chat' | 'code' | 'creative' | 'analysis',
    preference: 'local' | 'cloud' | 'auto' = 'auto'
  ): ServiceProvider | null {
    const connectedProviders = this.getConnectedProviders()

    if (connectedProviders.length === 0) {
      return null
    }

    // إذا كان التفضيل محدد
    if (preference !== 'auto') {
      const preferredProviders = connectedProviders.filter(p => p.type === preference)
      if (preferredProviders.length > 0) {
        return preferredProviders[0] // أعلى أولوية
      }
    }

    // اختيار تلقائي بناءً على المهمة
    switch (task) {
      case 'code':
        // تفضيل المزودين المحليين للبرمجة (خصوصية)
        return connectedProviders.find(p => p.type === 'local') || connectedProviders[0]

      case 'creative':
        // تفضيل المزودين السحابيين للإبداع (جودة أعلى)
        return connectedProviders.find(p => p.type === 'cloud') || connectedProviders[0]

      case 'analysis':
        // تفضيل المزودين السحابيين للتحليل
        return connectedProviders.find(p => p.type === 'cloud') || connectedProviders[0]

      default:
        // للمحادثة العامة، اختيار أول مزود متاح
        return connectedProviders[0]
    }
  }

  // إرسال رسالة مع اختيار تلقائي للمزود والنموذج
  async sendMessage(
    messagesOrString: ChatMessage[] | string,
    modelOrOptions?: string | {
      model?: string
      provider?: string
      usePromptTemplate?: string
      conversationId?: string
      settings?: Partial<ConversationSettings>
    }
  ): Promise<{
    success: boolean
    message?: string
    response?: string
    provider?: string
    model?: string
    usage?: any
    error?: string
    emotionalAnalysis?: any
  }> {
    try {
      if (!this.isInitialized) {
        await this.initializeProviders()
      }

      // معالجة المعاملات المرنة
      let messages: ChatMessage[]
      let options: any = {}
      let selectedModel: string

      if (Array.isArray(messagesOrString)) {
        // إذا كان المعامل الأول مصفوفة رسائل
        messages = messagesOrString
        if (typeof modelOrOptions === 'string') {
          selectedModel = modelOrOptions
        } else {
          options = modelOrOptions || {}
          selectedModel = options.model || ''
        }
      } else {
        // إذا كان المعامل الأول نص
        const userMessage = messagesOrString
        selectedModel = typeof modelOrOptions === 'string' ? modelOrOptions : (modelOrOptions?.model || '')
        options = typeof modelOrOptions === 'object' ? modelOrOptions : {}

        messages = [{
          role: 'user',
          content: userMessage
        }]
      }

      // الحصول على آخر رسالة للمعالجة
      const lastMessage = messages[messages.length - 1]
      const userMessage = lastMessage?.content || ''

      // معالجة قالب Prompt إذا تم تحديده
      let processedMessage = userMessage
      if (options.usePromptTemplate) {
        const template = promptsLibrary.getPromptById(options.usePromptTemplate)
        if (template) {
          processedMessage = template.prompt + '\n\n' + userMessage
          promptsLibrary.recordUsage(template.id)
          // تحديث آخر رسالة
          messages[messages.length - 1].content = processedMessage
        }
      }

      // تحليل المشاعر
      const emotionalAnalysis = emotionalAI.getFullEmotionalAnalysis(processedMessage, selectedModel)

      // اختيار المزود والنموذج
      const taskType = this.detectTaskType(processedMessage)
      let selectedProvider = options.provider ?
        this.providers.get(options.provider) :
        this.selectBestProvider(taskType, options.settings?.preferredProvider)

      // إذا لم يوجد مزود متصل، استخدم النظام الاحتياطي
      if (!selectedProvider || selectedProvider.status !== 'connected') {
        console.log('⚠️ لا يوجد مزود متصل، استخدام النظام الاحتياطي')
        selectedProvider = {
          id: 'fallback-engine',
          name: 'النظام الاحتياطي',
          type: 'local',
          status: 'connected',
          models: [{ id: 'fallback', name: 'نظام احتياطي', description: 'نظام احتياطي مفيد' }],
          capabilities: ['chat'],
          priority: 10
        }
      }

      console.log('🎯 المزود المختار:', selectedProvider.name)

      // اختيار النموذج إذا لم يكن محدد
      if (!selectedModel) {
        selectedModel = this.selectBestModel(selectedProvider, taskType)
      }

      // إرسال الرسالة حسب المزود
      let result: any
      switch (selectedProvider.id) {
        case 'fallback-engine':
          // النظام الاحتياطي - استجابات بسيطة ومفيدة
          result = await this.generateFallbackResponse(processedMessage, selectedModel)
          break
        case 'msty':
          result = await this.mstyAPI.sendMessage(messages, selectedModel)
          break
        case 'ollama':
          console.log('📤 إرسال إلى Ollama:', selectedModel)
          const lastMessage = messages[messages.length - 1]
          const conversationHistory = messages.slice(0, -1)
          result = await this.ollamaAPI.sendMessage(lastMessage.content, selectedModel, conversationHistory)
          console.log('📋 نتيجة Ollama:', result?.success ? 'نجح' : 'فشل')
          break
        case 'openrouter':
          if (this.openRouterAPI) {
            result = await this.openRouterAPI.sendMessage(processedMessage, selectedModel, messages.slice(0, -1))
          }
          break
        default:
          throw new Error(`مزود غير مدعوم: ${selectedProvider.id}`)
      }

      // معالجة النتيجة
      if (result && (result.success !== false)) {
        let response: string
        let usage: any = null
        let emotionalAnalysisResult: any = null

        // معالجة خاصة للنظام الاحتياطي
        if (selectedProvider.id === 'fallback-engine') {
          response = result.response || result.message || result
          usage = {
            prompt_tokens: processedMessage.length,
            completion_tokens: response.length,
            total_tokens: processedMessage.length + response.length,
            processing_time: result.processingTime || 100
          }
          emotionalAnalysisResult = emotionalAnalysis.emotionalState.confidence > 0.7 ? {
            emotion: emotionalAnalysis.emotionalState.primary,
            intensity: emotionalAnalysis.emotionalState._intensity,
            confidence: emotionalAnalysis.emotionalState.confidence
          } : undefined
        } else {
          // معالجة للمزودين الآخرين
          if (typeof result === 'string') {
            response = result
          } else if (result.message) {
            response = result.message
          } else if (result.response) {
            response = result.response
          } else {
            response = String(result)
          }

          usage = result.usage || {
            prompt_tokens: processedMessage.length,
            completion_tokens: response.length,
            total_tokens: processedMessage.length + response.length
          }

          emotionalAnalysisResult = emotionalAnalysis.emotionalState.confidence > 0.7 ? {
            emotion: emotionalAnalysis.emotionalState.primary,
            intensity: emotionalAnalysis.emotionalState._intensity,
            confidence: emotionalAnalysis.emotionalState.confidence
          } : undefined
        }

        return {
          success: true,
          message: response, // للتوافق مع App.tsx
          response,
          provider: selectedProvider.id,
          model: selectedModel,
          usage,
          emotionalAnalysis: emotionalAnalysisResult,
          confidence: result.confidence || 0.8,
          suggestions: result.suggestions || [],
          metadata: result.metadata || {}
        }
      } else {
        throw new Error(result?.error || 'فشل في الحصول على رد من الذكاء الاصطناعي')
      }

    } catch (error) {
      console.error('❌ خطأ في إرسال الرسالة:', error)
      return {
        success: false,
        error: (error as any).message || 'خطأ غير معروف'
      }
    }
  }

  // تحديد نوع المهمة من الرسالة
  private detectTaskType(message: string): 'chat' | 'code' | 'creative' | 'analysis' {
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('كود') || lowerMessage.includes('برمجة') ||
        lowerMessage.includes('code') || lowerMessage.includes('function')) {
      return 'code'
    }

    if (lowerMessage.includes('قصة') || lowerMessage.includes('شعر') ||
        lowerMessage.includes('إبداع') || lowerMessage.includes('creative')) {
      return 'creative'
    }

    if (lowerMessage.includes('تحليل') || lowerMessage.includes('بيانات') ||
        lowerMessage.includes('analysis') || lowerMessage.includes('data')) {
      return 'analysis'
    }

    return 'chat'
  }

  // اختيار أفضل نموذج للمهمة
  private selectBestModel(provider: ServiceProvider, taskType: string): string {
    const models = provider.models

    if (models.length === 0) {
      throw new Error('لا توجد نماذج متاحة في المزود المحدد')
    }

    // البحث عن نموذج مناسب للمهمة
    switch (taskType) {
      case 'code':
        const codeModel = models.find(m =>
          m.id.includes('code') || m.id.includes('deepseek') || m.name.includes('💻')
        )
        return codeModel?.id || models[0].id

      case 'creative':
        const creativeModel = models.find(m =>
          m.id.includes('literary') || m.id.includes('creative') || m.name.includes('📚')
        )
        return creativeModel?.id || models[0].id

      default:
        // للمحادثة العامة، اختيار أول نموذج متاح
        return models[0].id
    }
  }

  // تحديث إعدادات المزود
  async updateProviderSettings(providerId: string, settings: any): Promise<boolean> {
    try {
      switch (providerId) {
        case 'openrouter':
          if (settings.apiKey && this.openRouterAPI) {
            this.openRouterAPI.updateApiKey(settings.apiKey)
            localStorage.setItem('openrouter-api-key', settings.apiKey)
          }
          break
        case 'msty':
          if (settings.baseURL) {
            this.mstyAPI.updateConnection(settings.baseURL, settings.apiKey)
          }
          break
      }

      // إعادة تهيئة المزود
      await this.initializeProviders()
      return true
    } catch (error) {
      console.error('❌ خطأ في تحديث إعدادات المزود:', error)
      return false
    }
  }



  // النظام الاحتياطي - استجابات مفيدة عندما لا تتوفر نماذج حقيقية
  private async generateFallbackResponse(message: string, model: string): Promise<any> {
    const startTime = Date.now()

    // تحليل الرسالة لتقديم استجابة مناسبة
    const lowerMessage = message.toLowerCase()
    let response = ''

    // استجابات للتحية
    if (lowerMessage.includes('مرحبا') || lowerMessage.includes('السلام') || lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      response = `مرحباً بك! 👋

أنا مساعدك الذكي، وأعمل حالياً في الوضع الاحتياطي لأنه لا توجد نماذج ذكاء اصطناعي متاحة.

🔧 **لتفعيل النماذج الحقيقية:**
• **Ollama**: حمل من https://ollama.ai/ ثم شغل \`ollama pull llama3.1:8b\`
• **OpenRouter**: احصل على مفتاح API من https://openrouter.ai/
• **Msty**: حمل من https://msty.app/

💡 **نماذج مُوصى بها:**
• 🦙 Llama 3.1 8B - للمحادثة العامة
• 💻 CodeLlama 13B - للبرمجة
• 🌟 Mistral 7B - سريع ومتقدم

كيف يمكنني مساعدتك اليوم؟`
    }
    // استجابات للبرمجة
    else if (lowerMessage.includes('برمجة') || lowerMessage.includes('كود') || lowerMessage.includes('programming') || lowerMessage.includes('code')) {
      response = `💻 **مرحباً بك في عالم البرمجة!**

أفهم أنك تسأل عن البرمجة. للحصول على أفضل مساعدة في البرمجة، أنصحك بتفعيل نموذج متخصص:

🎯 **أفضل النماذج للبرمجة:**
• **CodeLlama 13B** - متخصص في الكود
• **Llama 3.1 8B** - ممتاز للشرح والتعليم
• **Qwen 2.5 7B** - يدعم لغات برمجة متعددة

📥 **للتفعيل:**
\`\`\`bash
# تثبيت Ollama
ollama pull codellama:13b
# أو
ollama pull llama3.1:8b
\`\`\`

💡 **نصائح عامة للبرمجة:**
• ابدأ بالأساسيات وتدرج
• مارس البرمجة يومياً
• اقرأ كود الآخرين
• لا تخف من الأخطاء - هي جزء من التعلم

ما اللغة البرمجية التي تهتم بها؟`
    }
    // استجابات للمساعدة
    else if (lowerMessage.includes('مساعدة') || lowerMessage.includes('help') || lowerMessage.includes('كيف')) {
      response = `🆘 **مساعدة سريعة**

أعمل حالياً في الوضع الاحتياطي. إليك كيفية تفعيل النماذج الحقيقية:

🦙 **Ollama (مُوصى به للمبتدئين):**
1. حمل من: https://ollama.ai/
2. ثبت التطبيق
3. شغل: \`ollama pull llama3.1:8b\`
4. أعد تشغيل هذا التطبيق

🌐 **OpenRouter (للنماذج المتقدمة):**
1. اذهب إلى: https://openrouter.ai/
2. أنشئ حساب واحصل على مفتاح API
3. أدخل المفتاح في إعدادات التطبيق

🟣 **Msty (واجهة متقدمة):**
1. حمل من: https://msty.app/
2. ثبت وشغل التطبيق
3. حمل نموذج من المكتبة

📊 **حالة النظام الحالية:**
• النماذج المحلية: غير متاحة
• النماذج السحابية: تحتاج مفتاح API
• النظام الاحتياطي: نشط

هل تحتاج مساعدة في خطوة معينة؟`
    }
    // استجابة عامة
    else {
      response = `شكراً لرسالتك! 📝

أعمل حالياً في الوضع الاحتياطي لأنه لا توجد نماذج ذكاء اصطناعي متاحة.

🚀 **للحصول على استجابات ذكية حقيقية:**

**الخيار الأول - Ollama (مجاني ومحلي):**
\`\`\`bash
# تحميل وتثبيت
curl -fsSL https://ollama.ai/install.sh | sh
# أو حمل من الموقع: https://ollama.ai/

# تحميل نموذج
ollama pull llama3.1:8b
\`\`\`

**الخيار الثاني - OpenRouter (سحابي):**
• احصل على مفتاح API من https://openrouter.ai/
• أدخله في إعدادات التطبيق ⚙️

**النماذج المُوصى بها:**
• 🦙 **Llama 3.1 8B** - للمحادثة العامة (4.7GB)
• 💻 **CodeLlama 13B** - للبرمجة (7.3GB)
• ⚡ **Phi-3 Mini** - سريع وخفيف (2.3GB)

بمجرد تفعيل أي نموذج، ستحصل على استجابات ذكية ومتقدمة!

هل تريد مساعدة في تثبيت Ollama؟`
    }

    const processingTime = Date.now() - startTime

    return {
      success: true,
      response,
      processingTime,
      confidence: 0.8,
      suggestions: [
        'كيف أثبت Ollama؟',
        'ما أفضل النماذج للبرمجة؟',
        'كيف أحصل على مفتاح OpenRouter؟'
      ]
    }
  }

  // إعادة تهيئة الخدمة (مفيد عند تحديث الإعدادات)
  async reinitialize(): Promise<void> {
    console.log('🔄 إعادة تهيئة جميع الخدمات...')

    // إعادة تعيين الحالة
    this.isInitialized = false
    this.providers.clear()
    this.openRouterAPI = null

    // إعادة التهيئة
    await this.initializeProviders()

    console.log('✅ تم إعادة تهيئة الخدمات بنجاح')
  }

  // الحصول على إحصائيات الخدمة
  getServiceStats(): {
    totalProviders: number
    connectedProviders: number
    totalModels: number
    availableModels: number
    isInitialized: boolean
  } {
    const providers = this.getProviders()
    const connectedProviders = this.getConnectedProviders()
    const allModels = this.getAllAvailableModels()
    const availableModels = allModels.filter(model => model.available !== false)

    return {
      totalProviders: providers.length,
      connectedProviders: connectedProviders.length,
      totalModels: allModels.length,
      availableModels: availableModels.length,
      isInitialized: this.isInitialized
    }
  }
}

// إنشاء مثيل مشترك
export const advancedAI = new AdvancedAIService()
