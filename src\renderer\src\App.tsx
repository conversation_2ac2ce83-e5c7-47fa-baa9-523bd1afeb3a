import React, { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import Sidebar from './components/Sidebar'
import SettingsPanel from './components/SettingsPanel'
// 🔥 إزالة استيراد LoadingScreen نهائياً
import { Conversation } from './types'
import './App.css'

interface AppState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  isLoading: boolean
  showSettings: boolean
  settings: any
  availableModels: any[]
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    conversations: [],
    currentConversation: null,
    isLoading: false, // 🔥 إزالة شاشة التحميل نهائياً
    showSettings: false,
    settings: { theme: 'dark', language: 'ar' },
    availableModels: [
      { id: 'local-model', name: 'النموذج المحلي', provider: 'local' }
    ]
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    console.log('🚀 بدء تهيئة التطبيق...')

    // تطبيق الثيم الافتراضي فوراً
    document.documentElement.setAttribute('data-theme', 'dark')

    console.log('✅ تم إنجاز التهيئة بنجاح - التطبيق جاهز!')
  }

  // إنشاء محادثة جديدة
  const createNewConversation = async (title?: string) => {
    console.log('🆕 إنشاء محادثة جديدة...')

    try {
      const result = await window.electronAPI.createConversation(
        title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`
      )

      if (result.success) {
        setState(prev => ({
          ...prev,
          conversations: [result.conversation, ...prev.conversations],
          currentConversation: result.conversation
        }))
        console.log('✅ تم إنشاء محادثة جديدة:', result.conversation.title)
      } else {
        console.error('❌ خطأ في إنشاء المحادثة:', result.error)
      }
    } catch (error) {
      console.error('❌ خطأ في إنشاء المحادثة:', error)
    }
  }

  // تحديد المحادثة الحالية (مبسط)
  const selectConversation = async (conversationId: string) => {
    const conversation = state.conversations.find(c => c.id === conversationId)
    if (conversation) {
      setState(prev => ({ ...prev, currentConversation: conversation }))
      console.log('✅ تم تحديد المحادثة:', conversation.title)
    }
  }

  // حذف محادثة (مبسط)
  const deleteConversation = async (conversationId: string) => {
    setState(prev => {
      const updatedConversations = prev.conversations.filter(c => c.id !== conversationId)
      const newCurrent = prev.currentConversation?.id === conversationId
        ? updatedConversations[0] || null
        : prev.currentConversation

      return {
        ...prev,
        conversations: updatedConversations,
        currentConversation: newCurrent
      }
    })
    console.log('🗑️ تم حذف المحادثة')
  }

  // إرسال رسالة للذكاء الاصطناعي
  const sendMessage = async (message: string, model: string) => {
    console.log('📤 إرسال رسالة:', { message, model })

    // إنشاء محادثة جديدة إذا لم تكن موجودة
    if (!state.currentConversation) {
      await createNewConversation()
    }

    if (!state.currentConversation) {
      console.error('❌ لا توجد محادثة حالية')
      return { success: false, error: 'لا توجد محادثة حالية' }
    }

    try {
      // إرسال الرسالة للخدمة الحقيقية
      const result = await window.electronAPI.sendMessage({
        message,
        model: model || 'app/chat-general',
        conversationId: state.currentConversation.id
      })

      if (result.success) {
        // تحديث المحادثة بالنتيجة الجديدة
        setState(prev => ({
          ...prev,
          currentConversation: result.conversation,
          conversations: prev.conversations.map(conv =>
            conv.id === result.conversation.id ? result.conversation : conv
          )
        }))

        console.log('✅ تم إرسال الرسالة بنجاح باستخدام:', result.modelUsed)
        return { success: true }
      } else {
        console.error('❌ خطأ في إرسال الرسالة:', result.error)
        return { success: false, error: result.error }
      }
    } catch (error) {
      console.error('❌ خطأ في إرسال الرسالة:', error)
      return { success: false, error: error.message }
    }
  }

  // حفظ الإعدادات (مبسط)
  const saveSettings = async (newSettings: any) => {
    setState(prev => ({ ...prev, settings: { ...prev.settings, ...newSettings } }))

    // تطبيق السمة الجديدة
    if (newSettings.theme) {
      document.documentElement.setAttribute('data-theme', newSettings.theme)
    }

    console.log('⚙️ تم حفظ الإعدادات:', newSettings)
    return { success: true }
  }

  // تصدير محادثة (مبسط)
  const exportConversation = async (conversationId: string, format: string) => {
    console.log('📤 تصدير المحادثة:', conversationId, format)
    // سيتم تطبيق التصدير الحقيقي لاحقاً
    return { success: true, message: 'سيتم تطبيق التصدير قريباً' }
  }

  // 🔥 إزالة شاشة التحميل نهائياً - الدخول مباشرة للواجهة الرئيسية

  return (
    <div className="app">
      <div className="app-container">
        {/* الشريط الجانبي */}
        <Sidebar
          conversations={state.conversations}
          currentConversation={state.currentConversation}
          onSelectConversation={selectConversation}
          onCreateConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onExportConversation={exportConversation}
          onShowSettings={() => setState(prev => ({ ...prev, showSettings: true }))}
        />

        {/* واجهة الدردشة الرئيسية */}
        <div className="main-content">
          <ChatInterface
            conversation={state.currentConversation}
            availableModels={state.availableModels}
            settings={state.settings}
            onSendMessage={sendMessage}
            onCreateConversation={createNewConversation}
          />
        </div>

        {/* لوحة الإعدادات */}
        {state.showSettings && (
          <SettingsPanel
            settings={state.settings}
            availableModels={state.availableModels}
            onSaveSettings={saveSettings}
            onClose={() => setState(prev => ({ ...prev, showSettings: false }))}
          />
        )}
      </div>
    </div>
  )
}

export default App
