import React, { useState, useEffect } from 'react'
import ChatInterface from './components/ChatInterface'
import Sidebar from './components/Sidebar'
import SettingsPanel from './components/SettingsPanel'
import { Conversation } from './types'
import { advancedAI } from '../../services/AdvancedAIService'
import './App.css'

interface AppState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  isLoading: boolean
  showSettings: boolean
  settings: any
  availableModels: any[]
  connectedProviders: any[]
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    conversations: [],
    currentConversation: null,
    isLoading: true,
    showSettings: false,
    settings: { theme: 'dark', language: 'ar' },
    availableModels: [],
    connectedProviders: []
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    console.log('🚀 بدء تهيئة التطبيق المتقدم...')

    try {
      // تحميل الإعدادات المحفوظة
      const savedSettings = localStorage.getItem('ai-chat-settings')
      let settings = { theme: 'dark', language: 'ar' }

      if (savedSettings) {
        try {
          settings = { ...settings, ...JSON.parse(savedSettings) }
          console.log('📋 تم تحميل الإعدادات المحفوظة:', settings)
        } catch (error) {
          console.warn('⚠️ خطأ في تحميل الإعدادات المحفوظة:', error)
        }
      }

      // تطبيق الثيم
      document.documentElement.setAttribute('data-theme', settings.theme)

      // تهيئة الخدمة المتقدمة
      await advancedAI.reinitialize()

      // تحديث النماذج والمزودين المتاحين
      const availableModels = advancedAI.getAllAvailableModels()
      const connectedProviders = advancedAI.getConnectedProviders()
      const serviceStats = advancedAI.getServiceStats()

      setState(prev => ({
        ...prev,
        settings,
        availableModels,
        connectedProviders,
        isLoading: false
      }))

      console.log('✅ تم تهيئة التطبيق بنجاح!')
      console.log('📊 الإحصائيات:', serviceStats.connectedProviders, 'مزود متصل،', serviceStats.totalModels, 'نموذج متاح')
    } catch (error) {
      console.error('❌ خطأ في تهيئة التطبيق:', error)
      // تطبيق الإعدادات الافتراضية في حالة الخطأ
      document.documentElement.setAttribute('data-theme', 'dark')
      setState(prev => ({ ...prev, isLoading: false }))
    }
  }

  // إنشاء محادثة جديدة (مبسط)
  const createNewConversation = async (title?: string) => {
    console.log('🆕 إنشاء محادثة جديدة...')

    const newConversation = {
      id: Date.now().toString(),
      title: title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`,
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    setState(prev => ({
      ...prev,
      conversations: [newConversation, ...prev.conversations],
      currentConversation: newConversation
    }))

    console.log('✅ تم إنشاء محادثة جديدة:', newConversation.title)
  }

  // تحديد المحادثة الحالية (مبسط)
  const selectConversation = async (conversationId: string) => {
    const conversation = state.conversations.find(c => c.id === conversationId)
    if (conversation) {
      setState(prev => ({ ...prev, currentConversation: conversation }))
      console.log('✅ تم تحديد المحادثة:', conversation.title)
    }
  }

  // حذف محادثة (مبسط)
  const deleteConversation = async (conversationId: string) => {
    setState(prev => {
      const updatedConversations = prev.conversations.filter(c => c.id !== conversationId)
      const newCurrent = prev.currentConversation?.id === conversationId
        ? updatedConversations[0] || null
        : prev.currentConversation

      return {
        ...prev,
        conversations: updatedConversations,
        currentConversation: newCurrent
      }
    })
    console.log('🗑️ تم حذف المحادثة')
  }

  // إرسال رسالة متقدمة
  const sendMessage = async (message: string, model: string) => {
    console.log('📤 إرسال رسالة متقدمة:', message)

    // إنشاء محادثة جديدة إذا لم تكن موجودة
    if (!state.currentConversation) {
      await createNewConversation()
    }

    try {
      // إنشاء رسالة المستخدم
      const userMessage = {
        id: Date.now().toString(),
        role: 'user' as const,
        content: message,
        timestamp: new Date().toISOString()
      }

      // إضافة رسالة المستخدم فوراً
      if (state.currentConversation) {
        const tempConversation = {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, userMessage],
          updatedAt: new Date().toISOString()
        }

        setState(prev => ({
          ...prev,
          currentConversation: tempConversation,
          conversations: prev.conversations.map(conv =>
            conv.id === tempConversation.id ? tempConversation : conv
          )
        }))
      }

      // إرسال الرسالة للخدمة المتقدمة
      const messages = state.currentConversation?.messages || []
      const allMessages = [...messages, userMessage]

      console.log('🤖 إرسال إلى الخدمة المتقدمة...')
      const response = await advancedAI.sendMessage(allMessages, model)

      let botContent = ''
      if (response.success) {
        botContent = response.message || response.response || 'تم الرد بنجاح'
        console.log('✅ تم الرد بنجاح من', response.provider || 'الخدمة المتقدمة', 'باستخدام', model)
      } else {
        botContent = `عذراً، حدث خطأ في الاتصال: ${response.error || 'خطأ غير معروف'}`
        console.error('❌ خطأ في الخدمة:', response.error)
      }

      // إنشاء رد البوت
      const botMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant' as const,
        content: botContent,
        timestamp: new Date().toISOString()
      }

      // تحديث المحادثة الحالية مع رد البوت
      if (state.currentConversation) {
        const updatedConversation = {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, userMessage, botMessage],
          updatedAt: new Date().toISOString()
        }

        setState(prev => ({
          ...prev,
          currentConversation: updatedConversation,
          conversations: prev.conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedConversation : conv
          )
        }))
      }

      return { success: true }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      return { success: false, error: error.message }
    }
  }

  // حفظ الإعدادات (محسن)
  const saveSettings = async (newSettings: any) => {
    try {
      // دمج الإعدادات الجديدة مع الحالية
      const updatedSettings = { ...state.settings, ...newSettings }

      // تحديث الحالة
      setState(prev => ({ ...prev, settings: updatedSettings }))

      // حفظ في localStorage
      localStorage.setItem('ai-chat-settings', JSON.stringify(updatedSettings))

      // تطبيق السمة الجديدة
      if (newSettings.theme) {
        document.documentElement.setAttribute('data-theme', newSettings.theme)
      }

      // إعادة تهيئة الخدمات إذا تم تحديث مفتاح OpenRouter
      if (newSettings.openrouter_api_key) {
        console.log('🔄 إعادة تهيئة الخدمات مع مفتاح OpenRouter الجديد...')
        await advancedAI.reinitialize()

        // تحديث النماذج المتاحة
        const availableModels = advancedAI.getAllAvailableModels()
        const connectedProviders = advancedAI.getConnectedProviders()

        setState(prev => ({
          ...prev,
          availableModels,
          connectedProviders,
          settings: updatedSettings
        }))

        console.log('✅ تم تحديث الخدمات بنجاح')
        console.log('📊 النماذج المتاحة:', availableModels.length)
        console.log('🔗 المزودين المتصلين:', connectedProviders.length)
      }

      console.log('⚙️ تم حفظ الإعدادات:', updatedSettings)
      return { success: true }
    } catch (error) {
      console.error('❌ خطأ في حفظ الإعدادات:', error)
      return { success: false, error: error.message }
    }
  }

  // تصدير محادثة (مبسط)
  const exportConversation = async (conversationId: string, format: string) => {
    console.log('📤 تصدير المحادثة:', conversationId, format)
    // سيتم تطبيق التصدير الحقيقي لاحقاً
    return { success: true, message: 'سيتم تطبيق التصدير قريباً' }
  }

  // شاشة التحميل البسيطة
  if (state.isLoading) {
    return (
      <div className="app loading-screen">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <h2>🚀 تهيئة التطبيق المتقدم...</h2>
          <p>جاري تحميل النماذج والخدمات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="app">
      <div className="app-container">
        {/* الشريط الجانبي */}
        <Sidebar
          conversations={state.conversations}
          currentConversation={state.currentConversation}
          onSelectConversation={selectConversation}
          onCreateConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onExportConversation={exportConversation}
          onShowSettings={() => setState(prev => ({ ...prev, showSettings: true }))}
        />

        {/* واجهة الدردشة الرئيسية */}
        <div className="main-content">
          <ChatInterface
            conversation={state.currentConversation}
            availableModels={state.availableModels}
            settings={state.settings}
            onSendMessage={sendMessage}
            onCreateConversation={createNewConversation}
          />
        </div>

        {/* لوحة الإعدادات */}
        {state.showSettings && (
          <SettingsPanel
            settings={state.settings}
            availableModels={state.availableModels}
            onSaveSettings={saveSettings}
            onClose={() => setState(prev => ({ ...prev, showSettings: false }))}
          />
        )}
      </div>
    </div>
  )
}

export default App
