const axios = require('axios'); 
 
// اختبار الاتصال بـ Ollama 
async function testOllama() { 
  try { 
    console.log('جاري اختبار الاتصال بـ Ollama...'); 
    const response = await axios.get('http://localhost:11434/v1/models', { timeout: 5000 }); 
    if (response.status === 200) { 
      console.log('\x1b[32m[✓] تم الاتصال بـ Ollama بنجاح\x1b[0m'); 
      console.log('النماذج المتاحة:'); 
        response.data.models.forEach(model =
          console.log(`  - ${model.name}`); 
        }); 
      } else { 
        console.log('  لا توجد نماذج متاحة. يرجى تنزيل النماذج باستخدام أمر: ollama pull llama2'); 
      } 
    } 
    return true; 
  } catch (error) { 
    console.log('\x1b[31m[✗] فشل الاتصال بـ Ollama\x1b[0m'); 
    console.log(`  السبب: ${error.message}`); 
    console.log('  تأكد من تشغيل Ollama على جهازك وأنه يعمل على المنفذ 11434'); 
    return false; 
  } 
} 
 
// اختبار الاتصال بـ OpenRouter 
async function testOpenRouter(apiKey) { 
  if (!apiKey) { 
    console.log('\x1b[33m[!] لم يتم توفير مفتاح API لـ OpenRouter\x1b[0m'); 
    console.log('  يمكنك الحصول على مفتاح API من https://openrouter.ai'); 
    return false; 
  } 
 
  try { 
    console.log('جاري اختبار الاتصال بـ OpenRouter...'); 
    const response = await axios.get('https://openrouter.ai/api/v1/models', { 
      headers: { 
        'Authorization': `Bearer ${apiKey}`, 
        'HTTP-Referer': 'https://ai-chat-bot.local', 
        'X-Title': 'AI Chat Bot' 
      }, 
      timeout: 10000 
    }); 
 
    if (response.status === 200) { 
      console.log('\x1b[32m[✓] تم الاتصال بـ OpenRouter بنجاح\x1b[0m'); 
      console.log('النماذج المتاحة (عينة):'); 
