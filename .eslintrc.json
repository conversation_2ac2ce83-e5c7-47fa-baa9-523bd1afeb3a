{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks", "@typescript-eslint", "react-refresh"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "react-refresh/only-export-components": "warn", "no-console": "warn", "prefer-const": "error", "no-var": "error"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["dist/", "build/", "node_modules/", "*.js"]}