import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Copy, Download, RotateCcw } from 'lucide-react';
// import ReactMarkdown from 'react-markdown'
// import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
// import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { Conversation, ChatMessage, AIModel, AppSettings } from '../types';
import ModelSelector from './ModelSelector';
import MessageInput from './MessageInput';
import EmotionalIndicator from './EmotionalIndicator';
import './ChatInterface.css';
import { writeFile } from 'fs/promises';

interface ChatInterfaceProps {
  conversation: Conversation | null;
  availableModels: AIModel[];
  settings: AppSettings;
  onSendMessage: (message: string, model: string) => Promise<any>;
  onCreateConversation: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversation,
  availableModels,
  settings,
  onSendMessage,
  onCreateConversation
}) => {
  // تنظيف HTML
  const escapeHtml = (text: string): string => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  const exportConversation = async (conversation: any, filePath: string, format: string): Promise<void> => {
    switch (format.toLowerCase()) {
      case 'txt':
        // await this.exportAsText(conversation, filePath);
        break;
      case 'pdf':
        // await this.exportAsPDF(conversation, filePath);
        break;
      case 'json':
        // await this.exportAsJSON(conversation, filePath);
        break;
      case 'html':
        // await this.exportAsHTML(conversation, filePath);
        break;
      default:
        throw new Error(`تنسيق غير مدعوم: ${format}`);
    }
  }

  const [selectedModel, setSelectedModel] = useState(
    settings.default_model || 'meta-llama/llama-3.3-8b-instruct:free'
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emotionalState, setEmotionalState] = useState<any>(null);
  const [showEmotionalIndicator, setShowEmotionalIndicator] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // التمرير إلى أسفل عند إضافة رسائل جديدة
  useEffect(() => {
    scrollToBottom();
  }, [conversation?.messages]);

  // تحديث النموذج المحدد عند تغيير الإعدادات
  useEffect(() => {
    if (settings.default_model) {
      setSelectedModel(settings.default_model);
    }
  }, [settings.default_model]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }

  const handleSendMessage = async (message: string) => {
    if (!message.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await onSendMessage(message, selectedModel);
      
      // إذا كان الرد من النظام (بدون خدمات متاحة)
      if (result.modelUsed === 'system') {
        setError(result.response);
        // عرض رسالة النظام في الواجهة
        setEmotionalState({
          primary: 'neutral',
          secondary: [],
          _intensity: 0.5,
          confidence: 1,
          suggestions: [
            'تأكد من تشغيل LM Studio أو Msty',
            'تحقق من إعدادات OpenRouter',
            'تأكد من اتصال الإنترنت'
          ]
        });
        setShowEmotionalIndicator(true);
        return;
      }

      if (!result.success) {
        setError(result.error || 'حدث خطأ أثناء إرسال الرسالة');
      } else {
        // تحديث الحالة العاطفية وعرض المؤشر
        if (result.emotionalAnalysis) {
          setEmotionalState(result.emotionalAnalysis);
          setShowEmotionalIndicator(true);
        }
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
      setError('حدث خطأ غير متوقع - تأكد من تشغيل الخدمات المطلوبة');
      
      // عرض مؤشر الحالة في حالة الخطأ
      setEmotionalState({
        primary: 'anxiety',
        secondary: ['neutral'],
        _intensity: 0.7,
        confidence: 1,
        suggestions: [
          'تأكد من تشغيل خدمات الذكاء الاصطناعي',
          'تحقق من الإعدادات والاتصال',
          'حاول مرة أخرى بعد قليل'
        ]
      });
      setShowEmotionalIndicator(true);
    } finally {
      setIsLoading(false);
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // يمكن إضافة إشعار نجح النسخ هنا
    } catch (error) {
      console.error('فشل في نسخ النص:', error);
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: '2-digit'
    });
  }

  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user';

    return (
      <div
        key={message.id}
        className={`message ${isUser ? 'message-user' : 'message-assistant'} fade-in`}
      >
        <div className="message-avatar">
          {isUser ? (
            <User size={20} />
          ) : (
            <Bot size={20} />
          )}
        </div>

        <div className="message-content">
          <div className="message-header">
            <span className="message-role">
              {isUser ? 'أنت' : 'المساعد الذكي'}
            </span>
            {settings.show_timestamps && (
              <span className="message-timestamp">
                {formatTimestamp(message.timestamp)}
              </span>
            )}
          </div>

          <div className="message-text">
            <p style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
              {escapeHtml(message.content)}
            </p>
          </div>

          <div className="message-actions">
            <button
              className="btn-icon"
              onClick={() => copyToClipboard(message.content)}
              title="نسخ النص"
            >
              <Copy size={16} />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="chat-interface">
      {/* المؤشر العاطفي */}
      <EmotionalIndicator
        emotionalState={emotionalState}
        isVisible={showEmotionalIndicator}
        onClose={() => setShowEmotionalIndicator(false)}
      />

      {/* شريط الأدوات العلوي */}
      <div className="chat-header">
        <div className="chat-title">
          <h2>{conversation?.title || 'مرحباً بك في بوت الدردشة الذكي'}</h2>
          {conversation && (
            <span className="message-count">
              {conversation.messages.length} رسالة
            </span>
          )}
        </div>

        <div className="chat-controls">
          <ModelSelector
            models={availableModels}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
            servicesStatus={{
              lmStudio: {
                available: true,
                modelsCount: 3,
                status: 'متصل'
              },
              msty: {
                available: true,
                modelsCount: 5,
                status: 'متصل'
              },
              openRouter: {
                available: true,
                hasApiKey: true,
                status: 'متصل'
              }
            }}
          />

          <button
            className="btn btn-outline btn-sm"
            onClick={onCreateConversation}
            title="محادثة جديدة"
          >
            <RotateCcw size={16} />
            محادثة جديدة
          </button>
        </div>
      </div>

      {/* منطقة الرسائل */}
      <div className="messages-container" ref={messagesContainerRef}>
        {conversation?.messages.length === 0 ? (
          <div className="welcome-message">
            <div className="welcome-content">
              <Bot size={48} className="welcome-icon" />
              <h3>مرحباً بك في مساعد الكتابة الإبداعية</h3>
              <p>
                يمكنك الآن بدء رحلتك الإبداعية مع الذكاء الاصطناعي. اختر النموذج المناسب لاحتياجاتك واكتب رسالتك أدناه.
              </p>
              
              <div className="creative-templates">
                <h4>قوالب للبدء السريع:</h4>
                <div className="templates-grid">
                  <button className="template-button" onClick={() => handleSendMessage("ساعدني في كتابة بداية رواية عن شخصية تكتشف قدرات خارقة")}>
                    <span className="template-icon">📚</span>
                    <span>بداية رواية</span>
                  </button>
                  
                  <button className="template-button" onClick={() => handleSendMessage("ساعدني في كتابة قصيدة عن الأمل والتفاؤل")}>
                    <span className="template-icon">🎵</span>
                    <span>قصيدة شعرية</span>
                  </button>
                  
                  <button className="template-button" onClick={() => handleSendMessage("ساعدني في تطوير شخصية رئيسية لرواية. أريدها شخصية معقدة وعميقة")}>
                    <span className="template-icon">👤</span>
                    <span>تطوير شخصية</span>
                  </button>
                  
                  <button className="template-button" onClick={() => handleSendMessage("ساعدني في كتابة حوار درامي بين شخصيتين متصارعتين")}>
                    <span className="template-icon">🎭</span>
                    <span>حوار درامي</span>
                  </button>
                  
                  <button className="template-button" onClick={() => handleSendMessage("اقترح علي حبكة مشوقة لرواية خيال علمي")}>
                    <span className="template-icon">🌌</span>
                    <span>حبكة قصة</span>
                  </button>
                  
                  <button className="template-button" onClick={() => handleSendMessage("ساعدني في وصف مشهد طبيعي بأسلوب أدبي جميل")}>
                    <span className="template-icon">🏞️</span>
                    <span>وصف مشهد</span>
                  </button>
                </div>
              </div>
              
              <div className="welcome-features">
                <div className="feature">
                  <span>📚</span>
                  <span>نماذج متخصصة للروايات والشعر</span>
                </div>
                <div className="feature">
                  <span>🎭</span>
                  <span>مساعدة في كتابة الحوار والسيناريو</span>
                </div>
                <div className="feature">
                  <span>🧠</span>
                  <span>تحليل عاطفي ذكي للنصوص</span>
                </div>
                <div className="feature">
                  <span>🔍</span>
                  <span>اقتراحات إبداعية متنوعة</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="messages-list">
            {conversation?.messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </div>
        )}

        {isLoading && (
          <div className="typing-indicator">
            <div className="typing-avatar">
              <Bot size={20} />
            </div>
            <div className="typing-content">
              <div className="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span className="typing-text">المساعد يكتب...</span>
            </div>
          </div>
        )}
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="error-message">
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      {/* منطقة إدخال الرسائل */}
      <div className="chat-input-container">
        <MessageInput
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
          placeholder="اكتب رسالتك هنا..."
        />
      </div>
    </div>
  );
}

export default ChatInterface;
