@echo off
chcp 65001 >nul
echo ========================================
echo      إنشاء اختصار سطح المكتب
echo ========================================
echo.

:: الحصول على مسار سطح المكتب
set "DESKTOP=%USERPROFILE%\Desktop"
set "APP_PATH=%CD%"
set "SHORTCUT_NAME=AI Chat Bot - بوت الدردشة الذكي"

echo 📁 مسار التطبيق: %APP_PATH%
echo 🖥️ مسار سطح المكتب: %DESKTOP%
echo.

:: إنشاء ملف VBS لإنشاء الاختصار
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\%SHORTCUT_NAME%.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "cmd.exe" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Arguments = "/c cd /d ""%APP_PATH%"" && npm start" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%APP_PATH%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "AI Chat Bot - بوت الدردشة الذكي المتطور" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WindowStyle = 7 >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"

:: تشغيل ملف VBS
cscript "%TEMP%\CreateShortcut.vbs" >nul

:: حذف ملف VBS المؤقت
del "%TEMP%\CreateShortcut.vbs" >nul

:: التحقق من نجاح الإنشاء
if exist "%DESKTOP%\%SHORTCUT_NAME%.lnk" (
    echo ✅ تم إنشاء الاختصار بنجاح!
    echo.
    echo 🎉 يمكنك الآن العثور على الاختصار في:
    echo    "%DESKTOP%\%SHORTCUT_NAME%.lnk"
    echo.
    echo 💡 انقر نقراً مزدوجاً على الاختصار لتشغيل التطبيق
) else (
    echo ❌ فشل في إنشاء الاختصار
    echo 💡 جرب تشغيل هذا الملف كمسؤول
)

echo.
echo ========================================
echo      اكتمل إنشاء الاختصار!
echo ========================================
pause
