import React from 'react'
import { <PERSON><PERSON>, Loader } from 'lucide-react'
import './LoadingScreen.css'

const LoadingScreen: React.FC = () => {
  return (
    <div className="loading-screen">
      <div className="loading-content">
        {/* شعار التطبيق */}
        <div className="app-logo-large">
          <Bot size={64} className="logo-icon" />
          <h1 className="app-title">بوت الدردشة الذكي</h1>
          <p className="app-subtitle">مدعوم بالذكاء الاصطناعي المتقدم</p>
        </div>

        {/* مؤشر التحميل */}
        <div className="loading-indicator">
          <div className="spinner">
            <Loader size={32} className="spinner-icon" />
          </div>
          <p className="loading-text">جاري تحميل التطبيق...</p>
        </div>

        {/* شريط التقدم */}
        <div className="progress-bar">
          <div className="progress-fill"></div>
        </div>

        {/* معلومات التحميل */}
        <div className="loading-steps">
          <div className="step active">
            <span className="step-number">1</span>
            <span className="step-text">تهيئة التطبيق</span>
          </div>
          <div className="step">
            <span className="step-number">2</span>
            <span className="step-text">تحميل النماذج</span>
          </div>
          <div className="step">
            <span className="step-number">3</span>
            <span className="step-text">إعداد الواجهة</span>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="loading-info">
          <div className="feature-highlight">
            <span className="feature-icon">🤖</span>
            <span className="feature-text">نماذج ذكاء اصطناعي متقدمة</span>
          </div>
          <div className="feature-highlight">
            <span className="feature-icon">🌐</span>
            <span className="feature-text">دعم اللغة العربية والإنجليزية</span>
          </div>
          <div className="feature-highlight">
            <span className="feature-icon">🔒</span>
            <span className="feature-text">خصوصية وأمان عاليين</span>
          </div>
        </div>

        {/* تذييل شاشة التحميل */}
        <div className="loading-footer">
          <p className="version-info">الإصدار 1.0.0</p>
          <p className="copyright">© 2024 بوت الدردشة الذكي</p>
        </div>
      </div>

      {/* خلفية متحركة */}
      <div className="animated-background">
        <div className="floating-shape shape-1"></div>
        <div className="floating-shape shape-2"></div>
        <div className="floating-shape shape-3"></div>
        <div className="floating-shape shape-4"></div>
        <div className="floating-shape shape-5"></div>
      </div>
    </div>
  )
}

export default LoadingScreen
