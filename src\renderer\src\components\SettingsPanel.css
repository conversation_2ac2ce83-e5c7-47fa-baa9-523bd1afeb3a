.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.settings-panel {
  width: 90%;
  max-width: 800px;
  height: 90%;
  max-height: 700px;
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* رأس لوحة الإعدادات */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.settings-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: 600;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 50%;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: var(--danger-color);
  color: white;
}

/* علامات التبويب */
.settings-tabs {
  display: flex;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  background: none;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: var(--bg-primary);
}

/* محتوى الإعدادات */
.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.settings-tab-content {
  max-width: 600px;
  margin: 0 auto;
}

/* مجموعات الإعدادات */
.setting-group {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.setting-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-group h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.setting-description {
  display: block;
  margin-top: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.setting-description a {
  color: var(--primary-color);
  text-decoration: none;
}

.setting-description a:hover {
  text-decoration: underline;
}

/* عناصر النموذج */
.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* مربعات الاختيار */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* شريط التمرير */
.range-input {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.range-input::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.range-value {
  text-align: center;
  margin-top: var(--spacing-sm);
  font-weight: 600;
  color: var(--primary-color);
}

/* خيارات السمة */
.theme-options {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-md);
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.theme-option input[type="radio"] {
  display: none;
}

.theme-preview {
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius);
  border: 2px solid var(--border-color);
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.theme-option input[type="radio"]:checked + .theme-preview {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.theme-preview.light {
  background-color: #ffffff;
}

.theme-preview.dark {
  background-color: #2c3e50;
}

.theme-header {
  height: 20px;
  background-color: #f8f9fa;
}

.theme-preview.dark .theme-header {
  background-color: #34495e;
}

.theme-content {
  height: 40px;
  background-color: #ffffff;
}

.theme-preview.dark .theme-content {
  background-color: #2c3e50;
}

/* إدخال الملفات */
.file-input-label {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
}

.file-input-label:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.file-input {
  display: none;
}

/* قسم حول التطبيق */
.about-section {
  text-align: center;
}

.app-info {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.app-info h2 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--primary-color);
  font-size: 2rem;
  font-weight: 700;
}

.version {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-md);
}

.description {
  color: var(--text-primary);
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

.features-list,
.credits,
.contact-info {
  margin-bottom: var(--spacing-xl);
  text-align: right;
}

.features-list h3,
.credits h3,
.contact-info h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.features-list ul,
.credits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li,
.credits li {
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  position: relative;
  padding-right: var(--spacing-lg);
}

.features-list li::before,
.credits li::before {
  content: '✓';
  position: absolute;
  right: 0;
  color: var(--success-color);
  font-weight: bold;
}

.contact-info p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* تذييل لوحة الإعدادات */
.settings-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.save-message {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
  text-align: center;
}

.save-message.success {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.save-message.error {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .settings-panel {
    width: 95%;
    height: 95%;
    margin: 2.5%;
  }
  
  .settings-tabs {
    flex-wrap: wrap;
  }
  
  .tab-btn {
    flex: 1;
    min-width: 0;
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .tab-btn span {
    display: none;
  }
  
  .settings-content {
    padding: var(--spacing-md);
  }
  
  .theme-options {
    justify-content: center;
  }
  
  .footer-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .settings-panel {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
  
  .settings-header {
    padding: var(--spacing-md);
  }
  
  .settings-content {
    padding: var(--spacing-sm);
  }
  
  .setting-group {
    margin-bottom: var(--spacing-lg);
  }
  
  .theme-preview {
    width: 60px;
    height: 45px;
  }
}

/* تحسين شريط التمرير */
.settings-content::-webkit-scrollbar {
  width: 8px;
}

.settings-content::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

.settings-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.settings-content::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

.settings-tabs::-webkit-scrollbar {
  height: 4px;
}

.settings-tabs::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.settings-tabs::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

/* تأثيرات إضافية */
.setting-group {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين إمكانية الوصول */
.tab-btn:focus,
.close-btn:focus,
.checkbox-label:focus-within,
.theme-option:focus-within,
.file-input-label:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* تحسين النصوص الطويلة */
.setting-description,
.description,
.contact-info p {
  word-wrap: break-word;
  overflow-wrap: break-word;
}
