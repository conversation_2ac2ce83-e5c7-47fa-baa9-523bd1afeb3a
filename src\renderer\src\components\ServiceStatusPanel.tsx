import React, { useState, useEffect } from 'react'
import { advancedAI } from '../../services/AdvancedAIService'
import './ServiceStatusPanel.css'

interface ServiceStatusPanelProps {
  isOpen: boolean
  onClose: () => void
  onRefresh?: () => void
}

interface ServiceStatus {
  id: string
  name: string
  type: 'local' | 'cloud'
  status: 'connected' | 'disconnected' | 'error'
  models: any[]
  capabilities: string[]
  priority: number
  lastChecked?: string
  responseTime?: number
}

const ServiceStatusPanel: React.FC<ServiceStatusPanelProps> = ({
  isOpen,
  onClose,
  onRefresh
}) => {
  const [services, setServices] = useState<ServiceStatus[]>([])
  const [stats, setStats] = useState<any>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<string>('')

  useEffect(() => {
    if (isOpen) {
      loadServiceStatus()
    }
  }, [isOpen])

  const loadServiceStatus = async () => {
    setIsRefreshing(true)
    try {
      const providers = advancedAI.getProviders()
      const serviceStats = advancedAI.getServiceStats()
      
      setServices(providers)
      setStats(serviceStats)
      setLastUpdate(new Date().toLocaleTimeString('ar-SA'))
    } catch (error) {
      console.error('خطأ في تحميل حالة الخدمات:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await advancedAI.reinitialize()
      await loadServiceStatus()
      if (onRefresh) {
        onRefresh()
      }
    } catch (error) {
      console.error('خطأ في تحديث الخدمات:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'connected': return '✅'
      case 'disconnected': return '⚠️'
      case 'error': return '❌'
      default: return '❓'
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'connected': return 'متصل'
      case 'disconnected': return 'غير متصل'
      case 'error': return 'خطأ'
      default: return 'غير معروف'
    }
  }

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'connected': return '#10b981'
      case 'disconnected': return '#f59e0b'
      case 'error': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getServiceIcon = (serviceId: string): string => {
    switch (serviceId) {
      case 'msty': return '🟣'
      case 'ollama': return '🦙'
      case 'openrouter': return '🌐'
      case 'lmstudio': return '🏠'
      default: return '🤖'
    }
  }

  const getCapabilityIcon = (capability: string): string => {
    const icons: Record<string, string> = {
      'chat': '💬',
      'streaming': '🔄',
      'local-privacy': '🔒',
      'custom-models': '⚙️',
      'multiple-models': '🔢',
      'high-quality': '⭐',
      'open-source': '🔓'
    }
    return icons[capability] || '✨'
  }

  if (!isOpen) return null

  return (
    <div className="service-status-overlay">
      <div className="service-status-panel">
        {/* رأس اللوحة */}
        <div className="status-header">
          <div className="status-title">
            <h2>🔧 حالة الخدمات</h2>
            <p>مراقبة ومتابعة جميع خدمات الذكاء الاصطناعي</p>
          </div>
          <div className="header-actions">
            <button 
              className={`refresh-btn ${isRefreshing ? 'refreshing' : ''}`}
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              🔄 {isRefreshing ? 'جاري التحديث...' : 'تحديث'}
            </button>
            <button className="close-button" onClick={onClose}>
              ✕
            </button>
          </div>
        </div>

        {/* إحصائيات عامة */}
        {stats && (
          <div className="status-stats">
            <div className="stat-card">
              <div className="stat-icon">🔌</div>
              <div className="stat-info">
                <span className="stat-number">{stats.connectedProviders}</span>
                <span className="stat-label">خدمة متصلة</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🤖</div>
              <div className="stat-info">
                <span className="stat-number">{stats.totalModels}</span>
                <span className="stat-label">نموذج متاح</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📚</div>
              <div className="stat-info">
                <span className="stat-number">{stats.promptsStats?.totalPrompts || 0}</span>
                <span className="stat-label">قالب جاهز</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">⏰</div>
              <div className="stat-info">
                <span className="stat-number">{lastUpdate}</span>
                <span className="stat-label">آخر تحديث</span>
              </div>
            </div>
          </div>
        )}

        {/* قائمة الخدمات */}
        <div className="services-list">
          <h3>📋 الخدمات المتاحة</h3>
          
          {services.length === 0 ? (
            <div className="no-services">
              <div className="no-services-icon">🔍</div>
              <h4>لا توجد خدمات متاحة</h4>
              <p>تأكد من تشغيل إحدى الخدمات المدعومة</p>
            </div>
          ) : (
            services.map((service) => (
              <div key={service.id} className="service-card">
                <div className="service-header">
                  <div className="service-info">
                    <div className="service-icon">
                      {getServiceIcon(service.id)}
                    </div>
                    <div className="service-details">
                      <h4>{service.name}</h4>
                      <span className="service-type">
                        {service.type === 'local' ? '🏠 محلي' : '☁️ سحابي'}
                      </span>
                    </div>
                  </div>
                  <div className="service-status">
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(service.status) }}
                    >
                      {getStatusIcon(service.status)} {getStatusText(service.status)}
                    </span>
                  </div>
                </div>

                <div className="service-content">
                  {/* النماذج المتاحة */}
                  <div className="service-section">
                    <h5>🤖 النماذج المتاحة ({service.models.length})</h5>
                    {service.models.length > 0 ? (
                      <div className="models-list">
                        {service.models.slice(0, 3).map((model, index) => (
                          <span key={index} className="model-tag">
                            {model.name || model.id}
                          </span>
                        ))}
                        {service.models.length > 3 && (
                          <span className="model-tag more">
                            +{service.models.length - 3} أخرى
                          </span>
                        )}
                      </div>
                    ) : (
                      <p className="no-models">لا توجد نماذج متاحة</p>
                    )}
                  </div>

                  {/* القدرات */}
                  <div className="service-section">
                    <h5>✨ القدرات</h5>
                    <div className="capabilities-list">
                      {service.capabilities.map((capability, index) => (
                        <span key={index} className="capability-tag">
                          {getCapabilityIcon(capability)} {capability}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* معلومات إضافية */}
                  <div className="service-section">
                    <div className="service-meta">
                      <span className="meta-item">
                        🎯 الأولوية: {service.priority}
                      </span>
                      {service.responseTime && (
                        <span className="meta-item">
                          ⚡ زمن الاستجابة: {service.responseTime}ms
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* نصائح وإرشادات */}
        <div className="status-tips">
          <h3>💡 نصائح للاستخدام الأمثل</h3>
          <div className="tips-grid">
            <div className="tip-card">
              <div className="tip-icon">🟣</div>
              <div className="tip-content">
                <h4>Msty</h4>
                <p>للحصول على أفضل أداء محلي مع خصوصية كاملة</p>
              </div>
            </div>
            <div className="tip-card">
              <div className="tip-icon">🌐</div>
              <div className="tip-content">
                <h4>OpenRouter</h4>
                <p>للوصول لأحدث النماذج السحابية المتقدمة</p>
              </div>
            </div>
            <div className="tip-card">
              <div className="tip-icon">📚</div>
              <div className="tip-content">
                <h4>مكتبة القوالب</h4>
                <p>استخدم القوالب الجاهزة للحصول على أفضل النتائج</p>
              </div>
            </div>
          </div>
        </div>

        {/* تذييل اللوحة */}
        <div className="status-footer">
          <div className="footer-info">
            <span>🔄 يتم تحديث الحالة تلقائياً كل دقيقة</span>
          </div>
          <div className="footer-actions">
            <button className="secondary-btn" onClick={onClose}>
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ServiceStatusPanel
