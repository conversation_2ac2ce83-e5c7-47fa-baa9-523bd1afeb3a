import axios, { AxiosInstance } from 'axios'
import { emotionalAI } from './EmotionalAI'

/**
 * خدمة الاتصال مع Msty
 * Msty هو تطبيق محلي للذكاء الاصطناعي مشابه لـ LM Studio
 */
export class MstyAPI {
  private apiClient: AxiosInstance
  private baseURL = 'http://localhost:10000/v1' // منفذ Msty الافتراضي
  private isAvailable = false

  constructor() {
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer msty-local-key' // مفتاح محلي افتراضي
      },
      timeout: 30000
    })
  }

  // فحص توفر Msty
  async checkAvailability(): Promise<boolean> {
    const maxRetries = 3;
    const defaultPorts = [10000, 8080, 3000, 5000, 11434];

    for (let retry = 0; retry < maxRetries; retry++) {
      for (const port of defaultPorts) {
        try {
          const client = axios.create({
            baseURL: `http://localhost:${port}/v1`,
            timeout: 3000,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer msty-local-key'
            }
          });

          const response = await client.get('/models');
          if (response.status === 200) {
            this.baseURL = `http://localhost:${port}/v1`;
            this.apiClient.defaults.baseURL = this.baseURL;
            this.isAvailable = true;
            console.log(`✅ تم الاتصال بـ Msty على المنفذ ${port}`);
            return true;
          }
        } catch (error) {
          continue;
        }
      }

      if (retry < maxRetries - 1) {
        console.log(`⏳ محاولة إعادة الاتصال ${retry + 1}/${maxRetries}...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    this.isAvailable = false;
    console.log('❌ تعذر الاتصال بـ Msty - تأكد من تشغيل الخدمة');
    return false;
  }

  // الحصول على النماذج المتاحة
  async getModels(): Promise<any[]> {
    try {
      if (!this.isAvailable) {
        await this.checkAvailability()
      }

      if (!this.isAvailable) {
        return []
      }

      const response = await this.apiClient.get('/models')
      console.log('📋 البيانات المستلمة من الخدمة:', response.data)

      let models = response.data.data || response.data.models || response.data || []

      // التحقق من أن models هو مصفوفة
      if (!Array.isArray(models)) {
        console.log('⚠️ البيانات ليست مصفوفة، محاولة استخراج النماذج...')
        console.log('🔍 نوع البيانات:', typeof models)

        // إذا كانت البيانات كائن، حاول استخراج المصفوفة
        if (models.models && Array.isArray(models.models)) {
          models = models.models
        } else if (models.data && Array.isArray(models.data)) {
          models = models.data
        } else if (typeof models === 'object' && models !== null) {
          // تحويل الكائن إلى مصفوفة
          models = Object.values(models).filter(item =>
            item && typeof item === 'object' && (item.id || item.name)
          )
        } else {
          console.warn('⚠️ تنسيق غير متوقع للنماذج، استخدام نماذج افتراضية')
          models = [
            { id: 'local-model-1', name: 'نموذج محلي 1' },
            { id: 'local-model-2', name: 'نموذج محلي 2' }
          ]
        }
      }

      if (Array.isArray(models) && models.length > 0) {
        return models.map((model: any) => ({
          id: model.id || model.name || 'unknown',
          name: model.id || model.name || 'نموذج غير معروف',
          description: model.description || `نموذج Msty: ${model.id || model.name || 'غير معروف'}`,
          provider: 'msty',
          type: 'local',
          available: true
        }))
      } else {
        console.log('📋 لا توجد نماذج متاحة، إرجاع نماذج افتراضية')
        return [
          {
            id: 'local-fallback-1',
            name: '🏠 نموذج محلي 1',
            description: 'نموذج محلي افتراضي',
            provider: 'msty',
            type: 'local',
            available: true
          },
          {
            id: 'local-fallback-2',
            name: '🏠 نموذج محلي 2',
            description: 'نموذج محلي افتراضي',
            provider: 'msty',
            type: 'local',
            available: true
          }
        ]
      }
    } catch (error) {
      console.error('خطأ في جلب نماذج Msty:', error)
      return []
    }
  }

  // إرسال رسالة للدردشة
  async sendMessage(messages: any[], model: string): Promise<any> {
    try {
      if (!this.isAvailable) {
        throw new Error('Msty غير متاح')
      }

      // تحليل المشاعر في آخر رسالة
      const lastMessage = messages[messages.length - 1]
      const userMessage = lastMessage?.content || ''

      // تحليل المشاعر وتحسين الاستجابة
      const emotionalAnalysis = emotionalAI.getFullEmotionalAnalysis(userMessage, model)

      // تحديث الرسالة النظامية مع التحليل العاطفي
      const systemMessage = emotionalAnalysis.systemMessage
      const enhancedMessages = [
        { role: 'system', content: systemMessage },
        ...messages.filter(msg => msg.role !== 'system')
      ]

      // محاولة إرسال الرسالة مع نقاط API مختلفة
      const endpoints = ['/chat/completions', '/v1/chat/completions', '/api/chat', '/chat']
      let lastError: any = null

      for (const endpoint of endpoints) {
        try {
          console.log(`🔄 محاولة إرسال إلى: ${endpoint}`)

          const response = await this.apiClient.post(endpoint, {
            model: model,
            messages: enhancedMessages,
            temperature: 0.7,
            max_tokens: 2000,
            stream: false
          })

          if (response.data && response.data.choices && response.data.choices[0]) {
            return {
              success: true,
              message: response.data.choices[0].message.content,
              usage: response.data.usage
            }
          }
        } catch (endpointError) {
          console.log(`❌ فشل ${endpoint}:`, (endpointError as any).message)
          lastError = endpointError
          continue
        }
      }

      // إذا فشلت جميع المحاولات، استخدم النظام الاحتياطي
      console.log('🔄 استخدام النظام الاحتياطي المحلي...')
      return this.generateFallbackResponse(userMessage, model)

    } catch (error) {
      console.error('خطأ في إرسال رسالة لـ Msty:', error)

      // استخدام النظام الاحتياطي في حالة الخطأ
      const lastMessage = messages[messages.length - 1]
      const userMessage = lastMessage?.content || ''
      return this.generateFallbackResponse(userMessage, model)
    }
  }

  // نظام احتياطي لتوليد الاستجابات
  private generateFallbackResponse(userMessage: string, model: string): any {
    const responses = [
      `مرحباً! أفهم أنك تسأل عن "${userMessage}".

🔧 **حالة النظام الحالية:**
• الخدمة المحلية متصلة ولكن لا تدعم Chat API
• يعمل النظام الاحتياطي حالياً

💡 **للحصول على استجابات ذكية حقيقية:**

**الخيار الأول - Ollama (مُوصى به):**
\`\`\`bash
# تثبيت Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# تحميل نموذج
ollama pull llama3.1:8b
\`\`\`

**الخيار الثاني - OpenRouter:**
• احصل على مفتاح API من https://openrouter.ai/
• أدخله في إعدادات التطبيق ⚙️

**النماذج المُوصى بها:**
• 🦙 Llama 3.1 8B - للمحادثة العامة
• 💻 CodeLlama 13B - للبرمجة
• ⚡ Phi-3 Mini - للأجهزة المحدودة

بمجرد تفعيل أي نموذج، ستحصل على استجابات ذكية ومتقدمة!

هل تريد مساعدة في إعداد Ollama؟`,

      `شكراً لرسالتك! 📝

أرى أنك تسأل عن "${userMessage}".

🤖 **النظام الاحتياطي نشط حالياً** لأن الخدمة المحلية لا تدعم Chat API.

🚀 **لتفعيل الذكاء الاصطناعي الحقيقي:**

**للمبتدئين:**
\`\`\`bash
ollama pull phi3:mini     # خفيف وسريع (2.3GB)
\`\`\`

**للاستخدام العام:**
\`\`\`bash
ollama pull llama3.1:8b  # الأفضل للجميع (4.7GB)
\`\`\`

**للمطورين:**
\`\`\`bash
ollama pull codellama:13b # متخصص في البرمجة (7.3GB)
\`\`\`

📥 **خطوات التثبيت:**
1. حمل Ollama من https://ollama.ai/
2. ثبت التطبيق
3. شغل أحد الأوامر أعلاه
4. أعد تشغيل هذا التطبيق

💬 **أو استخدم OpenRouter للنماذج السحابية المتقدمة!**

ما النموذج الذي تفضل تجربته؟`
    ]

    const randomResponse = responses[Math.floor(Math.random() * responses.length)]

    return {
      success: true,
      message: randomResponse,
      usage: {
        prompt_tokens: userMessage.length,
        completion_tokens: randomResponse.length,
        total_tokens: userMessage.length + randomResponse.length
      },
      fallback: true
    }
  }

  // إرسال رسالة مع تدفق (streaming)
  async sendMessageStream(messages: any[], model: string, onChunk: (chunk: string) => void): Promise<any> {
    try {
      if (!this.isAvailable) {
        throw new Error('Msty غير متاح')
      }

      // تحليل المشاعر في آخر رسالة
      const lastMessage = messages[messages.length - 1]
      const userMessage = lastMessage?.content || ''

      // تحليل المشاعر وتحسين الاستجابة
      const emotionalAnalysis = emotionalAI.getFullEmotionalAnalysis(userMessage, model)

      // تحديث الرسالة النظامية مع التحليل العاطفي
      const systemMessage = emotionalAnalysis.systemMessage
      const enhancedMessages = [
        { role: 'system', content: systemMessage },
        ...messages.filter(msg => msg.role !== 'system')
      ]

      const response = await this.apiClient.post('/chat/completions', {
        model: model,
        messages: enhancedMessages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: true
      }, {
        responseType: 'stream'
      })

      let fullMessage = ''

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)

            if (data === '[DONE]') {
              return
            }

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content

              if (content) {
                fullMessage += content
                onChunk(content)
              }
            } catch (parseError) {
              // تجاهل أخطاء التحليل
            }
          }
        }
      })

      return new Promise((resolve) => {
        response.data.on('end', () => {
          resolve({
            success: true,
            message: fullMessage
          })
        })

        response.data.on('error', (error: any) => {
                  resolve({
                    success: false,
                    error: (error as any).message
                  })
        })
      })
    } catch (error) {
      console.error('خطأ في إرسال رسالة متدفقة لـ Msty:', error)
      return {
        success: false,
        error: (error as any).message || 'خطأ في الاتصال مع Msty'
      }
    }
  }

  // فحص حالة النموذج
  async getModelInfo(modelId: string): Promise<any> {
    try {
      const response = await this.apiClient.get(`/models/${modelId}`)
      return {
        success: true,
        model: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: (error as any).message
      }
    }
  }

  // تحديث إعدادات الاتصال
  updateConnection(baseURL: string, apiKey?: string): void {
    this.baseURL = baseURL
    this.apiClient.defaults.baseURL = baseURL

    if (apiKey) {
      this.apiClient.defaults.headers['Authorization'] = `Bearer ${apiKey}`
    }
  }

  // الحصول على معلومات الخادم
  async getServerInfo(): Promise<any> {
    try {
      const response = await this.apiClient.get('/health')
      return {
        success: true,
        info: response.data,
        baseURL: this.baseURL,
        available: this.isAvailable
      }
    } catch (error) {
      return {
        success: false,
        error: (error as any).message,
        baseURL: this.baseURL,
        available: false
      }
    }
  }
}

// إنشاء مثيل مشترك
export const mstyAPI = new MstyAPI()
