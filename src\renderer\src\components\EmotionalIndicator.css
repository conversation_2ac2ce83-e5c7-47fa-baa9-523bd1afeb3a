.emotional-indicator {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  animation: slideInLeft 0.3s ease-out;
}

.emotional-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: var(--spacing-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 280px;
  max-width: 320px;
  font-size: var(--font-size-sm);
}

[data-theme="dark"] .emotional-card {
  background: linear-gradient(135deg, rgba(44, 62, 80, 0.95), rgba(52, 73, 94, 0.95));
  border-color: rgba(255, 255, 255, 0.1);
}

/* رأس البطاقة */
.emotional-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .emotional-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.emotional-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-secondary);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
}

/* أيقونات المشاعر */
.emotion-icon {
  transition: all 0.3s ease;
}

.emotion-icon.happiness {
  color: #FFD700;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));
}

.emotion-icon.love {
  color: #FF69B4;
  filter: drop-shadow(0 0 8px rgba(255, 105, 180, 0.3));
}

.emotion-icon.excitement {
  color: #FF4500;
  filter: drop-shadow(0 0 8px rgba(255, 69, 0, 0.3));
}

.emotion-icon.sadness {
  color: #4682B4;
  filter: drop-shadow(0 0 8px rgba(70, 130, 180, 0.3));
}

.emotion-icon.anger {
  color: #DC143C;
  filter: drop-shadow(0 0 8px rgba(220, 20, 60, 0.3));
}

.emotion-icon.anxiety {
  color: #9370DB;
  filter: drop-shadow(0 0 8px rgba(147, 112, 219, 0.3));
}

.emotion-icon.loneliness {
  color: #708090;
  filter: drop-shadow(0 0 8px rgba(112, 128, 144, 0.3));
}

.emotion-icon.nostalgia {
  color: #DDA0DD;
  filter: drop-shadow(0 0 8px rgba(221, 160, 221, 0.3));
}

.emotion-icon.hope {
  color: #32CD32;
  filter: drop-shadow(0 0 8px rgba(50, 205, 50, 0.3));
}

.emotion-icon.neutral {
  color: #808080;
}

/* المشاعر الأساسية */
.primary-emotion {
  margin-bottom: var(--spacing-md);
}

.emotion-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.emotion-label {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.emotion-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.metric {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 40px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

[data-theme="dark"] .progress-bar {
  background-color: rgba(255, 255, 255, 0.1);
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-fill.confidence {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.metric-value {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  min-width: 35px;
  text-align: right;
}

/* المشاعر الثانوية */
.secondary-emotions {
  margin-bottom: var(--spacing-md);
}

.section-title {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.emotion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.emotion-tag {
  padding: 2px 8px;
  border: 1px solid;
  border-radius: 12px;
  font-size: 0.75rem;
  background-color: rgba(255, 255, 255, 0.5);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

[data-theme="dark"] .emotion-tag {
  background-color: rgba(0, 0, 0, 0.3);
}

.emotion-tag:hover {
  transform: scale(1.05);
}

/* الاقتراحات */
.suggestions {
  margin-bottom: var(--spacing-md);
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestion-item {
  padding: var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  position: relative;
  padding-right: var(--spacing-md);
}

.suggestion-item::before {
  content: '💡';
  position: absolute;
  right: 0;
  top: var(--spacing-xs);
}

/* الرسالة العاطفية */
.emotional-message {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
  border: 1px solid rgba(0, 123, 255, 0.2);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm);
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: 500;
}

[data-theme="dark"] .emotional-message {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.1));
}

/* الأنيميشن */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.emotion-icon {
  animation: pulse 2s infinite;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .emotional-indicator {
    top: 10px;
    left: 10px;
    right: 10px;
  }
  
  .emotional-card {
    min-width: auto;
    max-width: none;
  }
  
  .emotion-metrics {
    gap: var(--spacing-xs);
  }
  
  .metric {
    font-size: 0.8rem;
  }
  
  .emotion-tags {
    gap: 4px;
  }
  
  .emotion-tag {
    font-size: 0.7rem;
    padding: 1px 6px;
  }
}

@media (max-width: 480px) {
  .emotional-indicator {
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    margin: var(--spacing-sm);
  }
  
  .emotional-card {
    padding: var(--spacing-sm);
  }
  
  .emotional-title {
    font-size: var(--font-size-sm);
  }
  
  .emotion-label {
    font-size: var(--font-size-base);
  }
}

/* تأثيرات إضافية */
.emotional-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.emotional-card {
  transition: all 0.3s ease;
}

/* تحسين إمكانية الوصول */
.close-btn:focus,
.emotion-tag:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* تحسين النصوص العربية */
.emotional-card {
  direction: rtl;
  text-align: right;
}

.progress-bar {
  direction: ltr;
}

.metric-value {
  direction: ltr;
}
