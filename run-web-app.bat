@echo off
title Run AI Chat Bot as Web App

echo ========================================
echo    Run AI Chat Bot as Web App
echo ========================================
echo.

echo Step 1: Building the web version...
call npm run build:renderer
if errorlevel 1 (
    echo Warning: Issues with renderer build, but continuing...
)

echo Step 2: Starting the web server...
echo The application will be available at http://localhost:5000
echo.
echo Press Ctrl+C to stop the server when you're done.
echo.

npx serve -s dist/renderer

echo.
pause
