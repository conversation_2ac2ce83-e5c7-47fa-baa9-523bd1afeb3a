import React, { useState, useRef, useEffect } from 'react';
import { 
  Play, 
  Copy, 
  Download, 
  FileText, 
  Code, 
  Terminal,
  Settings,
  Maximize2,
  Minimize2,
  RefreshCw,
  Save
} from 'lucide-react';

interface CodeEditorProps {
  initialCode?: string;
  language?: string;
  theme?: 'light' | 'dark';
  onCodeChange?: (code: string) => void;
  onRunCode?: (code: string, language: string) => Promise<any>;
  className?: string;
}

const supportedLanguages = [
  { id: 'javascript', name: 'JavaScript', icon: '🟨', extension: '.js' },
  { id: 'typescript', name: 'TypeScript', icon: '🔷', extension: '.ts' },
  { id: 'python', name: 'Python', icon: '🐍', extension: '.py' },
  { id: 'html', name: 'HTML', icon: '🌐', extension: '.html' },
  { id: 'css', name: 'CSS', icon: '🎨', extension: '.css' },
  { id: 'json', name: 'J<PERSON><PERSON>', icon: '📋', extension: '.json' },
  { id: 'markdown', name: 'Markdown', icon: '📝', extension: '.md' },
  { id: 'sql', name: 'S<PERSON>', icon: '🗄️', extension: '.sql' },
  { id: 'bash', name: 'Bash', icon: '⚡', extension: '.sh' },
  { id: 'xml', name: 'XML', icon: '📄', extension: '.xml' }
];

const codeTemplates = {
  javascript: `// مرحباً بك في محرر JavaScript
console.log('مرحباً من JavaScript!');

// مثال على دالة
function greet(name) {
  return \`مرحباً \${name}!\`;
}

console.log(greet('صديقي العبقري'));`,

  python: `# مرحباً بك في محرر Python
print('مرحباً من Python!')

# مثال على دالة
def greet(name):
    return f'مرحباً {name}!'

print(greet('صديقي العبقري'))`,

  html: `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة تجريبية</title>
</head>
<body>
    <h1>مرحباً بك في محرر HTML</h1>
    <p>هذا مثال على صفحة HTML بسيطة</p>
</body>
</html>`,

  css: `/* مرحباً بك في محرر CSS */
body {
  font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
  direction: rtl;
  text-align: right;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}`,

  json: `{
  "name": "مشروع تجريبي",
  "version": "1.0.0",
  "description": "مثال على ملف JSON",
  "author": "المطور العبقري",
  "features": [
    "سهل الاستخدام",
    "متطور",
    "عربي"
  ],
  "config": {
    "theme": "dark",
    "language": "ar"
  }
}`
};

export const CodeEditor: React.FC<CodeEditorProps> = ({
  initialCode = '',
  language = 'javascript',
  theme = 'dark',
  onCodeChange,
  onRunCode,
  className = ''
}) => {
  const [code, setCode] = useState(initialCode || codeTemplates[language as keyof typeof codeTemplates] || '');
  const [selectedLanguage, setSelectedLanguage] = useState(language);
  const [isRunning, setIsRunning] = useState(false);
  const [output, setOutput] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [fontSize, setFontSize] = useState(14);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [wordWrap, setWordWrap] = useState(true);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const outputRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (onCodeChange) {
      onCodeChange(code);
    }
  }, [code, onCodeChange]);

  useEffect(() => {
    // تحديث الكود عند تغيير اللغة
    if (!initialCode) {
      setCode(codeTemplates[selectedLanguage as keyof typeof codeTemplates] || '');
    }
  }, [selectedLanguage, initialCode]);

  const handleCodeChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCode(e.target.value);
  };

  const handleRunCode = async () => {
    if (!onRunCode) {
      // محاكاة تشغيل الكود
      setIsRunning(true);
      setOutput('جاري تشغيل الكود...\n');
      
      setTimeout(() => {
        setOutput(`✅ تم تشغيل الكود بنجاح!\n\nاللغة: ${selectedLanguage}\nعدد الأسطر: ${code.split('\n').length}\nعدد الأحرف: ${code.length}\n\n📝 ملاحظة: هذا مثال على الإخراج. في التطبيق الحقيقي، سيتم تشغيل الكود فعلياً.`);
        setIsRunning(false);
      }, 2000);
      return;
    }

    setIsRunning(true);
    try {
      const result = await onRunCode(code, selectedLanguage);
      setOutput(result.output || 'تم التشغيل بنجاح');
    } catch (error) {
      setOutput(`❌ خطأ في التشغيل:\n${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  const copyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setOutput('✅ تم نسخ الكود إلى الحافظة');
    } catch (error) {
      setOutput('❌ فشل في نسخ الكود');
    }
  };

  const downloadCode = () => {
    const selectedLang = supportedLanguages.find(lang => lang.id === selectedLanguage);
    const extension = selectedLang?.extension || '.txt';
    const filename = `code${extension}`;
    
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
    
    setOutput(`✅ تم تحميل الملف: ${filename}`);
  };

  const clearCode = () => {
    setCode('');
    setOutput('تم مسح الكود');
  };

  const loadTemplate = () => {
    const template = codeTemplates[selectedLanguage as keyof typeof codeTemplates];
    if (template) {
      setCode(template);
      setOutput(`✅ تم تحميل قالب ${selectedLanguage}`);
    }
  };

  const getLineNumbers = () => {
    const lines = code.split('\n');
    return lines.map((_, index) => index + 1).join('\n');
  };

  return (
    <div className={`code-editor ${isFullscreen ? 'fullscreen' : ''} ${className}`}>
      {/* شريط الأدوات */}
      <div className="editor-toolbar">
        <div className="toolbar-left">
          <select
            value={selectedLanguage}
            onChange={(e) => setSelectedLanguage(e.target.value)}
            className="language-selector"
          >
            {supportedLanguages.map(lang => (
              <option key={lang.id} value={lang.id}>
                {lang.icon} {lang.name}
              </option>
            ))}
          </select>

          <button
            onClick={loadTemplate}
            className="btn btn-sm"
            title="تحميل قالب"
          >
            <FileText size={16} />
            قالب
          </button>
        </div>

        <div className="toolbar-center">
          <span className="editor-title">
            <Code size={16} />
            محرر الكود المتطور
          </span>
        </div>

        <div className="toolbar-right">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="btn btn-sm"
            title="الإعدادات"
          >
            <Settings size={16} />
          </button>

          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="btn btn-sm"
            title={isFullscreen ? 'تصغير' : 'ملء الشاشة'}
          >
            {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </button>
        </div>
      </div>

      {/* إعدادات المحرر */}
      {showSettings && (
        <div className="editor-settings">
          <div className="setting-group">
            <label>حجم الخط:</label>
            <input
              type="range"
              min="10"
              max="24"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
            />
            <span>{fontSize}px</span>
          </div>

          <div className="setting-group">
            <label>
              <input
                type="checkbox"
                checked={showLineNumbers}
                onChange={(e) => setShowLineNumbers(e.target.checked)}
              />
              إظهار أرقام الأسطر
            </label>
          </div>

          <div className="setting-group">
            <label>
              <input
                type="checkbox"
                checked={wordWrap}
                onChange={(e) => setWordWrap(e.target.checked)}
              />
              التفاف النص
            </label>
          </div>
        </div>
      )}

      {/* منطقة المحرر */}
      <div className="editor-container">
        <div className="editor-main">
          {showLineNumbers && (
            <div 
              className="line-numbers"
              style={{ fontSize: `${fontSize}px` }}
            >
              {getLineNumbers()}
            </div>
          )}
          
          <textarea
            ref={textareaRef}
            value={code}
            onChange={handleCodeChange}
            className="code-textarea"
            placeholder="اكتب الكود هنا..."
            spellCheck={false}
            style={{
              fontSize: `${fontSize}px`,
              whiteSpace: wordWrap ? 'pre-wrap' : 'pre',
              wordWrap: wordWrap ? 'break-word' : 'normal'
            }}
          />
        </div>

        {/* منطقة الإخراج */}
        <div className="output-container">
          <div className="output-header">
            <Terminal size={16} />
            <span>الإخراج</span>
            <button
              onClick={() => setOutput('')}
              className="btn btn-sm"
              title="مسح الإخراج"
            >
              <RefreshCw size={14} />
            </button>
          </div>
          
          <div 
            ref={outputRef}
            className="output-content"
            style={{ fontSize: `${fontSize - 2}px` }}
          >
            {output || 'لا يوجد إخراج بعد...'}
          </div>
        </div>
      </div>

      {/* شريط الأدوات السفلي */}
      <div className="editor-footer">
        <div className="footer-left">
          <button
            onClick={handleRunCode}
            disabled={isRunning}
            className="btn btn-primary"
          >
            {isRunning ? (
              <>
                <RefreshCw size={16} className="spinning" />
                جاري التشغيل...
              </>
            ) : (
              <>
                <Play size={16} />
                تشغيل
              </>
            )}
          </button>

          <button
            onClick={copyCode}
            className="btn btn-outline"
            title="نسخ الكود"
          >
            <Copy size={16} />
            نسخ
          </button>

          <button
            onClick={downloadCode}
            className="btn btn-outline"
            title="تحميل الكود"
          >
            <Download size={16} />
            تحميل
          </button>

          <button
            onClick={clearCode}
            className="btn btn-outline btn-danger"
            title="مسح الكود"
          >
            <RefreshCw size={16} />
            مسح
          </button>
        </div>

        <div className="footer-right">
          <span className="code-stats">
            الأسطر: {code.split('\n').length} | 
            الأحرف: {code.length} | 
            اللغة: {supportedLanguages.find(l => l.id === selectedLanguage)?.name}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;
