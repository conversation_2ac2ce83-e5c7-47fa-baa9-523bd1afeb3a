import React, { useState, useEffect } from 'react';
import { Brain, Upload, Setting<PERSON>, Play, Pause, Bar<PERSON>hart3, <PERSON><PERSON><PERSON>, Target, Zap } from 'lucide-react';

interface TrainingConfig {
  modelName: string;
  learningRate: number;
  epochs: number;
  batchSize: number;
  specialization: string;
  personality: string;
  language: string;
}

interface TrainingProgress {
  currentEpoch: number;
  totalEpochs: number;
  loss: number;
  accuracy: number;
  status: 'idle' | 'training' | 'paused' | 'completed' | 'error';
}

const GeniusTraining: React.FC = () => {
  const [config, setConfig] = useState<TrainingConfig>({
    modelName: 'arabic-genius-v1',
    learningRate: 0.0001,
    epochs: 10,
    batchSize: 4,
    specialization: 'general',
    personality: 'helpful',
    language: 'arabic'
  });

  const [progress, setProgress] = useState<TrainingProgress>({
    currentEpoch: 0,
    totalEpochs: 0,
    loss: 0,
    accuracy: 0,
    status: 'idle'
  });

  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [logs, setLogs] = useState<string[]>([]);

  const specializations = [
    { value: 'general', label: '🌟 عام ومتنوع', desc: 'مساعد ذكي شامل' },
    { value: 'business', label: '💼 الأعمال', desc: 'خبير في إدارة الأعمال' },
    { value: 'education', label: '🎓 التعليم', desc: 'مدرس ومرشد تعليمي' },
    { value: 'creative', label: '🎨 الإبداع', desc: 'كاتب وفنان مبدع' },
    { value: 'technical', label: '⚙️ التقني', desc: 'خبير تقني ومطور' },
    { value: 'medical', label: '🏥 الطبي', desc: 'مساعد طبي متخصص' }
  ];

  const personalities = [
    { value: 'helpful', label: '😊 مساعد ودود', desc: 'مفيد ومتعاون' },
    { value: 'professional', label: '👔 مهني', desc: 'رسمي ودقيق' },
    { value: 'creative', label: '🎭 مبدع', desc: 'خيالي ومبتكر' },
    { value: 'wise', label: '🧙‍♂️ حكيم', desc: 'عميق ومتأمل' },
    { value: 'energetic', label: '⚡ نشيط', desc: 'متحمس ومحفز' },
    { value: 'calm', label: '🧘 هادئ', desc: 'صبور ومتزن' }
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const fileNames = Array.from(files).map(file => file.name);
      setUploadedFiles(prev => [...prev, ...fileNames]);
      addLog(`📁 تم رفع ${files.length} ملف: ${fileNames.join(', ')}`);
    }
  };

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  const startTraining = () => {
    if (uploadedFiles.length === 0) {
      addLog('❌ يرجى رفع ملفات التدريب أولاً');
      return;
    }

    setProgress(prev => ({ ...prev, status: 'training', totalEpochs: config.epochs }));
    addLog(`🚀 بدء التدريب العبقري للنموذج: ${config.modelName}`);
    addLog(`🎯 التخصص: ${specializations.find(s => s.value === config.specialization)?.label}`);
    addLog(`🎭 الشخصية: ${personalities.find(p => p.value === config.personality)?.label}`);
    
    // محاكاة التدريب
    simulateTraining();
  };

  const simulateTraining = () => {
    let epoch = 0;
    const interval = setInterval(() => {
      epoch++;
      const loss = Math.max(0.1, 2.0 - (epoch * 0.15) + (Math.random() * 0.1));
      const accuracy = Math.min(0.95, 0.3 + (epoch * 0.08) + (Math.random() * 0.05));
      
      setProgress(prev => ({
        ...prev,
        currentEpoch: epoch,
        loss: parseFloat(loss.toFixed(4)),
        accuracy: parseFloat(accuracy.toFixed(4))
      }));

      addLog(`📊 العصر ${epoch}/${config.epochs} - الخسارة: ${loss.toFixed(4)} - الدقة: ${(accuracy * 100).toFixed(2)}%`);

      if (epoch >= config.epochs) {
        clearInterval(interval);
        setProgress(prev => ({ ...prev, status: 'completed' }));
        addLog('🎉 تم إكمال التدريب بنجاح! النموذج أصبح عبقرياً!');
        addLog('💾 تم حفظ النموذج المدرب');
      }
    }, 2000);
  };

  const pauseTraining = () => {
    setProgress(prev => ({ ...prev, status: 'paused' }));
    addLog('⏸️ تم إيقاف التدريب مؤقتاً');
  };

  return (
    <div className="genius-training" dir="rtl">
      <div className="training-header">
        <div className="header-content">
          <Brain className="brain-icon" size={32} />
          <div>
            <h1>🧠 التدريب العبقري</h1>
            <p>اجعل الذكاء الاصطناعي عبقرياً مثلك!</p>
          </div>
        </div>
      </div>

      <div className="training-grid">
        {/* قسم رفع البيانات */}
        <div className="training-card">
          <div className="card-header">
            <Upload size={24} />
            <h3>📁 بيانات التدريب</h3>
          </div>
          <div className="upload-area">
            <input
              type="file"
              multiple
              accept=".txt,.json,.csv"
              onChange={handleFileUpload}
              className="file-input"
              id="training-files"
            />
            <label htmlFor="training-files" className="upload-label">
              <Sparkles size={48} />
              <p>اسحب الملفات هنا أو انقر للاختيار</p>
              <small>ملفات نصية، JSON، أو CSV</small>
            </label>
          </div>
          {uploadedFiles.length > 0 && (
            <div className="uploaded-files">
              <h4>الملفات المرفوعة ({uploadedFiles.length}):</h4>
              <ul>
                {uploadedFiles.map((file, index) => (
                  <li key={index}>📄 {file}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* قسم الإعدادات */}
        <div className="training-card">
          <div className="card-header">
            <Settings size={24} />
            <h3>⚙️ إعدادات العبقرية</h3>
          </div>
          <div className="config-form">
            <div className="form-group">
              <label>🏷️ اسم النموذج:</label>
              <input
                type="text"
                value={config.modelName}
                onChange={(e) => setConfig(prev => ({ ...prev, modelName: e.target.value }))}
                placeholder="اسم النموذج العبقري"
              />
            </div>

            <div className="form-group">
              <label>🎯 التخصص:</label>
              <select
                value={config.specialization}
                onChange={(e) => setConfig(prev => ({ ...prev, specialization: e.target.value }))}
              >
                {specializations.map(spec => (
                  <option key={spec.value} value={spec.value}>
                    {spec.label} - {spec.desc}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label>🎭 الشخصية:</label>
              <select
                value={config.personality}
                onChange={(e) => setConfig(prev => ({ ...prev, personality: e.target.value }))}
              >
                {personalities.map(pers => (
                  <option key={pers.value} value={pers.value}>
                    {pers.label} - {pers.desc}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>📚 عدد العصور:</label>
                <input
                  type="number"
                  value={config.epochs}
                  onChange={(e) => setConfig(prev => ({ ...prev, epochs: parseInt(e.target.value) }))}
                  min="1"
                  max="100"
                />
              </div>
              <div className="form-group">
                <label>⚡ معدل التعلم:</label>
                <input
                  type="number"
                  value={config.learningRate}
                  onChange={(e) => setConfig(prev => ({ ...prev, learningRate: parseFloat(e.target.value) }))}
                  step="0.0001"
                  min="0.0001"
                  max="0.01"
                />
              </div>
            </div>
          </div>
        </div>

        {/* قسم التحكم */}
        <div className="training-card">
          <div className="card-header">
            <Target size={24} />
            <h3>🎮 التحكم في التدريب</h3>
          </div>
          <div className="control-panel">
            <button
              className={`control-btn start ${progress.status === 'training' ? 'disabled' : ''}`}
              onClick={startTraining}
              disabled={progress.status === 'training'}
            >
              <Play size={20} />
              بدء التدريب العبقري
            </button>
            <button
              className={`control-btn pause ${progress.status !== 'training' ? 'disabled' : ''}`}
              onClick={pauseTraining}
              disabled={progress.status !== 'training'}
            >
              <Pause size={20} />
              إيقاف مؤقت
            </button>
          </div>
          
          {progress.status !== 'idle' && (
            <div className="progress-section">
              <div className="progress-info">
                <span>العصر: {progress.currentEpoch}/{progress.totalEpochs}</span>
                <span>الحالة: {getStatusText(progress.status)}</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${(progress.currentEpoch / progress.totalEpochs) * 100}%` }}
                />
              </div>
              {progress.loss > 0 && (
                <div className="metrics">
                  <span>📉 الخسارة: {progress.loss}</span>
                  <span>📈 الدقة: {(progress.accuracy * 100).toFixed(2)}%</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* قسم السجلات */}
        <div className="training-card logs-card">
          <div className="card-header">
            <BarChart3 size={24} />
            <h3>📊 سجل التدريب</h3>
          </div>
          <div className="logs-container">
            {logs.length === 0 ? (
              <p className="no-logs">لا توجد سجلات بعد...</p>
            ) : (
              <ul className="logs-list">
                {logs.map((log, index) => (
                  <li key={index} className="log-entry">{log}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>

      <style jsx>{`
        .genius-training {
          padding: 20px;
          max-width: 1200px;
          margin: 0 auto;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .training-header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 15px;
          padding: 30px;
          margin-bottom: 30px;
          color: white;
          text-align: center;
        }

        .header-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 20px;
        }

        .brain-icon {
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        .training-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: 20px;
        }

        .training-card {
          background: white;
          border-radius: 15px;
          padding: 25px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.1);
          border: 1px solid #e1e5e9;
        }

        .logs-card {
          grid-column: 1 / -1;
        }

        .card-header {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 20px;
          color: #333;
        }

        .upload-area {
          border: 2px dashed #667eea;
          border-radius: 10px;
          padding: 40px;
          text-align: center;
          transition: all 0.3s ease;
        }

        .upload-area:hover {
          border-color: #764ba2;
          background: #f8f9ff;
        }

        .file-input {
          display: none;
        }

        .upload-label {
          cursor: pointer;
          color: #667eea;
        }

        .uploaded-files {
          margin-top: 15px;
          padding: 15px;
          background: #f8f9ff;
          border-radius: 8px;
        }

        .uploaded-files ul {
          list-style: none;
          padding: 0;
          margin: 10px 0 0 0;
        }

        .uploaded-files li {
          padding: 5px 0;
          color: #555;
        }

        .config-form {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
        }

        .form-group label {
          font-weight: 600;
          color: #333;
        }

        .form-group input,
        .form-group select {
          padding: 10px;
          border: 2px solid #e1e5e9;
          border-radius: 8px;
          font-size: 14px;
          transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
          outline: none;
          border-color: #667eea;
        }

        .control-panel {
          display: flex;
          gap: 10px;
          margin-bottom: 20px;
        }

        .control-btn {
          flex: 1;
          padding: 12px 20px;
          border: none;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          transition: all 0.3s ease;
        }

        .control-btn.start {
          background: linear-gradient(135deg, #4CAF50, #45a049);
          color: white;
        }

        .control-btn.pause {
          background: linear-gradient(135deg, #ff9800, #f57c00);
          color: white;
        }

        .control-btn:hover:not(.disabled) {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .control-btn.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .progress-section {
          background: #f8f9ff;
          padding: 15px;
          border-radius: 8px;
        }

        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          font-size: 14px;
          color: #555;
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: #e1e5e9;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 10px;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #667eea, #764ba2);
          transition: width 0.3s ease;
        }

        .metrics {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          color: #555;
        }

        .logs-container {
          max-height: 300px;
          overflow-y: auto;
          background: #f8f9ff;
          border-radius: 8px;
          padding: 15px;
        }

        .logs-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .log-entry {
          padding: 8px 0;
          border-bottom: 1px solid #e1e5e9;
          font-family: 'Courier New', monospace;
          font-size: 13px;
          color: #333;
        }

        .log-entry:last-child {
          border-bottom: none;
        }

        .no-logs {
          text-align: center;
          color: #999;
          font-style: italic;
        }
      `}</style>
    </div>
  );
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'training': return '🔥 يتدرب...';
    case 'paused': return '⏸️ متوقف';
    case 'completed': return '✅ مكتمل';
    case 'error': return '❌ خطأ';
    default: return '⏳ في الانتظار';
  }
};

export default GeniusTraining;
