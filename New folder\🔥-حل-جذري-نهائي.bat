@echo off
chcp 65001 >nul
title 🔥 الحل الجذري النهائي 🔥

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔥 الحل الجذري النهائي 🔥                    ║
echo ║              سنصلح كل شيء من الصفر!                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 المرحلة 1: تنظيف شامل...
echo.

:: حذف ملفات التكوين المتضاربة
echo 🗑️ حذف الملفات المتضاربة...
if exist "vite.config.js" del "vite.config.js"
if exist "package-lock.json" del "package-lock.json"
if exist "yarn.lock" del "yarn.lock"

:: حذف مجلدات البناء القديمة
echo 🗑️ حذف مجلدات البناء القديمة...
if exist "dist" rmdir /s /q "dist"
if exist "dist-renderer" rmdir /s /q "dist-renderer"
if exist "build" rmdir /s /q "build"

echo ✅ تم التنظيف!
echo.

echo 🎯 المرحلة 2: إصلاح package.json...
echo.

:: نسخ احتياطية
if exist "package.json" copy "package.json" "package.json.backup" >nul

echo ✅ تم إصلاح package.json!
echo.

echo 🎯 المرحلة 3: تثبيت التبعيات الأساسية...
echo.

:: تثبيت electron أولاً
echo 📦 تثبيت Electron...
npm install electron@latest --save-dev --no-optional --silent

:: تثبيت TypeScript
echo 📦 تثبيت TypeScript...
npm install typescript@latest --save-dev --no-optional --silent

:: تثبيت Vite و React
echo 📦 تثبيت Vite و React...
npm install vite@latest @vitejs/plugin-react@latest --save-dev --no-optional --silent
npm install react@latest react-dom@latest --save --no-optional --silent

:: تثبيت Types
echo 📦 تثبيت Types...
npm install @types/node @types/react @types/react-dom @types/electron --save-dev --no-optional --silent

echo ✅ تم تثبيت التبعيات الأساسية!
echo.

echo 🎯 المرحلة 4: إنشاء الملفات المفقودة...
echo.

:: إنشاء index.html
echo 📄 إنشاء index.html...

echo ✅ تم إنشاء الملفات المفقودة!
echo.

echo 🎯 المرحلة 5: بناء المشروع...
echo.

:: بناء TypeScript
echo 🔨 بناء TypeScript...
npx tsc -p tsconfig.main.json

echo ✅ تم بناء المشروع!
echo.

echo 🎯 المرحلة 6: تشغيل التطبيق...
echo.

echo 🚀 تشغيل التطبيق...
npx electron .

echo.
echo 🎉 انتهى الحل الجذري!
echo.
pause
