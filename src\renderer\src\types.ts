// أنواع الرسائل
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  model?: string;
}

// أنواع المحادثات
export interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
  model: string;
  isEncrypted: boolean;
}

// أنواع النماذج
export interface AIModel {
  id: string;
  name: string;
  description: string;
  pricing: {
    prompt: string;
    completion: string;
  };
  context_length: number;
  architecture: {
    modality: string;
    tokenizer: string;
    instruct_type?: string;
  };
  top_provider: {
    max_completion_tokens?: number;
    is_moderated: boolean;
  };
  per_request_limits?: {
    prompt_tokens: string;
    completion_tokens: string;
  };
}

// أنواع الإعدادات
export interface AppSettings {
  openrouter_api_key: string;
  default_model: string;
  theme: 'light' | 'dark';
  language: 'ar' | 'en';
  auto_save: boolean;
  font_size: 'small' | 'medium' | 'large';
  show_timestamps: boolean;
  enable_sound: boolean;
  max_tokens: number;
  temperature: number;
  ollama_base_url: string; // New property
  // حالة الخدمات والنماذج
  services: {
    lmstudio: {
      available: boolean;
      connected: boolean;
      active: boolean;
      activationStatus: string; // New property
      connectionStatus: string; // New property
      errorInfo?: string; // New property
      error?: string;
      lastCheck: string;
    };
    msty: {
      available: boolean;
      connected: boolean;
      active: boolean;
      activationStatus: string; // New property
      connectionStatus: string; // New property
      errorInfo?: string; // New property
      error?: string;
      lastCheck: string;
    };
    ollama: {
      available: boolean;
      connected: boolean;
      active: boolean;
      activationStatus: string; // New property
      connectionStatus: string; // New property
      errorInfo?: string; // New property
      error?: string;
      lastCheck: string;
    };
    openrouter: {
      available: boolean;
      connected: boolean;
      active: boolean;
      activationStatus: string; // New property
      connectionStatus: string; // New property
      errorInfo?: string; // New property
      error?: string;
      lastCheck: string;
      models: string[];
    };
  };
}

// أنواع الاستجابات
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// أنواع الأحداث
export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: string;
}

// أنواع التصدير
export interface ExportOptions {
  format: 'txt' | 'pdf' | 'json' | 'html';
  includeTimestamps: boolean;
  includeMetadata: boolean;
}

// أنواع الإحصائيات
export interface AppStatistics {
  totalConversations: number;
  totalMessages: number;
  modelsUsed: string[];
  averageMessagesPerConversation: number;
  totalCharacters: number;
  mostUsedModel: string;
}

// أنواع الحالة
export interface AppState {
  isLoading: boolean;
  isConnected: boolean;
  currentModel: string;
  isTyping: boolean;
  error: string | null;
}

// أنواع المكونات
export interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// أنواع الأحداث المخصصة
export interface CustomEvents {
  'conversation-created': { conversation: Conversation };
  'conversation-deleted': { conversationId: string };
  'message-sent': { message: ChatMessage; conversationId: string };
  'settings-updated': { settings: Partial<AppSettings> };
  'model-changed': { modelId: string };
  'theme-changed': { theme: 'light' | 'dark' };
}

// أنواع الملفات
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

// أنواع النسخ الاحتياطي
export interface BackupData {
  version: string;
  timestamp: string;
  conversations: Record<string, Conversation>;
  settings: Partial<AppSettings>;
}

// أنواع التحقق
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// أنواع الإشعارات
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

// أنواع البحث
export interface SearchOptions {
  query: string;
  caseSensitive: boolean;
  wholeWords: boolean;
  regex: boolean;
  includeMessages: boolean;
  includeTitles: boolean;
}

export interface SearchResult {
  conversationId: string;
  messageId?: string;
  title: string;
  snippet: string;
  relevance: number;
}

// أنواع الاختصارات
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  action: string;
  description: string;
}

// أنواع السمات
export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    mono: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: string;
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

// أنواع الأداء
export interface PerformanceMetrics {
  responseTime: number;
  tokensPerSecond: number;
  memoryUsage: number;
  cpuUsage: number;
  networkLatency: number;
}

// أنواع الأخطاء المخصصة
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class APIError extends AppError {
  constructor(message: string, public statusCode: number, details?: any) {
    super(message, 'API_ERROR', details);
    this.name = 'APIError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, public field: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

// أنواع المساعدات
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// أنواع الأحداث العامة
export type EventHandler<T = any> = (event: T) => void | Promise<void>;

export type AsyncEventHandler<T = any> = (event: T) => Promise<void>;

// أنواع الحالة العامة
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type ConnectionState = 'connected' | 'disconnected' | 'connecting' | 'error';

declare global {
  interface Window {
    electronAPI: {
      sendMessage: (data: { message: string; model: string; conversationId: string }) => Promise<any>;
      getAvailableModels: () => Promise<any>;
      createConversation: (title: string) => Promise<any>;
      getConversations: () => Promise<any>;
      getConversation: (conversationId: string) => Promise<any>;
      deleteConversation: (conversationId: string) => Promise<any>;
      exportConversation: (data: { conversationId: string; format: string }) => Promise<any>;
      saveSettings: (settings: any) => Promise<any>;
      getSettings: () => Promise<any>;
      updateAISettings: (settings: any) => Promise<any>;
      getServicesStatus: () => Promise<any>;
      launchLMStudio: () => Promise<any>;
    }
  }
}
