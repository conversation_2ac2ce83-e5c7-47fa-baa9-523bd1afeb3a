# دليل تحسينات تطبيق الذكاء الاصطناعي للكتابة الإبداعية

## التحسينات الرئيسية

قمنا بتنفيذ مجموعة من التحسينات الشاملة لتطوير التطبيق وتعزيز تجربة المستخدم في مجال كتابة الروايات والشعر:

### 1. تحسين تكامل نماذج الذكاء الاصطناعي

- **تكامل Ollama**: تم تحسين الاتصال مع خدمة Ollama المحلية للحصول على نماذج محلية عالية الجودة.
- **تكامل OpenRouter**: تم تحسين الاتصال مع خدمة OpenRouter للوصول إلى النماذج السحابية المتخصصة.
- **آلية التبديل التلقائي**: إضافة آلية ذكية للتبديل بين المزودين حسب توفرهم.
- **نماذج متخصصة**: إضافة نماذج متخصصة للروايات والشعر والسينما.

### 2. تحسين واجهة المستخدم

- **مؤشرات حالة الخدمة**: إضافة مؤشرات واضحة لحالة خدمات الذكاء الاصطناعي.
- **تصنيف النماذج**: تنظيم النماذج حسب التخصص (روايات، شعر، سينما، برمجة).
- **تحسين التصميم**: تحسين تصميم واجهة اختيار النماذج لتجربة مستخدم أفضل.

### 3. تحسين تجربة الكتابة الإبداعية

- **مساعد الروايات**: إضافة نموذج متخصص في تطوير الشخصيات والحبكة والحوار وعناصر الرواية.
- **سيد الشعر**: إضافة نموذج متخصص في كتابة وتحليل الشعر العربي بأنواعه.
- **تحليل عاطفي**: تحسين التحليل العاطفي لتوجيه النماذج الأدبية المناسبة.

### 4. تحسين معالجة الأخطاء

- **فحص توفر الخدمات**: تحسين آلية فحص توفر خدمات الذكاء الاصطناعي.
- **رسائل خطأ واضحة**: تحسين رسائل الخطأ لتوجيه المستخدم بشكل أفضل.
- **آليات احتياطية**: إضافة استجابات احتياطية في حالة عدم توفر الخدمات.

## كيفية تشغيل التطبيق

### المتطلبات الأساسية

1. **Node.js**: يجب تثبيت Node.js الإصدار 14 أو أحدث.
2. **Ollama**: لاستخدام النماذج المحلية، يجب تثبيت وتشغيل Ollama على جهازك.
3. **مفتاح OpenRouter API**: للوصول إلى النماذج السحابية، يمكنك الحصول على مفتاح API من [OpenRouter](https://openrouter.ai).

### خطوات التشغيل

1. قم بتشغيل ملف `تشغيل-التطبيق-المحسن.bat` الذي قمنا بإنشائه.
2. سيقوم الملف بالتحقق من المتطلبات وتثبيت الحزم اللازمة إذا لم تكن موجودة.
3. سيتم تشغيل التطبيق تلقائياً بعد اكتمال التحقق.

### إعداد النماذج المحلية (Ollama)

1. قم بتثبيت Ollama من [الموقع الرسمي](https://ollama.ai).
2. قم بتشغيل Ollama على جهازك.
3. قم بتنزيل النماذج التي ترغب في استخدامها باستخدام الأمر:
   ```
   ollama pull llama2
   ```
4. يمكنك تنزيل نماذج أخرى مثل:
   ```
   ollama pull codellama
   ollama pull mistral
   ```

### إعداد النماذج السحابية (OpenRouter)

1. قم بإنشاء حساب على [OpenRouter](https://openrouter.ai).
2. قم بإنشاء مفتاح API من لوحة التحكم.
3. أدخل مفتاح API في إعدادات التطبيق.

## استخدام النماذج المتخصصة

### نماذج الروايات

استخدم نموذج "مساعد الروايات" للحصول على مساعدة في:
- تطوير الشخصيات
- بناء الحبكة
- كتابة الحوار
- وصف المشاهد
- تطوير الصراع والعقدة

### نماذج الشعر

استخدم نموذج "سيد الشعر" للحصول على مساعدة في:
- كتابة الشعر العمودي
- كتابة الشعر الحر
- تحليل القافية والوزن
- تطوير الصور الشعرية
- اقتراح أفكار شعرية

## استكشاف الأخطاء وإصلاحها

### مشكلة: لا يمكن الاتصال بـ Ollama

**الحل**:
1. تأكد من تشغيل Ollama على جهازك.
2. تأكد من أن Ollama يعمل على المنفذ 11434.
3. تحقق من تنزيل النماذج التي ترغب في استخدامها.

### مشكلة: لا يمكن الاتصال بـ OpenRouter

**الحل**:
1. تأكد من صحة مفتاح API.
2. تأكد من اتصالك بالإنترنت.
3. تحقق من حالة خدمة OpenRouter على موقعهم الرسمي.

### مشكلة: النماذج المتخصصة غير متاحة

**الحل**:
1. تأكد من اتصالك بخدمة OpenRouter.
2. قد تكون بعض النماذج المتخصصة غير متاحة مؤقتاً، جرب نموذجاً آخر.
3. استخدم النماذج المحلية كبديل.

## الخطوات القادمة

نعمل حالياً على تطوير المزيد من الميزات، بما في ذلك:

1. **أدوات متخصصة للكتابة الإبداعية**:
   - أداة لتطوير هيكل الرواية
   - أداة لتحليل القافية والوزن في الشعر
   - أداة لتوليد أفكار إبداعية

2. **تحسين التكامل مع قاعدة المعرفة**:
   - استخدام الأعمال الأدبية في مجلد Knowledge base لتدريب النماذج المحلية
   - إضافة إمكانية الاقتباس من الأعمال الأدبية الكلاسيكية

3. **تطوير واجهة مستخدم متخصصة للكتابة**:
   - إضافة محرر نصوص متقدم مع تنسيق RTL
   - إضافة أدوات تحليل النص والتدقيق اللغوي

نرحب بملاحظاتكم واقتراحاتكم لتحسين التطبيق!