@echo off
chcp 65001 >nul
title 🚀 AI Chat Bot - Production Build

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🏗️ AI Chat Bot - بناء الإنتاج                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: تحديد المسار الحالي
set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

echo 🔍 فحص المتطلبات...

:: فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر: 
node --version

:: فحص npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر: 
npm --version

:: فحص التبعيات
if not exist "node_modules" (
    echo ⚠️ التبعيات غير مثبتة، جاري التثبيت...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات!
        pause
        exit /b 1
    )
)

echo.
echo 🏗️ بناء التطبيق للإنتاج...
echo ⏳ هذا قد يستغرق بضع دقائق...

:: بناء التطبيق
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق!
    echo 🔍 تحقق من الأخطاء أعلاه وحاول مرة أخرى
    pause
    exit /b 1
)

echo ✅ تم بناء التطبيق بنجاح!

echo.
echo 📦 بناء حزمة التوزيع...

:: بناء حزمة التوزيع
call npm run dist
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء حزمة التوزيع!
    echo 🔍 تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            🎉 تم البناء بنجاح!                            ║
echo ║                                                                              ║
echo ║  📁 ملفات الإنتاج: dist/                                                   ║
echo ║  📦 حزمة التوزيع: dist/                                                    ║
echo ║  🚀 جاهز للنشر والتوزيع                                                    ║
echo ║                                                                              ║
echo ║  💡 الخطوات التالية:                                                        ║
echo ║  • تجد الملفات في مجلد dist/                                               ║
echo ║  • يمكن نشرها على أي خادم ويب                                             ║
echo ║  • أو توزيعها كتطبيق مكتبي                                                 ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: فتح مجلد النتائج
echo 📂 فتح مجلد النتائج...
start explorer dist

echo 🎉 تم الانتهاء من البناء!
pause
