# دليل التثبيت - بوت الدردشة الذكي

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10 (64-bit) أو أحدث
- **المعالج**: Intel Core i3 أو AMD Ryzen 3
- **الذاكرة**: 4 GB RAM
- **التخزين**: 1 GB مساحة فارغة
- **الاتصال**: اتصال بالإنترنت

### المُوصى به
- **نظام التشغيل**: Windows 11 (64-bit)
- **المعالج**: Intel Core i5 أو AMD Ryzen 5
- **الذاكرة**: 8 GB RAM أو أكثر
- **التخزين**: 2 GB مساحة فارغة
- **الاتصال**: اتصال سريع بالإنترنت

## خطوات التثبيت

### الطريقة الأولى: التشغيل السريع (مُوصى بها)

1. **تحميل Node.js**
   - اذهب إلى [nodejs.org](https://nodejs.org/)
   - حمل النسخة LTS (الموصى بها)
   - قم بتثبيتها مع الإعدادات الافتراضية

2. **تحميل المشروع**
   - حمل ملفات المشروع
   - فك الضغط في مجلد مناسب

3. **التشغيل**
   - انقر نقراً مزدوجاً على `start.bat`
   - انتظر حتى يكتمل التثبيت والتشغيل

### الطريقة الثانية: التثبيت اليدوي

1. **تثبيت Node.js**
   ```bash
   # تحقق من التثبيت
   node --version
   npm --version
   ```

2. **تثبيت التبعيات**
   ```bash
   # في مجلد المشروع
   npm install
   ```

3. **تشغيل التطبيق**
   ```bash
   # للتطوير
   npm run dev
   
   # أو بناء التطبيق
   npm run build
   npm run dist
   ```

## إعداد مفتاح API

### الحصول على مفتاح OpenRouter

1. **إنشاء حساب**
   - اذهب إلى [openrouter.ai](https://openrouter.ai/)
   - أنشئ حساباً جديداً أو سجل دخولك

2. **الحصول على المفتاح**
   - اذهب إلى صفحة API Keys
   - أنشئ مفتاحاً جديداً
   - انسخ المفتاح

3. **إدخال المفتاح في التطبيق**
   - افتح التطبيق
   - اذهب إلى الإعدادات
   - تبويب "API"
   - أدخل المفتاح في حقل "مفتاح OpenRouter API"

### استخدام المفتاح المُدمج (مؤقت)

يمكنك استخدام المفتاح المُدمج للاختبار:
```
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
```

**تحذير**: هذا المفتاح للاختبار فقط وقد يتوقف في أي وقت.

## استكشاف أخطاء التثبيت

### خطأ: "node is not recognized"

**السبب**: Node.js غير مثبت أو غير مُضاف لمتغير PATH

**الحل**:
1. تأكد من تثبيت Node.js
2. أعد تشغيل Command Prompt
3. أو أعد تشغيل الكمبيوتر

### خطأ: "npm install failed"

**السبب**: مشكلة في الشبكة أو الصلاحيات

**الحل**:
```bash
# مسح cache
npm cache clean --force

# إعادة المحاولة
npm install

# أو استخدام yarn
npm install -g yarn
yarn install
```

### خطأ: "Permission denied"

**السبب**: عدم وجود صلاحيات كافية

**الحل**:
1. تشغيل Command Prompt كمدير
2. أو تغيير مجلد التثبيت
3. أو استخدام:
   ```bash
   npm install --no-optional
   ```

### خطأ: "Port already in use"

**السبب**: المنفذ 3000 مُستخدم

**الحل**:
```bash
# إيقاف العمليات المُستخدمة للمنفذ
netstat -ano | findstr :3000
taskkill /PID <PID_NUMBER> /F

# أو تغيير المنفذ
set PORT=3001 && npm run dev
```

### خطأ: "API key invalid"

**السبب**: مفتاح API غير صحيح

**الحل**:
1. تحقق من صحة المفتاح
2. تأكد من عدم وجود مسافات إضافية
3. جرب إنشاء مفتاح جديد

## التحقق من التثبيت

### اختبار Node.js
```bash
node --version
# يجب أن يظهر: v18.x.x أو أحدث

npm --version
# يجب أن يظهر: 9.x.x أو أحدث
```

### اختبار التطبيق
1. تشغيل `npm run dev`
2. يجب أن يفتح التطبيق تلقائياً
3. تحقق من ظهور واجهة المستخدم
4. جرب إرسال رسالة اختبار

## تحسين الأداء

### إعدادات النظام
- **إغلاق البرامج غير الضرورية**
- **تحديث تعريفات الرسوميات**
- **تفعيل تسريع الأجهزة في المتصفح**

### إعدادات التطبيق
- **اختيار نموذج أسرع** (مثل Gemini Flash)
- **تقليل عدد الرموز القصوى**
- **إغلاق المحادثات غير المُستخدمة**

## النسخ الاحتياطي

### نسخ احتياطي للإعدادات
```bash
# نسخ مجلد البيانات
xcopy "%APPDATA%\ai-chat-bot" "backup\" /E /I

# أو من داخل التطبيق
# الإعدادات → متقدم → تصدير الإعدادات
```

### استعادة النسخة الاحتياطية
```bash
# استعادة مجلد البيانات
xcopy "backup\" "%APPDATA%\ai-chat-bot\" /E /I /Y

# أو من داخل التطبيق
# الإعدادات → متقدم → استيراد الإعدادات
```

## إلغاء التثبيت

### إزالة التطبيق
1. احذف مجلد المشروع
2. احذف البيانات المحفوظة:
   ```bash
   rmdir /S "%APPDATA%\ai-chat-bot"
   ```

### إزالة Node.js (اختياري)
1. لوحة التحكم → البرامج والميزات
2. ابحث عن "Node.js"
3. انقر "إلغاء التثبيت"

## الدعم الفني

### قبل طلب المساعدة
1. تأكد من قراءة هذا الدليل
2. تحقق من [الأسئلة الشائعة](README.md#استكشاف-الأخطاء)
3. جرب إعادة تشغيل التطبيق

### معلومات مطلوبة للدعم
- إصدار Windows
- إصدار Node.js
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

### قنوات الدعم
- **GitHub Issues**: للأخطاء والاقتراحات
- **البريد الإلكتروني**: للدعم المباشر
- **المجتمع**: للنقاشات العامة

---

**ملاحظة**: هذا الدليل يُحدث باستمرار. تأكد من الحصول على أحدث نسخة.
