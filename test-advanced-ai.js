// اختبار AdvancedAIService مع النماذج المدمجة
const { AdvancedAIService } = require('./src/services/AdvancedAIService');

console.log('🧪 اختبار AdvancedAIService مع النماذج المدمجة\n');

async function testAdvancedAI() {
  try {
    // إنشاء خدمة الذكاء الاصطناعي المتقدمة
    const advancedAI = new AdvancedAIService();
    
    console.log('🚀 تهيئة الخدمة...');
    await advancedAI.reinitialize();
    
    // الحصول على النماذج المتاحة
    const availableModels = advancedAI.getAllAvailableModels();
    console.log(`📋 النماذج المتاحة: ${availableModels.length}`);
    
    // عرض النماذج المدمجة
    const appModels = availableModels.filter(model => model.id.startsWith('app/'));
    console.log(`\n🚀 النماذج المدمجة (${appModels.length}):`);
    appModels.forEach((model, index) => {
      console.log(`${index + 1}. ${model.name}`);
      console.log(`   ID: ${model.id}`);
      console.log(`   الوصف: ${model.description}`);
      console.log('');
    });
    
    // اختبار النماذج المدمجة
    const testMessages = [
      { model: 'app/chat-general', message: 'مرحبا كيف حالك؟' },
      { model: 'app/code-expert', message: 'اكتب لي function في JavaScript' },
      { model: 'app/creative-writer', message: 'اكتب لي قصة قصيرة' },
      { model: 'app/arabic-expert', message: 'ما هي قواعد النحو العربي؟' },
      { model: 'app/smart-assistant', message: 'ساعدني في تنظيم يومي' }
    ];

    console.log('🧪 اختبار النماذج المدمجة...\n');
    
    for (const test of testMessages) {
      console.log(`📤 اختبار ${test.model}:`);
      console.log(`   الرسالة: "${test.message}"`);
      
      try {
        const result = await advancedAI.sendMessage(test.message, {
          model: test.model,
          provider: 'app-models'
        });
        
        if (result.success) {
          console.log(`✅ الرد: "${result.response.substring(0, 100)}..."`);
          console.log(`   المزود: ${result.metadata?.provider || 'غير محدد'}`);
          console.log(`   النموذج: ${result.metadata?.model || test.model}`);
        } else {
          console.log(`❌ خطأ: ${result.error}`);
        }
      } catch (error) {
        console.log(`❌ استثناء: ${error.message}`);
      }
      
      console.log('');
    }
    
    // إحصائيات الخدمة
    const stats = advancedAI.getServiceStats();
    console.log('📊 إحصائيات الخدمة:');
    console.log(`   المزودين المتصلين: ${stats.connectedProviders}`);
    console.log(`   إجمالي النماذج: ${stats.totalModels}`);
    console.log(`   النماذج المحلية: ${stats.localModels}`);
    console.log(`   النماذج السحابية: ${stats.cloudModels}`);
    
    // المزودين المتصلين
    const connectedProviders = advancedAI.getConnectedProviders();
    console.log(`\n🔗 المزودين المتصلين (${connectedProviders.length}):`);
    connectedProviders.forEach((provider, index) => {
      console.log(`${index + 1}. ${provider.name} (${provider.type})`);
      console.log(`   الحالة: ${provider.status}`);
      console.log(`   النماذج: ${provider.models.length}`);
      console.log(`   الأولوية: ${provider.priority}`);
      console.log('');
    });
    
    console.log('🎉 انتهى اختبار AdvancedAIService!');
    console.log('\n💡 الخلاصة:');
    console.log('✅ النماذج المدمجة تعمل بشكل مستقل');
    console.log('✅ لا تحتاج إلى خدمات خارجية');
    console.log('✅ متاحة دائماً في التطبيق');
    console.log('✅ تقدم استجابات ذكية ومتنوعة');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  }
}

// تشغيل الاختبار
testAdvancedAI().catch(console.error);
