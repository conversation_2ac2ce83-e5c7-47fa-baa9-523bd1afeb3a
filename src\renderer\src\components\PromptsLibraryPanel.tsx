import React, { useState, useEffect } from 'react'
import { promptsLibrary, PromptTemplate, PromptCategory } from '../../services/PromptsLibrary'
import './PromptsLibraryPanel.css'

interface PromptsLibraryPanelProps {
  isOpen: boolean
  onClose: () => void
  onSelectPrompt: (prompt: PromptTemplate) => void
}

const PromptsLibraryPanel: React.FC<PromptsLibraryPanelProps> = ({
  isOpen,
  onClose,
  onSelectPrompt
}) => {
  const [prompts, setPrompts] = useState<PromptTemplate[]>([])
  const [filteredPrompts, setFilteredPrompts] = useState<PromptTemplate[]>([])
  const [selectedCategory, setSelectedCategory] = useState<PromptCategory | 'all' | 'favorites' | 'recent'>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [favorites, setFavorites] = useState<string[]>([])
  const [stats, setStats] = useState<any>(null)

  // تحميل البيانات عند فتح اللوحة
  useEffect(() => {
    if (isOpen) {
      loadPromptsData()
    }
  }, [isOpen])

  // تطبيق الفلاتر عند تغيير البحث أو الفئة
  useEffect(() => {
    applyFilters()
  }, [prompts, selectedCategory, searchQuery])

  const loadPromptsData = () => {
    const allPrompts = promptsLibrary.getAllPrompts()
    const statsData = promptsLibrary.getUsageStats()
    
    setPrompts(allPrompts)
    setStats(statsData)
    setFavorites(promptsLibrary.getFavoritePrompts().map(p => p.id))
  }

  const applyFilters = () => {
    let filtered = prompts

    // تطبيق فلتر الفئة
    if (selectedCategory === 'favorites') {
      filtered = promptsLibrary.getFavoritePrompts()
    } else if (selectedCategory === 'recent') {
      filtered = promptsLibrary.getRecentPrompts()
    } else if (selectedCategory !== 'all') {
      filtered = prompts.filter(p => p.category === selectedCategory)
    }

    // تطبيق فلتر البحث
    if (searchQuery.trim()) {
      filtered = promptsLibrary.searchPrompts(searchQuery)
      if (selectedCategory !== 'all' && selectedCategory !== 'favorites' && selectedCategory !== 'recent') {
        filtered = filtered.filter(p => p.category === selectedCategory)
      }
    }

    setFilteredPrompts(filtered)
  }

  const handlePromptSelect = (prompt: PromptTemplate) => {
    promptsLibrary.recordUsage(prompt.id)
    onSelectPrompt(prompt)
    onClose()
  }

  const toggleFavorite = (promptId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    
    if (favorites.includes(promptId)) {
      promptsLibrary.removeFromFavorites(promptId)
      setFavorites(prev => prev.filter(id => id !== promptId))
    } else {
      promptsLibrary.addToFavorites(promptId)
      setFavorites(prev => [...prev, promptId])
    }
  }

  const getCategoryIcon = (category: PromptCategory): string => {
    const icons = {
      writing: '✍️',
      coding: '💻',
      analysis: '📊',
      creative: '🎨',
      business: '💼',
      education: '🎓',
      research: '🔬',
      translation: '🌐',
      summarization: '📝',
      conversation: '💬'
    }
    return icons[category] || '📄'
  }

  const getDifficultyColor = (difficulty: string): string => {
    switch (difficulty) {
      case 'مبتدئ': return '#4ade80'
      case 'متوسط': return '#fbbf24'
      case 'متقدم': return '#f87171'
      default: return '#6b7280'
    }
  }

  if (!isOpen) return null

  return (
    <div className="prompts-library-overlay">
      <div className="prompts-library-panel">
        {/* رأس اللوحة */}
        <div className="prompts-header">
          <div className="prompts-title">
            <h2>📚 مكتبة القوالب المتقدمة</h2>
            <p>مجموعة شاملة من القوالب المحترفة لجميع احتياجاتك</p>
          </div>
          <button className="close-button" onClick={onClose}>
            ✕
          </button>
        </div>

        {/* إحصائيات سريعة */}
        {stats && (
          <div className="prompts-stats">
            <div className="stat-item">
              <span className="stat-number">{stats.totalPrompts}</span>
              <span className="stat-label">قالب متاح</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{stats.favoriteCount}</span>
              <span className="stat-label">مفضل</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{stats.recentCount}</span>
              <span className="stat-label">مستخدم مؤخراً</span>
            </div>
          </div>
        )}

        {/* شريط البحث والفلاتر */}
        <div className="prompts-controls">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 ابحث في القوالب..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="category-filters">
            <button
              className={`filter-btn ${selectedCategory === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('all')}
            >
              📋 الكل
            </button>
            <button
              className={`filter-btn ${selectedCategory === 'favorites' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('favorites')}
            >
              ⭐ المفضلة
            </button>
            <button
              className={`filter-btn ${selectedCategory === 'recent' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('recent')}
            >
              🕒 الأخيرة
            </button>
            <button
              className={`filter-btn ${selectedCategory === 'creative' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('creative')}
            >
              🎨 إبداعية
            </button>
            <button
              className={`filter-btn ${selectedCategory === 'coding' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('coding')}
            >
              💻 برمجة
            </button>
            <button
              className={`filter-btn ${selectedCategory === 'business' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('business')}
            >
              💼 أعمال
            </button>
          </div>
        </div>

        {/* قائمة القوالب */}
        <div className="prompts-list">
          {filteredPrompts.length === 0 ? (
            <div className="no-prompts">
              <div className="no-prompts-icon">🔍</div>
              <h3>لا توجد قوالب مطابقة</h3>
              <p>جرب تغيير معايير البحث أو الفئة</p>
            </div>
          ) : (
            filteredPrompts.map((prompt) => (
              <div
                key={prompt.id}
                className="prompt-card"
                onClick={() => handlePromptSelect(prompt)}
              >
                <div className="prompt-header">
                  <div className="prompt-title">
                    <span className="prompt-icon">
                      {getCategoryIcon(prompt.category)}
                    </span>
                    <h3>{prompt.name}</h3>
                  </div>
                  <div className="prompt-actions">
                    <button
                      className={`favorite-btn ${favorites.includes(prompt.id) ? 'active' : ''}`}
                      onClick={(e) => toggleFavorite(prompt.id, e)}
                      title={favorites.includes(prompt.id) ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
                    >
                      {favorites.includes(prompt.id) ? '⭐' : '☆'}
                    </button>
                  </div>
                </div>

                <p className="prompt-description">{prompt.description}</p>

                <div className="prompt-meta">
                  <div className="prompt-tags">
                    {prompt.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="tag">
                        {tag}
                      </span>
                    ))}
                    {prompt.tags.length > 3 && (
                      <span className="tag more">+{prompt.tags.length - 3}</span>
                    )}
                  </div>
                  
                  <div className="prompt-info">
                    <span
                      className="difficulty-badge"
                      style={{ backgroundColor: getDifficultyColor(prompt.difficulty) }}
                    >
                      {prompt.difficulty}
                    </span>
                    {prompt.usageCount && prompt.usageCount > 0 && (
                      <span className="usage-count">
                        📊 {prompt.usageCount}
                      </span>
                    )}
                  </div>
                </div>

                {prompt.variables && prompt.variables.length > 0 && (
                  <div className="prompt-variables">
                    <span className="variables-label">متغيرات:</span>
                    {prompt.variables.map((variable, index) => (
                      <span key={index} className="variable">
                        {variable}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* تذييل اللوحة */}
        <div className="prompts-footer">
          <div className="footer-info">
            <span>💡 اختر قالباً لبدء محادثة محترفة</span>
          </div>
          <div className="footer-actions">
            <button className="secondary-btn" onClick={onClose}>
              إلغاء
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PromptsLibraryPanel
