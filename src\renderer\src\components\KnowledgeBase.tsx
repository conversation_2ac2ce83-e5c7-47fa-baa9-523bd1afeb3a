import React, { useState, useEffect } from 'react';
import { 
  BookOpen, 
  Upload, 
  Search, 
  Plus, 
  Edit3, 
  Trash2, 
  FileText, 
  Database, 
  Brain, 
  Zap,
  Tag,
  Filter,
  Download,
  RefreshCw,
  Star,
  Eye,
  ArrowLeft
} from 'lucide-react';

interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  type: 'text' | 'file' | 'url' | 'note';
  createdAt: string;
  updatedAt: string;
  priority: 'low' | 'medium' | 'high';
  isActive: boolean;
}

interface KnowledgeStats {
  totalItems: number;
  categories: number;
  totalSize: string;
  lastUpdated: string;
}

const KnowledgeBase: React.FC = () => {
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<KnowledgeItem | null>(null);
  const [stats, setStats] = useState<KnowledgeStats>({
    totalItems: 0,
    categories: 0,
    totalSize: '0 KB',
    lastUpdated: new Date().toISOString()
  });

  const categories = [
    { value: 'all', label: '🌟 الكل', color: '#667eea' },
    { value: 'general', label: '📚 عام', color: '#4CAF50' },
    { value: 'technical', label: '⚙️ تقني', color: '#2196F3' },
    { value: 'business', label: '💼 أعمال', color: '#FF9800' },
    { value: 'education', label: '🎓 تعليم', color: '#9C27B0' },
    { value: 'health', label: '🏥 صحة', color: '#F44336' },
    { value: 'science', label: '🔬 علوم', color: '#00BCD4' },
    { value: 'arts', label: '🎨 فنون', color: '#E91E63' },
    { value: 'personal', label: '👤 شخصي', color: '#795548' }
  ];

  const priorityColors = {
    low: '#4CAF50',
    medium: '#FF9800',
    high: '#F44336'
  };

  const [newItem, setNewItem] = useState<Partial<KnowledgeItem>>({
    title: '',
    content: '',
    category: 'general',
    tags: [],
    type: 'text',
    priority: 'medium',
    isActive: true
  });

  useEffect(() => {
    loadKnowledgeBase();
  }, []);

  const loadKnowledgeBase = () => {
    // محاكاة تحميل البيانات
    const sampleData: KnowledgeItem[] = [
      {
        id: '1',
        title: 'مقدمة في الذكاء الاصطناعي',
        content: 'الذكاء الاصطناعي هو مجال في علوم الحاسوب يهدف إلى إنشاء أنظمة قادرة على أداء مهام تتطلب ذكاءً بشرياً...',
        category: 'technical',
        tags: ['AI', 'تعلم آلي', 'تقنية'],
        type: 'text',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        priority: 'high',
        isActive: true
      },
      {
        id: '2',
        title: 'أساسيات إدارة الأعمال',
        content: 'إدارة الأعمال تشمل التخطيط والتنظيم والقيادة والرقابة لتحقيق أهداف المؤسسة...',
        category: 'business',
        tags: ['إدارة', 'أعمال', 'قيادة'],
        type: 'text',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        priority: 'medium',
        isActive: true
      },
      {
        id: '3',
        title: 'تقنيات التعلم الحديثة',
        content: 'التعلم النشط والتعلم التفاعلي من أهم الطرق الحديثة في التعليم...',
        category: 'education',
        tags: ['تعليم', 'تعلم', 'طرق حديثة'],
        type: 'text',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        priority: 'medium',
        isActive: true
      }
    ];

    setKnowledgeItems(sampleData);
    updateStats(sampleData);
  };

  const updateStats = (items: KnowledgeItem[]) => {
    const uniqueCategories = new Set(items.map(item => item.category));
    const totalSize = items.reduce((acc, item) => acc + item.content.length, 0);
    
    setStats({
      totalItems: items.length,
      categories: uniqueCategories.size,
      totalSize: `${(totalSize / 1024).toFixed(1)} KB`,
      lastUpdated: new Date().toISOString()
    });
  };

  const filteredItems = knowledgeItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    const matchesTags = selectedTags.length === 0 || 
                       selectedTags.some(tag => item.tags.includes(tag));
    
    return matchesSearch && matchesCategory && matchesTags && item.isActive;
  });

  const allTags = Array.from(new Set(knowledgeItems.flatMap(item => item.tags)));

  const handleAddItem = () => {
    if (!newItem.title || !newItem.content) return;

    const item: KnowledgeItem = {
      id: Date.now().toString(),
      title: newItem.title,
      content: newItem.content,
      category: newItem.category || 'general',
      tags: newItem.tags || [],
      type: newItem.type || 'text',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      priority: newItem.priority || 'medium',
      isActive: true
    };

    const updatedItems = [...knowledgeItems, item];
    setKnowledgeItems(updatedItems);
    updateStats(updatedItems);
    setShowAddForm(false);
    setNewItem({
      title: '',
      content: '',
      category: 'general',
      tags: [],
      type: 'text',
      priority: 'medium',
      isActive: true
    });
  };

  const handleEditItem = (item: KnowledgeItem) => {
    setEditingItem(item);
    setNewItem(item);
    setShowAddForm(true);
  };

  const handleUpdateItem = () => {
    if (!editingItem || !newItem.title || !newItem.content) return;

    const updatedItems = knowledgeItems.map(item =>
      item.id === editingItem.id
        ? { ...item, ...newItem, updatedAt: new Date().toISOString() }
        : item
    );

    setKnowledgeItems(updatedItems);
    updateStats(updatedItems);
    setEditingItem(null);
    setShowAddForm(false);
    setNewItem({
      title: '',
      content: '',
      category: 'general',
      tags: [],
      type: 'text',
      priority: 'medium',
      isActive: true
    });
  };

  const handleDeleteItem = (id: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      const updatedItems = knowledgeItems.filter(item => item.id !== id);
      setKnowledgeItems(updatedItems);
      updateStats(updatedItems);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const item: KnowledgeItem = {
          id: Date.now().toString() + Math.random(),
          title: file.name,
          content: content,
          category: 'general',
          tags: ['ملف مرفوع'],
          type: 'file',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          priority: 'medium',
          isActive: true
        };

        const updatedItems = [...knowledgeItems, item];
        setKnowledgeItems(updatedItems);
        updateStats(updatedItems);
      };
      reader.readAsText(file);
    });
  };

  const exportKnowledgeBase = () => {
    const dataStr = JSON.stringify(knowledgeItems, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `knowledge-base-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  return (
    <div className="knowledge-base" dir="rtl" style={{
      padding: '20px',
      maxWidth: '1400px',
      margin: '0 auto',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
    }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
        borderRadius: '15px',
        padding: '30px',
        marginBottom: '30px',
        color: 'white',
        textAlign: 'center'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '20px'
        }}>
          <BookOpen size={32} style={{ animation: 'pulse 2s infinite' }} />
          <div>
            <h1>📚 قاعدة المعرفة</h1>
            <p>مكتبة ذكية لتنظيم وإدارة المعلومات</p>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '15px',
        marginBottom: '30px'
      }}>
        <div style={{
          background: 'white',
          padding: '20px',
          borderRadius: '10px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <Database size={24} style={{ color: '#4CAF50', marginBottom: '10px' }} />
          <h3>{stats.totalItems}</h3>
          <p>إجمالي العناصر</p>
        </div>
        <div style={{
          background: 'white',
          padding: '20px',
          borderRadius: '10px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <Tag size={24} style={{ color: '#2196F3', marginBottom: '10px' }} />
          <h3>{stats.categories}</h3>
          <p>الفئات</p>
        </div>
        <div style={{
          background: 'white',
          padding: '20px',
          borderRadius: '10px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <FileText size={24} style={{ color: '#FF9800', marginBottom: '10px' }} />
          <h3>{stats.totalSize}</h3>
          <p>حجم البيانات</p>
        </div>
        <div style={{
          background: 'white',
          padding: '20px',
          borderRadius: '10px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <RefreshCw size={24} style={{ color: '#9C27B0', marginBottom: '10px' }} />
          <h3>اليوم</h3>
          <p>آخر تحديث</p>
        </div>
      </div>

      {/* Controls */}
      <div style={{
        background: 'white',
        padding: '25px',
        borderRadius: '15px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        marginBottom: '30px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          alignItems: 'end'
        }}>
          {/* Search */}
          <div>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 600 }}>
              🔍 البحث
            </label>
            <div style={{ position: 'relative' }}>
              <Search size={20} style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                color: '#999'
              }} />
              <input
                type="text"
                placeholder="ابحث في المعرفة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px 45px 12px 12px',
                  border: '2px solid #e1e5e9',
                  borderRadius: '8px',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>

          {/* Category Filter */}
          <div>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 600 }}>
              📂 الفئة
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #e1e5e9',
                borderRadius: '8px',
                fontSize: '14px'
              }}
            >
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
          </div>

          {/* Actions */}
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={() => setShowAddForm(true)}
              style={{
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #4CAF50, #45a049)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                fontWeight: 600
              }}
            >
              <Plus size={16} />
              إضافة
            </button>
            
            <input
              type="file"
              multiple
              accept=".txt,.md,.json"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
              id="file-upload"
            />
            <label
              htmlFor="file-upload"
              style={{
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #2196F3, #1976D2)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                fontWeight: 600
              }}
            >
              <Upload size={16} />
              رفع ملف
            </label>

            <button
              onClick={exportKnowledgeBase}
              style={{
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #FF9800, #F57C00)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                fontWeight: 600
              }}
            >
              <Download size={16} />
              تصدير
            </button>
          </div>
        </div>
      </div>

      {/* Knowledge Items */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
        gap: '20px'
      }}>
        {filteredItems.map(item => (
          <div key={item.id} style={{
            background: 'white',
            borderRadius: '15px',
            padding: '20px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            border: `2px solid ${priorityColors[item.priority]}20`,
            position: 'relative'
          }}>
            {/* Priority Indicator */}
            <div style={{
              position: 'absolute',
              top: '15px',
              left: '15px',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: priorityColors[item.priority]
            }} />

            {/* Header */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '15px'
            }}>
              <div style={{ flex: 1, marginLeft: '10px' }}>
                <h3 style={{
                  margin: '0 0 8px 0',
                  color: '#333',
                  fontSize: '16px',
                  fontWeight: 600
                }}>
                  {item.title}
                </h3>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  <span style={{
                    background: categories.find(c => c.value === item.category)?.color || '#999',
                    color: 'white',
                    padding: '2px 8px',
                    borderRadius: '12px'
                  }}>
                    {categories.find(c => c.value === item.category)?.label || item.category}
                  </span>
                  <span>{new Date(item.updatedAt).toLocaleDateString('ar-SA')}</span>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '5px' }}>
                <button
                  onClick={() => handleEditItem(item)}
                  style={{
                    padding: '6px',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    borderRadius: '4px',
                    color: '#666'
                  }}
                >
                  <Edit3 size={14} />
                </button>
                <button
                  onClick={() => handleDeleteItem(item.id)}
                  style={{
                    padding: '6px',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    borderRadius: '4px',
                    color: '#F44336'
                  }}
                >
                  <Trash2 size={14} />
                </button>
              </div>
            </div>

            {/* Content */}
            <div style={{
              background: '#f8f9ff',
              padding: '15px',
              borderRadius: '8px',
              marginBottom: '15px',
              fontSize: '14px',
              lineHeight: '1.6',
              color: '#555',
              maxHeight: '120px',
              overflow: 'hidden',
              position: 'relative'
            }}>
              {item.content.substring(0, 200)}
              {item.content.length > 200 && '...'}
            </div>

            {/* Tags */}
            {item.tags.length > 0 && (
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '6px'
              }}>
                {item.tags.map((tag, index) => (
                  <span key={index} style={{
                    background: '#e3f2fd',
                    color: '#1976d2',
                    padding: '4px 8px',
                    borderRadius: '12px',
                    fontSize: '12px'
                  }}>
                    #{tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '15px',
            padding: '30px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <h2>{editingItem ? 'تعديل العنصر' : 'إضافة عنصر جديد'}</h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 600 }}>
                  العنوان
                </label>
                <input
                  type="text"
                  value={newItem.title || ''}
                  onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 600 }}>
                  المحتوى
                </label>
                <textarea
                  value={newItem.content || ''}
                  onChange={(e) => setNewItem(prev => ({ ...prev, content: e.target.value }))}
                  rows={6}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 600 }}>
                    الفئة
                  </label>
                  <select
                    value={newItem.category || 'general'}
                    onChange={(e) => setNewItem(prev => ({ ...prev, category: e.target.value }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e1e5e9',
                      borderRadius: '8px'
                    }}
                  >
                    {categories.filter(c => c.value !== 'all').map(cat => (
                      <option key={cat.value} value={cat.value}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 600 }}>
                    الأولوية
                  </label>
                  <select
                    value={newItem.priority || 'medium'}
                    onChange={(e) => setNewItem(prev => ({ ...prev, priority: e.target.value as any }))}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e1e5e9',
                      borderRadius: '8px'
                    }}
                  >
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                  </select>
                </div>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 600 }}>
                  العلامات (مفصولة بفواصل)
                </label>
                <input
                  type="text"
                  value={newItem.tags?.join(', ') || ''}
                  onChange={(e) => setNewItem(prev => ({ 
                    ...prev, 
                    tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                  }))}
                  placeholder="علامة1, علامة2, علامة3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingItem(null);
                    setNewItem({
                      title: '',
                      content: '',
                      category: 'general',
                      tags: [],
                      type: 'text',
                      priority: 'medium',
                      isActive: true
                    });
                  }}
                  style={{
                    padding: '12px 20px',
                    background: '#f5f5f5',
                    color: '#333',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  إلغاء
                </button>
                <button
                  onClick={editingItem ? handleUpdateItem : handleAddItem}
                  style={{
                    padding: '12px 20px',
                    background: 'linear-gradient(135deg, #4CAF50, #45a049)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  {editingItem ? 'تحديث' : 'إضافة'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {filteredItems.length === 0 && (
        <div style={{
          textAlign: 'center',
          padding: '60px 20px',
          color: '#666'
        }}>
          <BookOpen size={64} style={{ marginBottom: '20px', opacity: 0.3 }} />
          <h3>لا توجد عناصر</h3>
          <p>ابدأ بإضافة معلومات جديدة لقاعدة المعرفة</p>
        </div>
      )}
    </div>
  );
};

export default KnowledgeBase;
