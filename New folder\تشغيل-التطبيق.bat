@echo off
chcp 65001 >nul
title بوت الدردشة الذكي - AI Chat Bot
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    بوت الدردشة الذكي                        ║
echo ║                   AI Chat Bot v1.0                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/4] التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت!
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo    https://nodejs.org/
    echo.
    echo 💡 اختر النسخة LTS وثبتها مع الإعدادات الافتراضية
    echo.
    pause
    exit /b 1
)
echo ✅ Node.js مثبت بنجاح!

echo.
echo [2/4] التحقق من التبعيات...
if not exist "node_modules" (
    echo 📦 جاري تثبيت التبعيات... (قد يستغرق 5-10 دقائق)
    echo ⏳ يرجى الانتظار ولا تغلق النافذة...
    echo.
    npm install --no-optional --legacy-peer-deps
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات!
        echo.
        echo 🔧 جرب الحلول التالية:
        echo    1. تأكد من الاتصال بالإنترنت
        echo    2. أعد تشغيل الكمبيوتر
        echo    3. شغل الملف كمدير
        echo.
        pause
        exit /b 1
    )
) else (
    echo ✅ التبعيات مثبتة مسبقاً!
)

echo.
echo [3/4] بناء التطبيق...
echo 🔨 جاري تحضير الملفات...
npm run build:main >nul 2>&1

echo.
echo [4/4] تشغيل التطبيق...
echo 🚀 جاري فتح بوت الدردشة الذكي...
echo.
echo 💡 نصائح للاستخدام:
echo    • اكتب رسالتك واضغط Enter
echo    • يمكنك تغيير النموذج من القائمة العلوية
echo    • المحادثات تُحفظ تلقائياً
echo.

start "" npm run dev

echo ✅ تم تشغيل التطبيق بنجاح!
echo.
echo 📱 إذا لم يفتح التطبيق تلقائياً، انتظر قليلاً...
echo 🌐 أو افتح المتصفح واذهب إلى: http://localhost:3000
echo.
echo ❓ للمساعدة: اقرأ ملف README.md
echo.
pause
