@echo off
chcp 65001 >nul
title 🧠 AI Chat Bot - Intelligent Engine

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                🦙 AI Chat Bot - النماذج مفتوحة المصدر 🦙                  ║
echo ║                      يدعم Ollama و HuggingFace والخدمات السحابية           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: تحديد المسار الحالي
set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

echo 🦙 تهيئة نظام النماذج مفتوحة المصدر...
echo.

echo ✨ المميزات المتاحة:
echo    🦙 دعم كامل لنماذج Ollama المحلية
echo    🤗 تكامل مع مكتبة HuggingFace
echo    🌐 خدمات سحابية متقدمة (OpenRouter)
echo    📥 تحميل وإدارة النماذج بسهولة
echo    🎯 نماذج متخصصة للبرمجة والإبداع
echo    🔒 خصوصية كاملة مع النماذج المحلية
echo.

echo 🔍 فحص المتطلبات الأساسية...

:: فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر:
node --version

:: فحص npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر:
npm --version

:: فحص التبعيات
if not exist "node_modules" (
    echo ⚠️ التبعيات غير مثبتة، جاري التثبيت...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات!
        pause
        exit /b 1
    )
)

echo ✅ التبعيات متوفرة

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        🦙 النماذج مفتوحة المصدر المحدثة                   ║
echo ║                                                                              ║
echo ║  🌟 النماذج المُوصى بها للمبتدئين:                                        ║
echo ║  • ⚡ Phi-3 Mini - خفيف وسريع (2.3GB) - 4GB RAM                         ║
echo ║  • 🦙 Llama 3.1 8B - الأفضل للاستخدام العام (4.7GB) - 8GB RAM           ║
echo ║                                                                              ║
echo ║  💻 للمطورين والمبرمجين:                                                  ║
echo ║  • 💻 CodeLlama 13B - متخصص في البرمجة (7.3GB) - 16GB RAM               ║
echo ║                                                                              ║
echo ║  🚀 للأداء المتقدم:                                                        ║
echo ║  • 🌟 Mistral 7B - سريع ومتقدم (4.1GB) - 8GB RAM                        ║
echo ║  • 💎 Gemma 2 9B - نموذج Google الجديد (5.4GB) - 12GB RAM                ║
echo ║  • 🐉 Qwen 2.5 7B - متعدد اللغات ممتاز للعربية (4.4GB) - 8GB RAM        ║
echo ║                                                                              ║
echo ║  🔥 للأجهزة القوية (احترافي):                                             ║
echo ║  • 🦙 Llama 3.1 70B - أقوى نموذج متاح (40GB) - 64GB RAM                 ║
echo ║                                                                              ║
echo ║  ✨ المميزات الجديدة:                                                      ║
echo ║  • تصنيف ذكي للنماذج حسب الاستخدام                                       ║
echo ║  • معلومات مفصلة عن متطلبات كل نموذج                                     ║
echo ║  • واجهة محسنة لاختيار وتحميل النماذج                                     ║
echo ║  • شارات أداء لكل نموذج (🔥⭐💎✅)                                        ║
echo ║  • تحذيرات للنماذج التي تحتاج أجهزة قوية                                 ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 بدء تشغيل نظام النماذج مفتوحة المصدر...
echo 📝 ملاحظة: سيعمل النظام الاحتياطي إذا لم تكن النماذج محملة
echo 🌐 سيتم فتح التطبيق في المتصفح على: http://localhost:5173
echo.

:: تشغيل التطبيق
start "AI Chat Bot - Intelligent Engine" cmd /k "npm run dev"

:: انتظار قليل ثم فتح المتصفح
echo ⏳ انتظار بدء المحرك الذكي...
timeout /t 8 /nobreak >nul

echo 🌐 فتح التطبيق في المتصفح...
start http://localhost:5173

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        🎉 المحرك الذكي جاهز للعمل! 🎉                     ║
echo ║                                                                              ║
echo ║  🔗 رابط التطبيق: http://localhost:5173                                    ║
echo ║  🧠 المحرك: نشط ومستقل                                                    ║
echo ║  ⚡ الاستجابة: فورية                                                        ║
echo ║  🔒 الخصوصية: كاملة (لا يرسل بيانات خارجية)                             ║
echo ║                                                                              ║
echo ║  💡 نصائح للاستخدام:                                                       ║
echo ║  • جرب النماذج المختلفة من القائمة العلوية                               ║
echo ║  • استخدم مكتبة القوالب 📚 للحصول على أفضل النتائج                     ║
echo ║  • النظام يتعلم من تفاعلاتك ويحسن الاستجابات                            ║
echo ║  • يمكنك العمل بدون اتصال إنترنت بالكامل                                ║
echo ║                                                                              ║
echo ║  🎯 أمثلة للتجربة:                                                          ║
echo ║  • "اشرح لي React بطريقة مبسطة"                                          ║
echo ║  • "اكتب لي قصة قصيرة عن المستقبل"                                       ║
echo ║  • "ما هي أفضل استراتيجيات التسويق؟"                                     ║
echo ║  • "اشرح لي قانون نيوتن الثاني"                                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🎉 استمتع بتجربة الذكاء الاصطناعي المستقل!
echo 💡 لا حاجة لـ Msty أو LM Studio - كل شيء مدمج ومتقدم!
echo.
pause
