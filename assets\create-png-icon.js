// إنشاء أيقونة PNG للتطبيق
const fs = require('fs');
const { createCanvas } = require('canvas');

function createIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // تدرج الخلفية العصري
    const bgGradient = ctx.createLinearGradient(0, 0, size, size);
    bgGradient.addColorStop(0, '#667eea');
    bgGradient.addColorStop(0.5, '#764ba2');
    bgGradient.addColorStop(1, '#f093fb');
    
    // رسم الخلفية المستديرة
    const cornerRadius = size * 0.23;
    ctx.fillStyle = bgGradient;
    roundRect(ctx, size * 0.03, size * 0.03, size * 0.94, size * 0.94, cornerRadius);
    ctx.fill();
    
    // رسم الروبوت
    const robotGradient = ctx.createLinearGradient(0, 0, size, size);
    robotGradient.addColorStop(0, '#ffffff');
    robotGradient.addColorStop(1, '#f8f9fa');
    
    // رأس الروبوت
    ctx.fillStyle = robotGradient;
    roundRect(ctx, size * 0.27, size * 0.20, size * 0.45, size * 0.35, size * 0.18);
    ctx.fill();
    
    // العيون
    ctx.fillStyle = '#667eea';
    ctx.beginPath();
    ctx.arc(size * 0.37, size * 0.31, size * 0.047, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(size * 0.63, size * 0.31, size * 0.047, 0, 2 * Math.PI);
    ctx.fill();
    
    // بريق العيون
    ctx.fillStyle = 'rgba(255,255,255,0.8)';
    ctx.beginPath();
    ctx.arc(size * 0.37, size * 0.31, size * 0.023, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(size * 0.63, size * 0.31, size * 0.023, 0, 2 * Math.PI);
    ctx.fill();
    
    // الفم
    ctx.fillStyle = '#667eea';
    roundRect(ctx, size * 0.43, size * 0.41, size * 0.14, size * 0.04, size * 0.02);
    ctx.fill();
    
    // الجسم
    ctx.fillStyle = robotGradient;
    roundRect(ctx, size * 0.31, size * 0.55, size * 0.38, size * 0.27, size * 0.08);
    ctx.fill();
    
    // شاشة الصدر
    ctx.fillStyle = 'rgba(102, 126, 234, 0.8)';
    roundRect(ctx, size * 0.43, size * 0.61, size * 0.14, size * 0.09, size * 0.03);
    ctx.fill();
    
    // خطوط الشاشة
    ctx.fillStyle = 'rgba(255,255,255,0.9)';
    roundRect(ctx, size * 0.45, size * 0.63, size * 0.10, size * 0.015, size * 0.008);
    ctx.fill();
    roundRect(ctx, size * 0.45, size * 0.65, size * 0.08, size * 0.015, size * 0.008);
    ctx.fill();
    
    // شارة AI
    const aiGradient = ctx.createLinearGradient(0, 0, size, size);
    aiGradient.addColorStop(0, '#ff6b6b');
    aiGradient.addColorStop(1, '#ffa726');
    
    ctx.fillStyle = aiGradient;
    ctx.beginPath();
    ctx.arc(size * 0.78, size * 0.22, size * 0.125, 0, 2 * Math.PI);
    ctx.fill();
    
    // نص AI
    ctx.fillStyle = '#ffffff';
    ctx.font = `bold ${size * 0.08}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('AI', size * 0.78, size * 0.22);
    
    return canvas;
}

function roundRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
}

// إنشاء الأيقونات
const sizes = [16, 32, 48, 64, 128, 256, 512];

sizes.forEach(size => {
    const canvas = createIcon(size);
    const buffer = canvas.toBuffer('image/png');
    const filename = size === 256 ? 'icon.png' : `icon-${size}.png`;
    fs.writeFileSync(`assets/${filename}`, buffer);
    console.log(`✅ تم إنشاء ${filename}`);
});

console.log('🎉 تم إنشاء جميع الأيقونات بنجاح!');
