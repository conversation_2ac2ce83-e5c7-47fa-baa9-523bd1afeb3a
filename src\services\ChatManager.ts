import Store from 'electron-store'
import { v4 as uuidv4 } from 'uuid'
import CryptoJS from 'crypto-js'
import { unifiedAI, AIProvider } from './UnifiedAIService'

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  model?: string
  provider?: AIProvider
}

export interface Conversation {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: string
  updatedAt: string
  model: string
  isEncrypted: boolean
  preferredProvider?: AIProvider
}

export class ChatManager {
  private store: Store
  private encryptionKey: string

  constructor(store: Store) {
    this.store = store
    // إنشاء مفتاح تشفير فريد لكل تطبيق
    this.encryptionKey = this.store.get('encryption_key', this.generateEncryptionKey()) as string
    if (!this.store.has('encryption_key')) {
      this.store.set('encryption_key', this.encryptionKey)
    }
  }

  // إنشاء مفتاح تشفير
  private generateEncryptionKey(): string {
    return CryptoJS.lib.WordArray.random(256/8).toString()
  }

  // تشفير النص
  private encrypt(text: string): string {
    return CryptoJS.AES.encrypt(text, this.encryptionKey).toString()
  }

  // فك تشفير النص
  private decrypt(encryptedText: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedText, this.encryptionKey)
    return bytes.toString(CryptoJS.enc.Utf8)
  }

  // إنشاء محادثة جديدة
  async createConversation(
    title: string,
    model: string = 'meta-llama/llama-3.3-8b-instruct:free',
    preferredProvider?: AIProvider
  ): Promise<Conversation> {
    const conversation: Conversation = {
      id: uuidv4(),
      title: title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`,
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      model: model,
      isEncrypted: true,
      preferredProvider: preferredProvider
    }

    // حفظ المحادثة
    await this.saveConversation(conversation)
    return conversation
  }

  // إرسال رسالة باستخدام الخدمة الموحدة
  async sendMessage(
    conversationId: string,
    userMessage: string,
    forceProvider?: AIProvider
  ): Promise<{
    conversation: Conversation
    response: string
    usedProvider: AIProvider | undefined
    modelUsed: string
  }> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    // إضافة رسالة المستخدم
    const userMsg: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content: userMessage,
      timestamp: new Date().toISOString(),
      model: conversation.model
    }

    conversation.messages.push(userMsg)

    try {
      // فحص حالة الخدمات
      const servicesStatus = await unifiedAI.checkAvailability();
      let aiResponse;
      console.log('Services Status:', servicesStatus);

      if (!servicesStatus.msty && !servicesStatus.openRouter) {
        console.log('❌ All services are unavailable.');
        // إذا لم تتوفر أي خدمة، نستخدم ردًا تجريبياً مع إخطار
        const aiMsg: ChatMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: `❌ لا يمكن الاتصال بأي من خدمات الذكاء الاصطناعي.\n\nيرجى التأكد من:\n1. تشغيل برنامج Ollama على جهازك\n2. تعيين مفتاح صالح لـ OpenRouter في الإعدادات\n3. التحقق من اتصال الإنترنت\n\nحالة الخدمات:\n- Ollama: ${servicesStatus.msty ? 'متصل' : 'غير متاح'}\n- OpenRouter: ${servicesStatus.openRouter ? 'متصل' : 'غير متاح'}\n`,
          timestamp: new Date().toISOString(),
          model: 'system',
          provider: undefined
        };
        const updatedConversation = await this.addMessage(conversationId, aiMsg);
        return { response: aiMsg.content, conversation: updatedConversation, usedProvider: undefined, modelUsed: 'system' };
      } else {
        // إرسال الرسالة للذكاء الاصطناعي المتوفر
        const result = await unifiedAI.sendMessage(
          userMessage,
          conversation.model,
          conversation.messages.filter(msg => msg.role !== 'system'),
          forceProvider || conversation.preferredProvider
        );
        aiResponse = result.response;

        // إضافة رد الذكاء الاصطناعي
        const aiMsg: ChatMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: aiResponse,
          timestamp: new Date().toISOString(),
          model: result.modelUsed,
          provider: result.usedProvider
        }

        conversation.messages.push(aiMsg)
        conversation.updatedAt = new Date().toISOString()

        // حفظ المحادثة المحدثة
        await this.saveConversation(conversation)

        return {
          conversation,
          response: aiResponse,
          usedProvider: result.usedProvider,
          modelUsed: result.modelUsed
        }
      }
    } catch (error) {
      // إزالة رسالة المستخدم في حالة الخطأ
      conversation.messages.pop()
      throw error
    }
  }

  // تحديث إعدادات الذكاء الاصطناعي
  async updateAISettings(settings: {
    openRouterApiKey?: string
    preferredProvider?: AIProvider
    fallbackToOpenRouter?: boolean
  }): Promise<void> {
    // حفظ الإعدادات في التخزين
    if (settings.openRouterApiKey !== undefined) {
      this.store.set('openrouter_api_key', settings.openRouterApiKey)
    }
    if (settings.preferredProvider !== undefined) {
      this.store.set('preferred_provider', settings.preferredProvider)
    }
    if (settings.fallbackToOpenRouter !== undefined) {
      this.store.set('fallback_to_openrouter', settings.fallbackToOpenRouter)
    }

    // تحديث الخدمة الموحدة
    unifiedAI.updateConfig({
      cloudApiKey: settings.openRouterApiKey || this.store.get('openrouter_api_key') as string,
      preferredProvider: settings.preferredProvider || this.store.get('preferred_provider', 'local') as AIProvider,
      fallbackToOpenRouter: settings.fallbackToOpenRouter ?? this.store.get('fallback_to_openrouter', true) as boolean
    })
  }

  // الحصول على حالة الخدمات
  async getServicesStatus() {
    return await unifiedAI.getServicesStatus()
  }

  // الحصول على جميع النماذج المتاحة
  async getAvailableModels() {
    return await unifiedAI.getAllModels()
  }

  // تشغيل LM Studio
  async launchLMStudio(): Promise<boolean> {
    // تم نقل منطق تشغيل LM Studio إلى UnifiedAIService
    // return await this.unifiedAI.launchLMStudio()
    // بما أننا لا ندعم LM Studio حاليًا، نرجع false
    return false;
  }

  // حفظ المحادثة
  private async saveConversation(conversation: Conversation): Promise<void> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>

    // تشفير المحتوى الحساس
    const encryptedConversation = {
      ...conversation,
      messages: conversation.messages.map(msg => ({
        ...msg,
        content: conversation.isEncrypted ? this.encrypt(msg.content) : msg.content
      }))
    }

    conversations[conversation.id] = encryptedConversation
    this.store.set('conversations', conversations)
  }

  // الحصول على جميع المحادثات
  async getConversations(): Promise<Conversation[]> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>

    return Object.values(conversations)
      .map(conv => ({
        ...conv,
        messages: conv.messages.map((msg: any) => ({
          ...msg,
          content: conv.isEncrypted ? this.decrypt(msg.content) : msg.content
        }))
      }))
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
  }

  // الحصول على محادثة محددة
  async getConversation(conversationId: string): Promise<Conversation | null> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>
    const conversation = conversations[conversationId]

    if (!conversation) {
      return null
    }

    // فك تشفير الرسائل
    return {
      ...conversation,
      messages: conversation.messages.map((msg: any) => ({
        ...msg,
        content: conversation.isEncrypted ? this.decrypt(msg.content) : msg.content
      }))
    }
  }

  // إضافة رسالة لمحادثة
  async addMessage(conversationId: string, message: Omit<ChatMessage, 'id'>): Promise<Conversation> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    const newMessage: ChatMessage = {
      id: uuidv4(),
      ...message
    }

    conversation.messages.push(newMessage)
    conversation.updatedAt = new Date().toISOString()

    await this.saveConversation(conversation)
    return conversation
  }

  // تحديث عنوان المحادثة
  async updateConversationTitle(conversationId: string, newTitle: string): Promise<void> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    conversation.title = newTitle
    conversation.updatedAt = new Date().toISOString()

    await this.saveConversation(conversation)
  }

  // حذف محادثة
  async deleteConversation(conversationId: string): Promise<void> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>

    if (conversations[conversationId]) {
      delete conversations[conversationId]
      this.store.set('conversations', conversations)
    }
  }

  // حذف رسالة من محادثة
  async deleteMessage(conversationId: string, messageId: string): Promise<void> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    conversation.messages = conversation.messages.filter(msg => msg.id !== messageId)
    conversation.updatedAt = new Date().toISOString()

    await this.saveConversation(conversation)
  }

  // البحث في المحادثات
  async searchConversations(query: string): Promise<Conversation[]> {
    const conversations = await this.getConversations()
    const searchTerm = query.toLowerCase()

    return conversations.filter(conv =>
      conv.title.toLowerCase().includes(searchTerm) ||
      conv.messages.some(msg =>
        msg.content.toLowerCase().includes(searchTerm)
      )
    )
  }

  // تصدير محادثة
  async exportConversation(conversationId: string): Promise<any> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    return {
      title: conversation.title,
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
      model: conversation.model,
      messages: conversation.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      }))
    }
  }

  // حذف جميع المحادثات
  async clearAllConversations(): Promise<void> {
    this.store.set('conversations', {})
  }

  // الحصول على إحصائيات
  async getStatistics(): Promise<any> {
    const conversations = await this.getConversations()

    const totalConversations = conversations.length
    const totalMessages = conversations.reduce((sum, conv) => sum + conv.messages.length, 0)
    const modelsUsed = [...new Set(conversations.map(conv => conv.model))]

    return {
      totalConversations,
      totalMessages,
      modelsUsed,
      averageMessagesPerConversation: totalConversations > 0 ? Math.round(totalMessages / totalConversations) : 0
    }
  }

  // نسخ احتياطي للبيانات
  async createBackup(): Promise<string> {
    const conversations = this.store.get('conversations', {})
    const settings = {
      theme: this.store.get('theme'),
      language: this.store.get('language'),
      default_model: this.store.get('default_model')
    }

    const backup = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      conversations,
      settings
    }

    return JSON.stringify(backup, null, 2)
  }

  // استعادة من النسخة الاحتياطية
  async restoreFromBackup(backupData: string): Promise<void> {
    try {
      const backup = JSON.parse(backupData)

      if (backup.conversations) {
        this.store.set('conversations', backup.conversations)
      }

      if (backup.settings) {
        Object.keys(backup.settings).forEach(key => {
          if (backup.settings[key] !== undefined) {
            this.store.set(key, backup.settings[key])
          }
        })
      }
    } catch (error) {
      throw new Error('فشل في استعادة النسخة الاحتياطية: بيانات غير صحيحة')
    }
  }
}
