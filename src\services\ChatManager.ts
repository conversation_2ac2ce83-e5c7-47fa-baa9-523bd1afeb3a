import Store from 'electron-store'
import { v4 as uuidv4 } from 'uuid'
import CryptoJS from 'crypto-js'
// تم إزالة UnifiedAIService - سيتم استخدام AIService الجديدة
export type AIProvider = 'local' | 'ollama' | 'openrouter'

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  model?: string
  provider?: AIProvider
}

export interface Conversation {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: string
  updatedAt: string
  model: string
  isEncrypted: boolean
  preferredProvider?: AIProvider
}

export class ChatManager {
  private store: Store
  private encryptionKey: string

  constructor(store: Store) {
    this.store = store
    // إنشاء مفتاح تشفير فريد لكل تطبيق
    this.encryptionKey = this.store.get('encryption_key', this.generateEncryptionKey()) as string
    if (!this.store.has('encryption_key')) {
      this.store.set('encryption_key', this.encryptionKey)
    }
  }

  // إنشاء مفتاح تشفير
  private generateEncryptionKey(): string {
    return CryptoJS.lib.WordArray.random(256/8).toString()
  }

  // تشفير النص
  private encrypt(text: string): string {
    return CryptoJS.AES.encrypt(text, this.encryptionKey).toString()
  }

  // فك تشفير النص
  private decrypt(encryptedText: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedText, this.encryptionKey)
    return bytes.toString(CryptoJS.enc.Utf8)
  }

  // إنشاء محادثة جديدة
  async createConversation(
    title: string,
    model: string = 'local/general-chat',
    preferredProvider?: AIProvider
  ): Promise<Conversation> {
    const conversation: Conversation = {
      id: uuidv4(),
      title: title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`,
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      model: model,
      isEncrypted: true,
      preferredProvider: preferredProvider
    }

    // حفظ المحادثة
    await this.saveConversation(conversation)
    return conversation
  }

  // إرسال رسالة - تم تبسيطها
  async sendMessage(
    conversationId: string,
    userMessage: string
  ): Promise<{
    conversation: Conversation
    response: string
    usedProvider: string
    modelUsed: string
  }> {
    // هذه الدالة لم تعد تستخدم - تم نقل المنطق إلى main.ts
    // نحتفظ بها للتوافق مع الكود الموجود
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    return {
      conversation,
      response: 'تم نقل منطق الإرسال إلى الخدمة الجديدة',
      usedProvider: 'local',
      modelUsed: 'local/general-chat'
    }
  }

  // تحديث إعدادات الذكاء الاصطناعي - تم تبسيطها
  async updateAISettings(settings: {
    openRouterApiKey?: string
    preferredProvider?: AIProvider
    fallbackToOpenRouter?: boolean
  }): Promise<void> {
    // حفظ الإعدادات في التخزين فقط
    if (settings.openRouterApiKey !== undefined) {
      this.store.set('openrouter_api_key', settings.openRouterApiKey)
    }
    if (settings.preferredProvider !== undefined) {
      this.store.set('preferred_provider', settings.preferredProvider)
    }
    if (settings.fallbackToOpenRouter !== undefined) {
      this.store.set('fallback_to_openrouter', settings.fallbackToOpenRouter)
    }
  }

  // الحصول على حالة الخدمات - تم إزالتها
  async getServicesStatus() {
    return { local: { available: true }, cloud: { available: false } }
  }

  // الحصول على جميع النماذج المتاحة - تم إزالتها
  async getAvailableModels() {
    return []
  }

  // تشغيل LM Studio
  async launchLMStudio(): Promise<boolean> {
    // تم نقل منطق تشغيل LM Studio إلى UnifiedAIService
    // return await this.unifiedAI.launchLMStudio()
    // بما أننا لا ندعم LM Studio حاليًا، نرجع false
    return false;
  }

  // حفظ المحادثة
  private async saveConversation(conversation: Conversation): Promise<void> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>

    // تشفير المحتوى الحساس
    const encryptedConversation = {
      ...conversation,
      messages: conversation.messages.map(msg => ({
        ...msg,
        content: conversation.isEncrypted ? this.encrypt(msg.content) : msg.content
      }))
    }

    conversations[conversation.id] = encryptedConversation
    this.store.set('conversations', conversations)
  }

  // الحصول على جميع المحادثات
  async getConversations(): Promise<Conversation[]> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>

    return Object.values(conversations)
      .map(conv => ({
        ...conv,
        messages: conv.messages.map((msg: any) => ({
          ...msg,
          content: conv.isEncrypted ? this.decrypt(msg.content) : msg.content
        }))
      }))
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
  }

  // الحصول على محادثة محددة
  async getConversation(conversationId: string): Promise<Conversation | null> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>
    const conversation = conversations[conversationId]

    if (!conversation) {
      return null
    }

    // فك تشفير الرسائل
    return {
      ...conversation,
      messages: conversation.messages.map((msg: any) => ({
        ...msg,
        content: conversation.isEncrypted ? this.decrypt(msg.content) : msg.content
      }))
    }
  }

  // إضافة رسالة لمحادثة
  async addMessage(conversationId: string, message: Omit<ChatMessage, 'id'>): Promise<Conversation> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    const newMessage: ChatMessage = {
      id: uuidv4(),
      ...message
    }

    conversation.messages.push(newMessage)
    conversation.updatedAt = new Date().toISOString()

    await this.saveConversation(conversation)
    return conversation
  }

  // تحديث عنوان المحادثة
  async updateConversationTitle(conversationId: string, newTitle: string): Promise<void> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    conversation.title = newTitle
    conversation.updatedAt = new Date().toISOString()

    await this.saveConversation(conversation)
  }

  // حذف محادثة
  async deleteConversation(conversationId: string): Promise<void> {
    const conversations = this.store.get('conversations', {}) as Record<string, any>

    if (conversations[conversationId]) {
      delete conversations[conversationId]
      this.store.set('conversations', conversations)
    }
  }

  // حذف رسالة من محادثة
  async deleteMessage(conversationId: string, messageId: string): Promise<void> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    conversation.messages = conversation.messages.filter(msg => msg.id !== messageId)
    conversation.updatedAt = new Date().toISOString()

    await this.saveConversation(conversation)
  }

  // البحث في المحادثات
  async searchConversations(query: string): Promise<Conversation[]> {
    const conversations = await this.getConversations()
    const searchTerm = query.toLowerCase()

    return conversations.filter(conv =>
      conv.title.toLowerCase().includes(searchTerm) ||
      conv.messages.some(msg =>
        msg.content.toLowerCase().includes(searchTerm)
      )
    )
  }

  // تصدير محادثة
  async exportConversation(conversationId: string): Promise<any> {
    const conversation = await this.getConversation(conversationId)

    if (!conversation) {
      throw new Error('المحادثة غير موجودة')
    }

    return {
      title: conversation.title,
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
      model: conversation.model,
      messages: conversation.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      }))
    }
  }

  // حذف جميع المحادثات
  async clearAllConversations(): Promise<void> {
    this.store.set('conversations', {})
  }

  // الحصول على إحصائيات
  async getStatistics(): Promise<any> {
    const conversations = await this.getConversations()

    const totalConversations = conversations.length
    const totalMessages = conversations.reduce((sum, conv) => sum + conv.messages.length, 0)
    const modelsUsed = [...new Set(conversations.map(conv => conv.model))]

    return {
      totalConversations,
      totalMessages,
      modelsUsed,
      averageMessagesPerConversation: totalConversations > 0 ? Math.round(totalMessages / totalConversations) : 0
    }
  }

  // نسخ احتياطي للبيانات
  async createBackup(): Promise<string> {
    const conversations = this.store.get('conversations', {})
    const settings = {
      theme: this.store.get('theme'),
      language: this.store.get('language'),
      default_model: this.store.get('default_model')
    }

    const backup = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      conversations,
      settings
    }

    return JSON.stringify(backup, null, 2)
  }

  // استعادة من النسخة الاحتياطية
  async restoreFromBackup(backupData: string): Promise<void> {
    try {
      const backup = JSON.parse(backupData)

      if (backup.conversations) {
        this.store.set('conversations', backup.conversations)
      }

      if (backup.settings) {
        Object.keys(backup.settings).forEach(key => {
          if (backup.settings[key] !== undefined) {
            this.store.set(key, backup.settings[key])
          }
        })
      }
    } catch (error) {
      throw new Error('فشل في استعادة النسخة الاحتياطية: بيانات غير صحيحة')
    }
  }
}
