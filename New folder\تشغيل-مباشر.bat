@echo off
chcp 65001 >nul
title تشغيل مباشر للتطبيق

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ⚡ تشغيل مباشر للتطبيق                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 تشغيل مباشر بدون vite...
echo.

echo 🔍 فحص TypeScript...
if exist "node_modules\.bin\tsc.cmd" (
    echo ✅ TypeScript موجود
    echo 🔄 بناء الملفات...
    node_modules\.bin\tsc.cmd -p tsconfig.main.json
    if errorlevel 1 (
        echo ⚠️ تحذير: مشاكل في TypeScript، لكن سنحاول المتابعة...
    ) else (
        echo ✅ تم بناء TypeScript بنجاح
    )
) else (
    echo ⚠️ TypeScript غير موجود، سنحاول التشغيل المباشر...
)

echo.
echo 🚀 تشغيل Electron...

:: تجربة طرق مختلفة لتشغيل Electron
if exist "node_modules\.bin\electron.cmd" (
    echo ✅ تشغيل عبر .bin
    node_modules\.bin\electron.cmd .
) else if exist "node_modules\electron\dist\electron.exe" (
    echo ✅ تشغيل عبر dist
    node_modules\electron\dist\electron.exe .
) else (
    echo 🔄 محاولة تشغيل مباشر...
    npm run dev:simple
)

echo.
echo 📝 انتهى التشغيل
pause
