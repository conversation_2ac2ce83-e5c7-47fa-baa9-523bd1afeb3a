@echo off
title Build Portable AI Chat Bot

echo ========================================
echo    Build Portable - AI Chat Bot
echo ========================================
echo.

echo Step 1: Closing any running instances...
taskkill /F /IM "AI Chat Bot - *" /T 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Building the renderer...
call npm run build:renderer
if errorlevel 1 (
    echo Warning: Issues with renderer build, but continuing...
)

echo Step 3: Building the application...
echo This process may take a few minutes...
call npx electron-builder --win portable --config.extraMetadata.main="index.js" --config.files=["dist/**/*","node_modules/**/*","assets/**/*","main.js","index.js","index.html"]

echo.
echo Process completed!
echo.

if exist "release\win-unpacked" (
    echo Portable version created in release\win-unpacked folder
    echo You can run the application directly from there.
    explorer release\win-unpacked
)

echo.
pause
