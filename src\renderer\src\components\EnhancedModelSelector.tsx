import React, { useState } from 'react'
import { ChevronDown, Star, Zap, Code, Globe, Cpu, AlertTriangle, Download } from 'lucide-react'

interface ModelInfo {
  id: string
  name: string
  description: string
  size?: string
  downloadCommand?: string
  available?: boolean
  provider?: string
  category?: string
  performance?: string
  speed?: string
  memoryRequired?: string
  recommended?: boolean
  features?: string[]
  warning?: string
}

interface EnhancedModelSelectorProps {
  models: ModelInfo[]
  selectedModel: string
  onModelSelect: (modelId: string) => void
  disabled?: boolean
  onShowDownloadPanel?: () => void
}

const EnhancedModelSelector: React.FC<EnhancedModelSelectorProps> = ({
  models,
  selectedModel,
  onModelSelect,
  disabled = false,
  onShowDownloadPanel
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // تصنيف النماذج
  const categorizeModels = () => {
    const categories = {
      recommended: models.filter(m => m.recommended && m.available),
      general: models.filter(m => m.category === 'general' && m.available),
      coding: models.filter(m => m.category === 'coding' && m.available),
      lightweight: models.filter(m => m.category === 'lightweight' && m.available),
      multilingual: models.filter(m => m.category === 'multilingual' && m.available),
      professional: models.filter(m => m.category === 'professional' && m.available),
      unavailable: models.filter(m => !m.available)
    }
    return categories
  }

  const categories = categorizeModels()
  const selectedModelInfo = models.find(m => m.id === selectedModel)

  // أيقونات الفئات
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'recommended': return <Star className="w-4 h-4 text-yellow-500" />
      case 'coding': return <Code className="w-4 h-4 text-blue-500" />
      case 'lightweight': return <Zap className="w-4 h-4 text-green-500" />
      case 'multilingual': return <Globe className="w-4 h-4 text-purple-500" />
      case 'professional': return <Cpu className="w-4 h-4 text-red-500" />
      default: return null
    }
  }

  // أسماء الفئات بالعربية
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'recommended': return 'المُوصى بها'
      case 'general': return 'عامة'
      case 'coding': return 'البرمجة'
      case 'lightweight': return 'خفيفة'
      case 'multilingual': return 'متعددة اللغات'
      case 'professional': return 'احترافية'
      case 'unavailable': return 'غير محملة'
      default: return category
    }
  }

  // عرض شارة الأداء
  const getPerformanceBadge = (performance?: string) => {
    if (!performance) return null
    
    switch (performance) {
      case 'exceptional':
        return <span className="px-1 py-0.5 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 text-xs rounded">🔥</span>
      case 'very_high':
        return <span className="px-1 py-0.5 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 text-xs rounded">⭐</span>
      case 'high':
        return <span className="px-1 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs rounded">💎</span>
      case 'medium':
        return <span className="px-1 py-0.5 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs rounded">✅</span>
      default:
        return null
    }
  }

  if (disabled) {
    return (
      <div className="relative">
        <div className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400">
          غير متاح...
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* زر اختيار النموذج */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        <div className="flex items-center gap-2">
          {selectedModelInfo ? (
            <>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {selectedModelInfo.name}
              </span>
              {selectedModelInfo.recommended && (
                <Star className="w-4 h-4 text-yellow-500" />
              )}
              {selectedModelInfo.warning && (
                <AlertTriangle className="w-4 h-4 text-red-500" />
              )}
              {getPerformanceBadge(selectedModelInfo.performance)}
            </>
          ) : (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              اختر نموذج...
            </span>
          )}
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* قائمة النماذج */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          
          {/* النماذج المُوصى بها */}
          {categories.recommended.length > 0 && (
            <div className="p-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                {getCategoryIcon('recommended')}
                {getCategoryName('recommended')}
              </div>
              {categories.recommended.map((model) => (
                <button
                  key={model.id}
                  onClick={() => {
                    onModelSelect(model.id)
                    setIsOpen(false)
                  }}
                  className={`w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    selectedModel === model.id ? 'bg-blue-100 dark:bg-blue-900' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {model.name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {model.size} • {model.memoryRequired}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {getPerformanceBadge(model.performance)}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* النماذج المتاحة الأخرى */}
          {Object.entries(categories).map(([category, categoryModels]) => {
            if (category === 'recommended' || category === 'unavailable' || categoryModels.length === 0) return null
            
            return (
              <div key={category} className="p-2 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                  {getCategoryIcon(category)}
                  {getCategoryName(category)}
                </div>
                {categoryModels.map((model) => (
                  <button
                    key={model.id}
                    onClick={() => {
                      onModelSelect(model.id)
                      setIsOpen(false)
                    }}
                    className={`w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                      selectedModel === model.id ? 'bg-blue-100 dark:bg-blue-900' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {model.name}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {model.size} • {model.memoryRequired}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {getPerformanceBadge(model.performance)}
                        {model.warning && (
                          <AlertTriangle className="w-3 h-3 text-red-500" />
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )
          })}

          {/* النماذج غير المحملة */}
          {categories.unavailable.length > 0 && (
            <div className="p-2">
              <div className="flex items-center justify-between px-2 py-1">
                <div className="flex items-center gap-2 text-xs font-medium text-gray-700 dark:text-gray-300">
                  <Download className="w-4 h-4 text-orange-500" />
                  {getCategoryName('unavailable')} ({categories.unavailable.length})
                </div>
                {onShowDownloadPanel && (
                  <button
                    onClick={() => {
                      onShowDownloadPanel()
                      setIsOpen(false)
                    }}
                    className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                  >
                    📥 تحميل
                  </button>
                )}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1">
                {categories.unavailable.slice(0, 3).map(m => m.name).join(', ')}
                {categories.unavailable.length > 3 && '...'}
              </div>
            </div>
          )}

          {/* رسالة عدم وجود نماذج */}
          {models.filter(m => m.available).length === 0 && (
            <div className="p-4 text-center">
              <AlertTriangle className="w-8 h-8 text-orange-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                لا توجد نماذج محملة
              </p>
              {onShowDownloadPanel && (
                <button
                  onClick={() => {
                    onShowDownloadPanel()
                    setIsOpen(false)
                  }}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                >
                  📥 تحميل النماذج
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* معلومات النموذج المحدد */}
      {selectedModelInfo && (
        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-900 rounded-lg">
          <div className="text-xs text-gray-600 dark:text-gray-400">
            {selectedModelInfo.description}
          </div>
          {selectedModelInfo.features && (
            <div className="flex flex-wrap gap-1 mt-1">
              {selectedModelInfo.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                >
                  {feature}
                </span>
              ))}
            </div>
          )}
        </div>
      )}

      {/* طبقة الخلفية لإغلاق القائمة */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default EnhancedModelSelector
