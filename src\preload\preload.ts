import { contextBridge, ipcRenderer } from 'electron'

// تعريف واجهة API الآمنة
const electronAPI = {
  // إرسال رسالة للذكاء الاصطناعي
  sendMessage: (data: { message: string; model: string; conversationId: string }) => 
    ipcRenderer.invoke('send-message', data),
  
  // إدارة النماذج
  getAvailableModels: () => 
    ipcRenderer.invoke('get-available-models'),
  
  // إدارة المحادثات
  createConversation: (title: string) => 
    ipcRenderer.invoke('create-conversation', title),
  
  getConversations: () => 
    ipcRenderer.invoke('get-conversations'),
  
  getConversation: (conversationId: string) => 
    ipcRenderer.invoke('get-conversation', conversationId),
  
  deleteConversation: (conversationId: string) => 
    ipcRenderer.invoke('delete-conversation', conversationId),
  
  // تصدير المحادثات
  exportConversation: (data: { conversationId: string; format: string }) => 
    ipcRenderer.invoke('export-conversation', data),
  
  // إدارة الإعدادات
  saveSettings: (settings: any) => 
    ipcRenderer.invoke('save-settings', settings),
  
  getSettings: () => 
    ipcRenderer.invoke('get-settings'),
  
  // الخدمة الموحدة
  updateAISettings: (settings: any) => 
    ipcRenderer.invoke('update-ai-settings', settings),
  
  getServicesStatus: () => 
    ipcRenderer.invoke('get-services-status'),
  
  launchLMStudio: () => 
    ipcRenderer.invoke('launch-lm-studio'),
  
  // وظائف تنزيل النماذج الجديدة
  getAvailableModelsForDownload: () => 
    ipcRenderer.invoke('get-available-models-for-download'),
  
  downloadModel: (modelId: string) => 
    ipcRenderer.invoke('download-model', modelId),
  
  deleteModel: (modelId: string) => 
    ipcRenderer.invoke('delete-model', modelId),
}

// كشف واجهة API الآمنة للمحتوى المعروض
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// تعريف النوع للنافذة
declare global {
  interface Window {
    electronAPI: typeof electronAPI
  }
}

export type ElectronAPI = typeof electronAPI
