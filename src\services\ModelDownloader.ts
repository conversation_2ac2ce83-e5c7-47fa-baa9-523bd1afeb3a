import { exec } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as https from 'https';
import * as http from 'http';

export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  size: string;
  downloadUrl: string;
  localPath?: string;
  isDownloaded: boolean;
  category: 'general' | 'novels' | 'poetry' | 'cinema' | 'programming';
}

export class ModelDownloader {
  private modelsDirectory: string;
  private availableModels: ModelInfo[] = [];
  
  constructor(modelsDirectory: string) {
    this.modelsDirectory = modelsDirectory;
    
    // إنشاء مجلد النماذج إذا لم يكن موجوداً
    if (!fs.existsSync(this.modelsDirectory)) {
      fs.mkdirSync(this.modelsDirectory, { recursive: true });
    }
    
    // تهيئة قائمة النماذج المتاحة للتنزيل
    this.initializeAvailableModels();
  }
  
  // تهيئة قائمة النماذج المتاحة للتنزيل
  private initializeAvailableModels(): void {
    // نماذج عامة
    this.availableModels.push({
      id: 'llama2',
      name: 'Llama 2 7B',
      description: 'نموذج محادثة عام من Meta',
      size: '3.8GB',
      downloadUrl: 'ollama:llama2',
      isDownloaded: this.isModelDownloaded('llama2'),
      category: 'general'
    });
    
    this.availableModels.push({
      id: 'mistral',
      name: 'Mistral 7B',
      description: 'نموذج محادثة عام من Mistral AI',
      size: '4.1GB',
      downloadUrl: 'ollama:mistral',
      isDownloaded: this.isModelDownloaded('mistral'),
      category: 'general'
    });
    
    // نماذج البرمجة
    this.availableModels.push({
      id: 'codellama',
      name: 'Code Llama 7B',
      description: 'نموذج متخصص في البرمجة من Meta',
      size: '3.9GB',
      downloadUrl: 'ollama:codellama',
      isDownloaded: this.isModelDownloaded('codellama'),
      category: 'programming'
    });
    
    // نماذج الروايات
    this.availableModels.push({
      id: 'nous-hermes',
      name: 'Nous Hermes 13B',
      description: 'نموذج متخصص في الكتابة الإبداعية',
      size: '7.3GB',
      downloadUrl: 'ollama:nous-hermes',
      isDownloaded: this.isModelDownloaded('nous-hermes'),
      category: 'novels'
    });
    
    // نماذج الشعر
    this.availableModels.push({
      id: 'wizard-vicuna',
      name: 'Wizard Vicuna 13B',
      description: 'نموذج متخصص في الكتابة الأدبية والشعر',
      size: '7.2GB',
      downloadUrl: 'ollama:wizard-vicuna',
      isDownloaded: this.isModelDownloaded('wizard-vicuna'),
      category: 'poetry'
    });
  }
  
  // التحقق مما إذا كان النموذج منزلاً بالفعل
  private isModelDownloaded(modelId: string): boolean {
    // في حالة Ollama، نتحقق من وجود النموذج في قائمة النماذج المنزلة
    try {
      const ollamaModelsPath = path.join(process.env.HOME || process.env.USERPROFILE || '', '.ollama', 'models');
      if (fs.existsSync(ollamaModelsPath)) {
        const files = fs.readdirSync(ollamaModelsPath);
        return files.some(file => file.includes(modelId));
      }
    } catch (error) {
      console.error('خطأ في التحقق من النماذج المنزلة:', error);
    }
    
    return false;
  }
  
  // الحصول على قائمة النماذج المتاحة للتنزيل
  getAvailableModels(): ModelInfo[] {
    // تحديث حالة التنزيل لكل نموذج
    this.availableModels.forEach(model => {
      model.isDownloaded = this.isModelDownloaded(model.id);
    });
    
    return this.availableModels;
  }
  
  // تنزيل نموذج باستخدام Ollama
  async downloadOllamaModel(modelId: string): Promise<{
    success: boolean;
    message: string;
    progress?: number;
  }> {
    return new Promise((resolve, reject) => {
      // التحقق من وجود Ollama
      exec('ollama --version', (error) => {
        if (error) {
          resolve({
            success: false,
            message: 'لم يتم العثور على Ollama. يرجى تثبيته أولاً من https://ollama.ai'
          });
          return;
        }
        
        // تنزيل النموذج باستخدام Ollama
        const downloadProcess = exec(`ollama pull ${modelId}`);
        
        let progressMessage = '';
        let lastProgress = 0;
        
        downloadProcess.stdout?.on('data', (data) => {
          progressMessage += data.toString();
          
          // استخراج نسبة التقدم من الرسالة
          const progressMatch = progressMessage.match(/(\d+)%/);
          if (progressMatch) {
            const progress = parseInt(progressMatch[1], 10);
            if (progress > lastProgress) {
              lastProgress = progress;
              // إرسال تحديث التقدم (يمكن تنفيذه بطريقة أخرى حسب احتياجات التطبيق)
              console.log(`تنزيل ${modelId}: ${progress}%`);
            }
          }
        });
        
        downloadProcess.stderr?.on('data', (data) => {
          console.error(`خطأ في تنزيل ${modelId}:`, data.toString());
        });
        
        downloadProcess.on('close', (code) => {
          if (code === 0) {
            resolve({
              success: true,
              message: `تم تنزيل ${modelId} بنجاح`,
              progress: 100
            });
          } else {
            resolve({
              success: false,
              message: `فشل في تنزيل ${modelId}. رمز الخروج: ${code}`
            });
          }
        });
      });
    });
  }
  
  // تنزيل نموذج
  async downloadModel(modelInfo: ModelInfo): Promise<{
    success: boolean;
    message: string;
    progress?: number;
  }> {
    // التحقق مما إذا كان النموذج منزلاً بالفعل
    if (this.isModelDownloaded(modelInfo.id)) {
      return {
        success: true,
        message: `النموذج ${modelInfo.name} منزل بالفعل`,
        progress: 100
      };
    }
    
    // تنزيل النموذج حسب نوع الرابط
    if (modelInfo.downloadUrl.startsWith('ollama:')) {
      const ollamaModelId = modelInfo.downloadUrl.replace('ollama:', '');
      return await this.downloadOllamaModel(ollamaModelId);
    } else if (modelInfo.downloadUrl.startsWith('http')) {
      // تنزيل النموذج من رابط مباشر (يمكن تنفيذه حسب الحاجة)
      return {
        success: false,
        message: 'تنزيل النماذج من روابط مباشرة غير مدعوم حالياً'
      };
    } else {
      return {
        success: false,
        message: 'نوع رابط التنزيل غير مدعوم'
      };
    }
  }
  
  // حذف نموذج
  async deleteModel(modelId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    return new Promise((resolve) => {
      // حذف النموذج باستخدام Ollama
      exec(`ollama rm ${modelId}`, (error, stdout, stderr) => {
        if (error) {
          resolve({
            success: false,
            message: `فشل في حذف النموذج: ${stderr}`
          });
          return;
        }
        
        resolve({
          success: true,
          message: `تم حذف النموذج ${modelId} بنجاح`
        });
      });
    });
  }
}

// إنشاء نسخة واحدة من ModelDownloader للاستخدام في جميع أنحاء التطبيق
const defaultModelsDirectory = path.join(process.env.HOME || process.env.USERPROFILE || '', '.ai-models');
export const modelDownloader = new ModelDownloader(defaultModelsDirectory);