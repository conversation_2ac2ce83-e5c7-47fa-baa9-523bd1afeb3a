import React, { useState, useEffect } from 'react';
import { Brain, Upload, Setting<PERSON>, Play, Pause, BarChart3, <PERSON><PERSON><PERSON>, Target, Zap, ArrowLeft } from 'lucide-react';

interface TrainingConfig {
  modelName: string;
  learningRate: number;
  epochs: number;
  batchSize: number;
  specialization: string;
  personality: string;
  language: string;
}

interface TrainingProgress {
  currentEpoch: number;
  totalEpochs: number;
  loss: number;
  accuracy: number;
  status: 'idle' | 'training' | 'paused' | 'completed' | 'error';
}

const GeniusTraining: React.FC = () => {
  const [config, setConfig] = useState<TrainingConfig>({
    modelName: 'arabic-genius-v1',
    learningRate: 0.0001,
    epochs: 10,
    batchSize: 4,
    specialization: 'general',
    personality: 'helpful',
    language: 'arabic'
  });

  const [progress, setProgress] = useState<TrainingProgress>({
    currentEpoch: 0,
    totalEpochs: 0,
    loss: 0,
    accuracy: 0,
    status: 'idle'
  });

  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [logs, setLogs] = useState<string[]>([]);

  const specializations = [
    { value: 'general', label: '🌟 عام ومتنوع', desc: 'مساعد ذكي شامل' },
    { value: 'business', label: '💼 الأعمال', desc: 'خبير في إدارة الأعمال' },
    { value: 'education', label: '🎓 التعليم', desc: 'مدرس ومرشد تعليمي' },
    { value: 'creative', label: '🎨 الإبداع', desc: 'كاتب وفنان مبدع' },
    { value: 'technical', label: '⚙️ التقني', desc: 'خبير تقني ومطور' },
    { value: 'medical', label: '🏥 الطبي', desc: 'مساعد طبي متخصص' }
  ];

  const personalities = [
    { value: 'helpful', label: '😊 مساعد ودود', desc: 'مفيد ومتعاون' },
    { value: 'professional', label: '👔 مهني', desc: 'رسمي ودقيق' },
    { value: 'creative', label: '🎭 مبدع', desc: 'خيالي ومبتكر' },
    { value: 'wise', label: '🧙‍♂️ حكيم', desc: 'عميق ومتأمل' },
    { value: 'energetic', label: '⚡ نشيط', desc: 'متحمس ومحفز' },
    { value: 'calm', label: '🧘 هادئ', desc: 'صبور ومتزن' }
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const fileNames = Array.from(files).map(file => file.name);
      setUploadedFiles(prev => [...prev, ...fileNames]);
      addLog(`📁 تم رفع ${files.length} ملف: ${fileNames.join(', ')}`);
    }
  };

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  const startTraining = () => {
    if (uploadedFiles.length === 0) {
      addLog('❌ يرجى رفع ملفات التدريب أولاً');
      return;
    }

    setProgress(prev => ({ ...prev, status: 'training', totalEpochs: config.epochs }));
    addLog(`🚀 بدء التدريب العبقري للنموذج: ${config.modelName}`);
    addLog(`🎯 التخصص: ${specializations.find(s => s.value === config.specialization)?.label}`);
    addLog(`🎭 الشخصية: ${personalities.find(p => p.value === config.personality)?.label}`);

    // محاكاة التدريب
    simulateTraining();
  };

  const simulateTraining = () => {
    let epoch = 0;
    const interval = setInterval(() => {
      epoch++;
      const loss = Math.max(0.1, 2.0 - (epoch * 0.15) + (Math.random() * 0.1));
      const accuracy = Math.min(0.95, 0.3 + (epoch * 0.08) + (Math.random() * 0.05));

      setProgress(prev => ({
        ...prev,
        currentEpoch: epoch,
        loss: parseFloat(loss.toFixed(4)),
        accuracy: parseFloat(accuracy.toFixed(4))
      }));

      addLog(`📊 العصر ${epoch}/${config.epochs} - الخسارة: ${loss.toFixed(4)} - الدقة: ${(accuracy * 100).toFixed(2)}%`);

      if (epoch >= config.epochs) {
        clearInterval(interval);
        setProgress(prev => ({ ...prev, status: 'completed' }));
        addLog('🎉 تم إكمال التدريب بنجاح! النموذج أصبح عبقرياً!');
        addLog('💾 تم حفظ النموذج المدرب');
      }
    }, 2000);
  };

  const pauseTraining = () => {
    setProgress(prev => ({ ...prev, status: 'paused' }));
    addLog('⏸️ تم إيقاف التدريب مؤقتاً');
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'training': return '🔥 يتدرب...';
      case 'paused': return '⏸️ متوقف';
      case 'completed': return '✅ مكتمل';
      case 'error': return '❌ خطأ';
      default: return '⏳ في الانتظار';
    }
  };

  return (
    <div className="genius-training" dir="rtl" style={{
      padding: '20px',
      maxWidth: '1200px',
      margin: '0 auto',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
    }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '15px',
        padding: '30px',
        marginBottom: '30px',
        color: 'white',
        textAlign: 'center'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '20px'
        }}>
          <Brain size={32} style={{ animation: 'pulse 2s infinite' }} />
          <div>
            <h1>🧠 التدريب العبقري</h1>
            <p>اجعل الذكاء الاصطناعي عبقرياً مثلك!</p>
          </div>
        </div>
      </div>

      {/* Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '20px'
      }}>
        {/* Upload Section */}
        <div style={{
          background: 'white',
          borderRadius: '15px',
          padding: '25px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
          border: '1px solid #e1e5e9'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            marginBottom: '20px',
            color: '#333'
          }}>
            <Upload size={24} />
            <h3>📁 بيانات التدريب</h3>
          </div>

          <div style={{
            border: '2px dashed #667eea',
            borderRadius: '10px',
            padding: '40px',
            textAlign: 'center',
            transition: 'all 0.3s ease'
          }}>
            <input
              type="file"
              multiple
              accept=".txt,.json,.csv"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
              id="training-files"
            />
            <label htmlFor="training-files" style={{
              cursor: 'pointer',
              color: '#667eea'
            }}>
              <Sparkles size={48} />
              <p>اسحب الملفات هنا أو انقر للاختيار</p>
              <small>ملفات نصية، JSON، أو CSV</small>
            </label>
          </div>

          {uploadedFiles.length > 0 && (
            <div style={{
              marginTop: '15px',
              padding: '15px',
              background: '#f8f9ff',
              borderRadius: '8px'
            }}>
              <h4>الملفات المرفوعة ({uploadedFiles.length}):</h4>
              <ul style={{ listStyle: 'none', padding: 0, margin: '10px 0 0 0' }}>
                {uploadedFiles.map((file, index) => (
                  <li key={index} style={{ padding: '5px 0', color: '#555' }}>
                    📄 {file}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Settings Section */}
        <div style={{
          background: 'white',
          borderRadius: '15px',
          padding: '25px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
          border: '1px solid #e1e5e9'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            marginBottom: '20px',
            color: '#333'
          }}>
            <Settings size={24} />
            <h3>⚙️ إعدادات العبقرية</h3>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
              <label style={{ fontWeight: 600, color: '#333' }}>🏷️ اسم النموذج:</label>
              <input
                type="text"
                value={config.modelName}
                onChange={(e) => setConfig(prev => ({ ...prev, modelName: e.target.value }))}
                placeholder="اسم النموذج العبقري"
                style={{
                  padding: '10px',
                  border: '2px solid #e1e5e9',
                  borderRadius: '8px',
                  fontSize: '14px'
                }}
              />
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
              <label style={{ fontWeight: 600, color: '#333' }}>🎯 التخصص:</label>
              <select
                value={config.specialization}
                onChange={(e) => setConfig(prev => ({ ...prev, specialization: e.target.value }))}
                style={{
                  padding: '10px',
                  border: '2px solid #e1e5e9',
                  borderRadius: '8px',
                  fontSize: '14px'
                }}
              >
                {specializations.map(spec => (
                  <option key={spec.value} value={spec.value}>
                    {spec.label} - {spec.desc}
                  </option>
                ))}
              </select>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
              <label style={{ fontWeight: 600, color: '#333' }}>🎭 الشخصية:</label>
              <select
                value={config.personality}
                onChange={(e) => setConfig(prev => ({ ...prev, personality: e.target.value }))}
                style={{
                  padding: '10px',
                  border: '2px solid #e1e5e9',
                  borderRadius: '8px',
                  fontSize: '14px'
                }}
              >
                {personalities.map(pers => (
                  <option key={pers.value} value={pers.value}>
                    {pers.label} - {pers.desc}
                  </option>
                ))}
              </select>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                <label style={{ fontWeight: 600, color: '#333' }}>📚 عدد العصور:</label>
                <input
                  type="number"
                  value={config.epochs}
                  onChange={(e) => setConfig(prev => ({ ...prev, epochs: parseInt(e.target.value) }))}
                  min="1"
                  max="100"
                  style={{
                    padding: '10px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                <label style={{ fontWeight: 600, color: '#333' }}>⚡ معدل التعلم:</label>
                <input
                  type="number"
                  value={config.learningRate}
                  onChange={(e) => setConfig(prev => ({ ...prev, learningRate: parseFloat(e.target.value) }))}
                  step="0.0001"
                  min="0.0001"
                  max="0.01"
                  style={{
                    padding: '10px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Control Section */}
        <div style={{
          background: 'white',
          borderRadius: '15px',
          padding: '25px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
          border: '1px solid #e1e5e9',
          gridColumn: '1 / -1'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            marginBottom: '20px',
            color: '#333'
          }}>
            <Target size={24} />
            <h3>🎮 التحكم في التدريب</h3>
          </div>

          <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
            <button
              onClick={startTraining}
              disabled={progress.status === 'training'}
              style={{
                flex: 1,
                padding: '12px 20px',
                border: 'none',
                borderRadius: '8px',
                fontWeight: 600,
                cursor: progress.status === 'training' ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                background: 'linear-gradient(135deg, #4CAF50, #45a049)',
                color: 'white',
                opacity: progress.status === 'training' ? 0.5 : 1
              }}
            >
              <Play size={20} />
              بدء التدريب العبقري
            </button>
            <button
              onClick={pauseTraining}
              disabled={progress.status !== 'training'}
              style={{
                flex: 1,
                padding: '12px 20px',
                border: 'none',
                borderRadius: '8px',
                fontWeight: 600,
                cursor: progress.status !== 'training' ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                background: 'linear-gradient(135deg, #ff9800, #f57c00)',
                color: 'white',
                opacity: progress.status !== 'training' ? 0.5 : 1
              }}
            >
              <Pause size={20} />
              إيقاف مؤقت
            </button>
          </div>

          {progress.status !== 'idle' && (
            <div style={{
              background: '#f8f9ff',
              padding: '15px',
              borderRadius: '8px'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '10px',
                fontSize: '14px',
                color: '#555'
              }}>
                <span>العصر: {progress.currentEpoch}/{progress.totalEpochs}</span>
                <span>الحالة: {getStatusText(progress.status)}</span>
              </div>
              <div style={{
                width: '100%',
                height: '8px',
                background: '#e1e5e9',
                borderRadius: '4px',
                overflow: 'hidden',
                marginBottom: '10px'
              }}>
                <div style={{
                  height: '100%',
                  background: 'linear-gradient(90deg, #667eea, #764ba2)',
                  width: `${(progress.currentEpoch / progress.totalEpochs) * 100}%`,
                  transition: 'width 0.3s ease'
                }} />
              </div>
              {progress.loss > 0 && (
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '14px',
                  color: '#555'
                }}>
                  <span>📉 الخسارة: {progress.loss}</span>
                  <span>📈 الدقة: {(progress.accuracy * 100).toFixed(2)}%</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Logs Section */}
        <div style={{
          background: 'white',
          borderRadius: '15px',
          padding: '25px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
          border: '1px solid #e1e5e9',
          gridColumn: '1 / -1'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            marginBottom: '20px',
            color: '#333'
          }}>
            <BarChart3 size={24} />
            <h3>📊 سجل التدريب</h3>
          </div>

          <div style={{
            maxHeight: '300px',
            overflowY: 'auto',
            background: '#f8f9ff',
            borderRadius: '8px',
            padding: '15px'
          }}>
            {logs.length === 0 ? (
              <p style={{ textAlign: 'center', color: '#999', fontStyle: 'italic' }}>
                لا توجد سجلات بعد...
              </p>
            ) : (
              <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                {logs.map((log, index) => (
                  <li key={index} style={{
                    padding: '8px 0',
                    borderBottom: index < logs.length - 1 ? '1px solid #e1e5e9' : 'none',
                    fontFamily: "'Courier New', monospace",
                    fontSize: '13px',
                    color: '#333'
                  }}>
                    {log}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeniusTraining;
