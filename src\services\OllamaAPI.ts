import axios, { AxiosInstance } from 'axios'

export class OllamaAPI {
  private apiClient: AxiosInstance
  private baseURL: string
  private isAvailable = false

  constructor(baseURL: string = 'http://localhost:11434') {
    this.baseURL = baseURL
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000
    })
  }

  // Check Ollama availability
  async checkAvailability(): Promise<boolean> {
    try {
      console.log('🔍 فحص توفر Ollama على:', this.baseURL)
      const response = await this.apiClient.get('/api/tags', { timeout: 5000 })
      this.isAvailable = response.status === 200
      console.log('✅ Ollama متاح:', this.isAvailable)
      return this.isAvailable
    } catch (error) {
      console.log('❌ Ollama غير متاح:', (error as any).message)
      this.isAvailable = false
      return false
    }
  }

  // Get available models
  async getModels(): Promise<any[]> {
    try {
      if (!this.isAvailable) {
        await this.checkAvailability()
      }

      if (!this.isAvailable) {
        return []
      }

      console.log('📋 جلب النماذج من Ollama...')
      const response = await this.apiClient.get('/api/tags')
      const models = response.data.models || []

      console.log('📋 النماذج المتاحة في Ollama:', models.length)

      return models.map((model: any) => ({
        id: model.name,
        name: `🦙 ${model.name}`,
        description: `نموذج Ollama محلي: ${model.name} (${this.formatSize(model.size)})`,
        provider: 'ollama',
        type: 'local',
        available: true,
        size: this.formatSize(model.size),
        modified: model.modified_at,
        category: this.getModelCategory(model.name),
        performance: this.getModelPerformance(model.name),
        recommended: model.name.includes('llama3.1:8b')
      }))
    } catch (error) {
      console.error('❌ خطأ في جلب نماذج Ollama:', error)
      return []
    }
  }

  // Helper functions
  private formatSize(bytes: number): string {
    if (!bytes) return 'غير معروف'
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  private getModelCategory(modelName: string): string {
    if (modelName.includes('llama')) return 'عام'
    if (modelName.includes('code')) return 'برمجة'
    if (modelName.includes('mistral')) return 'سريع'
    if (modelName.includes('gemma')) return 'متعدد اللغات'
    return 'عام'
  }

  private getModelPerformance(modelName: string): string {
    if (modelName.includes('70b') || modelName.includes('65b')) return 'عالي'
    if (modelName.includes('13b') || modelName.includes('14b')) return 'متوسط'
    if (modelName.includes('7b') || modelName.includes('8b')) return 'جيد'
    if (modelName.includes('3b') || modelName.includes('mini')) return 'سريع'
    return 'متوسط'
  }

  // Send message to Ollama
  async sendMessage(message: string, model: string, conversationHistory: any[] = []): Promise<any> {
    try {
      if (!this.isAvailable) {
        await this.checkAvailability()
        if (!this.isAvailable) {
          throw new Error('Ollama غير متاح')
        }
      }

      console.log('📤 إرسال رسالة إلى Ollama:', model)

      // تحضير الرسائل
      const messages = [
        {
          role: 'system',
          content: 'أنت مساعد ذكي مفيد. أجب على الأسئلة باللغة العربية بطريقة واضحة ومفيدة.'
        },
        ...conversationHistory,
        {
          role: 'user',
          content: message
        }
      ]

      // إرسال الطلب إلى Ollama
      const response = await this.apiClient.post('/api/chat', {
        model: model,
        messages: messages,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          top_k: 40
        }
      })

      if (response.data?.message?.content) {
        const content = response.data.message.content.trim()
        console.log('✅ تم الرد من Ollama بنجاح')
        return {
          success: true,
          message: content,
          usage: {
            prompt_tokens: message.length,
            completion_tokens: content.length,
            total_tokens: message.length + content.length
          }
        }
      } else {
        throw new Error('لم يتم الحصول على رد صحيح من Ollama')
      }
    } catch (error) {
      console.error('❌ خطأ في إرسال رسالة إلى Ollama:', error)
      return {
        success: false,
        error: `خطأ Ollama: ${(error as any).message}`
      }
    }
  }

  // Check if Ollama is available
  get available(): boolean {
    return this.isAvailable
  }
}