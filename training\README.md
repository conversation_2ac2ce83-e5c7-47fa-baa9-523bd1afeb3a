# 🧠 التدريب العبقري - Genius AI Training

## 🎯 نظرة عامة

**التدريب العبقري** هو نظام متطور لتدريب نماذج الذكاء الاصطناعي المخصصة باستخدام تقنيات حديثة مثل LoRA و QLoRA. يمكنك إنشاء نموذج ذكي مخصص لاحتياجاتك الخاصة!

## ✨ الميزات

### 🎭 تخصصات متنوعة
- **🌟 عام ومتنوع**: مساعد ذكي شامل
- **💼 الأعمال**: خبير في إدارة الأعمال
- **🎓 التعليم**: مدرس ومرشد تعليمي
- **🎨 الإبداع**: كاتب وفنان مبدع
- **⚙️ التقني**: خبير تقني ومطور
- **🏥 الطبي**: مساعد طبي متخصص

### 🎭 شخصيات مختلفة
- **😊 مساعد ودود**: مفيد ومتعاون
- **👔 مهني**: رسمي ودقيق
- **🎭 مبدع**: خيالي ومبتكر
- **🧙‍♂️ حكيم**: عميق ومتأمل
- **⚡ نشيط**: متحمس ومحفز
- **🧘 هادئ**: صبور ومتزن

## 🚀 كيفية الاستخدام

### 1️⃣ إعداد البيئة
```bash
# تشغيل ملف الإعداد
🧠-تشغيل-التدريب-العبقري.bat
```

### 2️⃣ تحضير البيانات
- **ملفات نصية (.txt)**: نصوص عادية
- **ملفات JSON (.json)**: محادثات منظمة
- **ملفات CSV (.csv)**: جداول بيانات

#### مثال على ملف JSON:
```json
[
  {
    "conversations": [
      {"from": "human", "value": "مرحبا، كيف حالك؟"},
      {"from": "gpt", "value": "مرحبا! أنا بخير، شكراً لك. كيف يمكنني مساعدتك؟"}
    ]
  }
]
```

### 3️⃣ بدء التدريب
1. افتح التطبيق
2. انقر على "🧠 التدريب العبقري"
3. ارفع ملفات التدريب
4. اختر الإعدادات المناسبة
5. انقر "بدء التدريب العبقري"

## ⚙️ الإعدادات المتقدمة

### 📚 عدد العصور (Epochs)
- **قليل (1-3)**: تدريب سريع، تحسن محدود
- **متوسط (5-10)**: توازن جيد
- **كثير (15+)**: تحسن كبير، وقت أطول

### ⚡ معدل التعلم (Learning Rate)
- **0.0001**: آمن ومستقر
- **0.001**: أسرع لكن أقل استقراراً
- **0.01**: سريع جداً، قد يكون غير مستقر

### 📦 حجم الدفعة (Batch Size)
- **1-2**: ذاكرة قليلة
- **4-8**: متوسط
- **16+**: ذاكرة كبيرة، تدريب أسرع

## 📁 هيكل المجلدات

```
training/
├── data/           # بيانات التدريب
├── models/         # النماذج المدربة
├── configs/        # ملفات التكوين
├── logs/           # سجلات التدريب
├── LLaMA-Factory/  # مكتبة التدريب
└── genius_trainer.py # المدرب الرئيسي
```

## 🔧 استكشاف الأخطاء

### ❌ خطأ: "Python غير مثبت"
**الحل**: قم بتثبيت Python 3.8+ من [python.org](https://python.org)

### ❌ خطأ: "نفاد الذاكرة"
**الحل**: 
- قلل حجم الدفعة (Batch Size)
- قلل طول النص (Cutoff Length)
- استخدم QLoRA بدلاً من LoRA

### ❌ خطأ: "فشل في تحميل LLaMA-Factory"
**الحل**:
- تأكد من اتصال الإنترنت
- قم بتثبيت Git
- جرب التحميل اليدوي

## 💡 نصائح للحصول على أفضل النتائج

### 📊 جودة البيانات
- استخدم بيانات عالية الجودة
- تأكد من التنوع في المحتوى
- اجعل الأمثلة واضحة ومفيدة

### ⏱️ وقت التدريب
- ابدأ بعدد قليل من العصور
- راقب الخسارة والدقة
- توقف عند التحسن المطلوب

### 🎯 التخصص
- اختر تخصصاً واحداً للبداية
- استخدم بيانات متخصصة
- اختبر النموذج بانتظام

## 🌟 أمثلة متقدمة

### مساعد تعليمي
```json
{
  "conversations": [
    {"from": "human", "value": "اشرح لي قانون نيوتن الأول"},
    {"from": "gpt", "value": "قانون نيوتن الأول ينص على أن الجسم يبقى في حالة سكون أو حركة منتظمة في خط مستقيم ما لم تؤثر عليه قوة خارجية..."}
  ]
}
```

### مساعد إبداعي
```json
{
  "conversations": [
    {"from": "human", "value": "اكتب قصة قصيرة عن المستقبل"},
    {"from": "gpt", "value": "في عام 2050، استيقظت سارة على صوت مساعدها الذكي يخبرها بأن الطقس مثالي للطيران..."}
  ]
}
```

## 🤝 المساهمة

نرحب بمساهماتكم! يمكنكم:
- إضافة تخصصات جديدة
- تحسين خوارزميات التدريب
- إنشاء بيانات تدريب عالية الجودة
- تطوير واجهات جديدة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع قسم استكشاف الأخطاء
2. تحقق من سجلات التدريب
3. جرب إعدادات مختلفة
4. اطلب المساعدة من المجتمع

## 🎉 النتائج المتوقعة

بعد التدريب الناجح، ستحصل على:
- **نموذج مخصص** لاحتياجاتك
- **أداء محسن** في المجال المختار
- **شخصية مميزة** تناسب أسلوبك
- **ذكاء متطور** يتعلم من بياناتك

---

**🧠 اجعل الذكاء الاصطناعي عبقرياً مثلك!** 🚀✨
