# 🚀 دليل ملفات التشغيل السريع

## 📋 الملفات المتاحة

### 🎯 **RUN-ENHANCED-APP.bat** - الملف الرئيسي الشامل
- **الوصف**: ملف تشغيل شامل مع خيارات متعددة
- **الميزات**:
  - فحص تلقائي للمتطلبات
  - كشف الخدمات المحلية المتاحة
  - خيارات تشغيل متعددة (تطوير، إنتاج، سريع)
  - إعداد تلقائي للبيئة
  - فتح المتصفح تلقائياً

### ⚡ **START-DEV.bat** - تشغيل سريع للتطوير
- **الوصف**: تشغيل فوري في وضع التطوير
- **الاستخدام**: للمطورين الذين يريدون بدء سريع
- **الميزات**:
  - فحص سريع للمتطلبات
  - تشغيل خادم التطوير
  - فتح المتصفح تلقائياً
  - إعادة تحميل تلقائية

### 🏗️ **START-PROD.bat** - بناء الإنتاج
- **الوصف**: بناء التطبيق للإنتاج والتوزيع
- **الاستخدام**: عند الحاجة لنشر التطبيق
- **الميزات**:
  - بناء محسن للإنتاج
  - إنشاء حزمة التوزيع
  - فتح مجلد النتائج
  - تحسين الأداء

### 🔍 **CHECK-SERVICES.bat** - فحص الخدمات
- **الوصف**: فحص شامل لجميع خدمات الذكاء الاصطناعي
- **الاستخدام**: للتحقق من توفر الخدمات قبل التشغيل
- **الميزات**:
  - فحص Msty, LM Studio, Ollama
  - فحص منافذ إضافية
  - فحص الاتصال بالإنترنت
  - توصيات للإعداد
  - خيار تشغيل مباشر

---

## 🎯 كيفية الاستخدام

### للمستخدمين الجدد (موصى به):
```bash
# شغل هذا الملف أولاً
CHECK-SERVICES.bat
```

### للتطوير السريع:
```bash
# تشغيل فوري
START-DEV.bat
```

### للاستخدام الشامل:
```bash
# جميع الخيارات متاحة
RUN-ENHANCED-APP.bat
```

### لبناء الإنتاج:
```bash
# بناء للنشر
START-PROD.bat
```

---

## 🔧 ما يحدث خلف الكواليس

### فحص المتطلبات:
- ✅ Node.js (18+)
- ✅ npm
- ✅ التبعيات (node_modules)

### فحص الخدمات المحلية:
- 🟣 **Msty** (المنفذ 10000)
- 🏠 **LM Studio** (المنفذ 1234)
- 🦙 **Ollama** (المنفذ 11434)
- 🔍 **منافذ إضافية** (8080, 3000, 5000)

### فحص الخدمات السحابية:
- 🌐 **OpenRouter API**
- 🌐 **اتصال الإنترنت**

---

## 🚨 استكشاف الأخطاء

### ❌ "Node.js غير مثبت"
**الحل**: حمل وثبت Node.js من [nodejs.org](https://nodejs.org/)

### ❌ "فشل في تثبيت التبعيات"
**الحلول**:
```bash
# حذف node_modules وإعادة التثبيت
rmdir /s node_modules
npm install
```

### ❌ "لا توجد خدمات متاحة"
**الحلول**:
1. شغل Msty أو LM Studio
2. تحقق من مفتاح OpenRouter API
3. شغل `CHECK-SERVICES.bat` للتشخيص

### ❌ "فشل في البناء"
**الحلول**:
1. تأكد من عدم وجود أخطاء في الكود
2. شغل `npm run build` يدوياً لرؤية الأخطاء
3. تحقق من مساحة القرص الصلب

---

## 💡 نصائح للاستخدام الأمثل

### للتطوير:
- استخدم `START-DEV.bat` للبدء السريع
- اترك الخادم يعمل في الخلفية
- أي تغيير في الكود سيحدث التطبيق تلقائياً

### للاختبار:
- شغل `CHECK-SERVICES.bat` أولاً
- تأكد من توفر خدمة واحدة على الأقل
- جرب الخدمات المختلفة

### للنشر:
- استخدم `START-PROD.bat` لبناء الإنتاج
- تحقق من مجلد `dist/` للملفات النهائية
- اختبر البناء قبل النشر

---

## 🔄 تحديث التطبيق

### تحديث التبعيات:
```bash
npm update
```

### تحديث الكود:
```bash
git pull origin main
npm install
```

### إعادة بناء:
```bash
START-PROD.bat
```

---

## 📞 الحصول على المساعدة

### مشاكل التشغيل:
1. شغل `CHECK-SERVICES.bat` للتشخيص
2. راجع رسائل الخطأ في النافذة
3. تحقق من ملفات السجل

### مشاكل الخدمات:
1. تأكد من تشغيل الخدمة المطلوبة
2. تحقق من المنافذ المستخدمة
3. أعد تشغيل الخدمة

### مشاكل البناء:
1. تحقق من مساحة القرص
2. أعد تثبيت التبعيات
3. تحقق من إصدار Node.js

---

## 🎉 استمتع بالتطوير!

هذه الملفات مصممة لتسهيل عملية التطوير والاستخدام. اختر الملف المناسب لاحتياجاتك واستمتع بتجربة الذكاء الاصطناعي المتقدمة!

**نصيحة**: ابدأ دائماً بـ `CHECK-SERVICES.bat` إذا كنت تواجه مشاكل.
