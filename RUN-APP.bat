@echo off
title AI Chat Bot - مع دعم LM Studio و Msty

echo ========================================
echo           AI Chat Bot
echo        Desktop Application
echo     مع دعم LM Studio و Msty
echo ========================================
echo.

echo 🚀 بدء تشغيل AI Chat Bot...
echo.

:: فحص وتشغيل LM Studio إذا كان متاحاً
echo 🔍 فحص LM Studio...
if exist "C:\Program Files\LM Studio\LM Studio.exe" (
    echo ✅ تم العثور على LM Studio
    echo 🚀 تشغيل LM Studio...
    start "" "C:\Program Files\LM Studio\LM Studio.exe"
    timeout /t 3 /nobreak >nul
) else (
    echo ⚠️  LM Studio غير مثبت في المسار الافتراضي
    echo 💡 يمكنك تحميله من: https://lmstudio.ai/
)

:: فحص Msty
echo.
echo 🔍 فحص Msty...
if exist "%LOCALAPPDATA%\Programs\Msty\Msty.exe" (
    echo ✅ تم العثور على Msty
    echo 💡 يمكنك استخدام Msty كبديل لـ LM Studio
) else (
    echo ⚠️  Msty غير مثبت
    echo 💡 يمكنك تحميله من: https://msty.app/
)

echo.
echo 🔧 تشغيل التطبيق...
npx electron .
if not errorlevel 1 goto success

echo Method 2: Build and run...
npx tsc -p tsconfig.main.json
npx electron .
if not errorlevel 1 goto success

echo Method 3: Install and run...
call ULTIMATE-FIX.bat
goto end

:success
echo.
echo AI Chat Bot started successfully!
goto end

:end
echo.
pause
