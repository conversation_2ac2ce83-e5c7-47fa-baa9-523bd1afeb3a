@echo off
chcp 65001 >nul
echo ======================================================
echo تشغيل تطبيق الذكاء الاصطناعي للكتابة الإبداعية - النسخة المحسنة
echo ======================================================
echo.
echo جاري التحقق من متطلبات التشغيل...

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] لم يتم العثور على Node.js. يرجى تثبيته أولاً.
    pause
    exit /b 1
)

REM التحقق من وجود npm
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] لم يتم العثور على npm. يرجى تثبيت Node.js بشكل صحيح.
    pause
    exit /b 1
)

echo [✓] تم العثور على Node.js و npm
echo.

REM التحقق من وجود حزم التطبيق
if not exist node_modules (
    echo [!] لم يتم العثور على حزم التطبيق. جاري تثبيتها...
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo [!] فشل في تثبيت حزم التطبيق.
        pause
        exit /b 1
    )
    echo [✓] تم تثبيت حزم التطبيق بنجاح
) else (
    echo [✓] حزم التطبيق موجودة
)

echo.
echo جاري تشغيل التطبيق...
echo.
echo [i] نصائح للاستخدام الأمثل:
echo  - تأكد من تشغيل Ollama على جهازك للوصول إلى النماذج المحلية
echo  - يمكنك الحصول على مفتاح API من OpenRouter للوصول إلى النماذج السحابية
echo  - استخدم النماذج المتخصصة للروايات والشعر للحصول على أفضل النتائج
echo.
echo [i] للخروج من التطبيق، أغلق هذه النافذة أو اضغط Ctrl+C
echo.

REM تشغيل التطبيق في وضع التطوير
call npm run dev

echo.
if %ERRORLEVEL% NEQ 0 (
    echo [!] حدث خطأ أثناء تشغيل التطبيق.
) else (
    echo [✓] تم إغلاق التطبيق بنجاح.
)

pause