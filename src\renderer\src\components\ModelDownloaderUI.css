.model-downloader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  direction: rtl;
}

.model-downloader-container {
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.model-downloader-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.model-downloader-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

.model-downloader-search {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
}

.model-downloader-search input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.model-downloader-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.model-downloader-categories {
  display: flex;
  gap: 8px;
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
}

.category-button {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 16px;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-button:hover {
  background-color: var(--bg-hover);
}

.category-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.models-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.model-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-secondary);
  transition: all 0.2s ease;
}

.model-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.model-info {
  flex: 1;
  overflow: hidden;
}

.model-info h3 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.model-description {
  margin: 0 0 8px 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.model-meta {
  display: flex;
  gap: 12px;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.model-size {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

.model-category {
  background-color: rgba(0, 123, 255, 0.1);
  color: var(--primary-color);
  padding: 2px 6px;
  border-radius: 4px;
}

.model-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.download-button, .delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.download-button {
  background-color: var(--primary-color);
  color: white;
}

.download-button:hover {
  background-color: var(--primary-color-dark);
}

.delete-button {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--error-color);
}

.delete-button:hover {
  background-color: rgba(220, 53, 69, 0.2);
}

.download-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-color);
  font-size: 0.85rem;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.downloaded-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--success-color);
  color: white;
}

.error-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--error-color);
  color: white;
}

.no-models {
  padding: 32px;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

.model-downloader-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--border-color);
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.model-downloader-footer a {
  color: var(--primary-color);
  text-decoration: none;
  margin-right: 8px;
}

.model-downloader-footer a:hover {
  text-decoration: underline;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 600px) {
  .model-downloader-container {
    width: 95%;
    max-height: 90vh;
  }
  
  .model-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .model-actions {
    margin-top: 12px;
    align-self: flex-end;
  }
}