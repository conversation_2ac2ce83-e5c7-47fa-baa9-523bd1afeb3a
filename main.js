const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const { join } = require('path');
const Store = require('electron-store');

// إعداد التخزين المحلي
const store = new Store();

// متغيرات عامة
let mainWindow = null;

const isDev = process.env.NODE_ENV === 'development';

function createWindow() {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
    },
    titleBarStyle: 'default',
    show: false,
  });

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    // تحميل صفحة بسيطة للاختبار
    try {
      // First try to load from the dist directory
      const indexPath = join(__dirname, 'dist', 'renderer', 'index.html');
      console.log('Trying to load from:', indexPath);
      mainWindow.loadFile(indexPath);
    } catch (error) {
      console.error('Error loading index.html:', error);
      // Fallback to data URL
      mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI Chat Bot</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
          }
          .container {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
          }
          .title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
          }
          .subtitle {
            font-size: 24px;
            margin-bottom: 30px;
            opacity: 0.9;
          }
          .status {
            font-size: 18px;
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
          }
          .success {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4caf50;
          }
          .robot {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
          }
          @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="robot">🤖</div>
          <h1 class="title">AI Chat Bot</h1>
          <p class="subtitle">بوت الدردشة الذكي</p>
          <div class="status success">
            ✅ تم تشغيل التطبيق بنجاح!
          </div>
          <div class="status">
            🎉 مبروك! التطبيق يعمل الآن على سطح المكتب
          </div>
          <div class="status">
            💡 هذه نسخة تجريبية - سيتم تطوير الواجهة الكاملة قريباً
          </div>
        </div>
      </body>
      </html>
    `));
    }
  }

  // إظهار النافذة عند الاستعداد
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
    console.log('✅ AI Chat Bot started successfully!');
  });

  // إغلاق التطبيق عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // منع التنقل إلى روابط خارجية
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// تهيئة التطبيق
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// إغلاق التطبيق على جميع المنصات عدا macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

console.log('🚀 Starting AI Chat Bot...');
