@echo off
chcp 65001 >nul
title 🧠 إعداد التدريب العبقري - AI Chat Bot

echo.
echo ========================================
echo      🧠 إعداد التدريب العبقري
echo      AI Chat Bot - النسخة العبقرية
echo ========================================
echo.

echo 📋 الخطوة 1: إنشاء مجلد التدريب...
if not exist "training" mkdir training
if not exist "training\data" mkdir training\data
if not exist "training\models" mkdir training\models
if not exist "training\configs" mkdir training\configs
if not exist "training\logs" mkdir training\logs

echo 📋 الخطوة 2: تحميل LLaMA-Factory...
cd training
if not exist "LLaMA-Factory" (
    echo 🔄 جاري تحميل LLaMA-Factory...
    git clone --depth 1 https://github.com/hiyouga/LLaMA-Factory.git
    if errorlevel 1 (
        echo ❌ فشل في تحميل LLaMA-Factory
        echo 💡 تأكد من تثبيت Git أولاً
        pause
        exit /b 1
    )
) else (
    echo ✅ LLaMA-Factory موجود مسبقاً
)

echo 📋 الخطوة 3: إعداد Python Environment...
cd LLaMA-Factory

echo 🔄 تثبيت المتطلبات الأساسية...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت PyTorch
)

echo 🔄 تثبيت LLaMA-Factory...
pip install -e ".[torch,metrics]" --no-build-isolation
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت LLaMA-Factory
)

echo 📋 الخطوة 4: إنشاء ملفات التكوين العبقرية...
cd ..\..\

echo 🎉 تم إعداد التدريب العبقري بنجاح!
echo.
echo 🚀 الخطوات التالية:
echo    1. إضافة بيانات التدريب في مجلد training\data
echo    2. تشغيل واجهة التدريب من التطبيق
echo    3. بدء تدريب النموذج العبقري
echo.
echo ========================================
echo      🧠 جاهز للعبقرية!
echo ========================================
pause
