/**
 * محرك الذكاء الاصطناعي العاطفي العربي
 * يحلل المشاعر ويقترح المحتوى المناسب
 */

export interface EmotionalState {
  primary: string // المشاعر الأساسية
  secondary: string[] // المشاعر الثانوية
  _intensity: number // شدة المشاعر (0-1)
  confidence: number // مستوى الثقة في التحليل
  suggestions: string[] // اقتراحات للتفاعل
}

export interface EmotionalResponse {
  tone: string // نبرة الاستجابة
  approach: string // طريقة التعامل
  literaryStyle: string // الأسلوب الأدبي المناسب
  recommendedModel: string // النموذج الأدبي المقترح
}

export class EmotionalAI {
  // قاموس المشاعر العربية
  private emotionalLexicon = {
    // مشاعر إيجابية
    happiness: {
      keywords: [
        'سعيد', 'فرح', 'مبسوط', 'مسرور', 'بهيج', 'مرح', 'منشرح',
        'فرحان', 'مبتهج', 'سرور', 'بهجة', 'انشراح', 'حبور', 'غبطة',
        'رائع', 'جميل', 'ممتاز', 'عظيم', 'مذهل', 'رائع', 'مدهش'
      ],
      intensity: 0.8,
      response: 'supportive_celebration'
    },

    love: {
      keywords: [
        'حب', 'عشق', 'غرام', 'هيام', 'وله', 'شوق', 'حنين',
        'أحبك', 'عاشق', 'محب', 'متيم', 'مفتون', 'مولع',
        'قلبي', 'روحي', 'حبيبي', 'عزيزي', 'غالي'
      ],
      intensity: 0.9,
      response: 'romantic_poetic'
    },

    excitement: {
      keywords: [
        'متحمس', 'متشوق', 'متلهف', 'مندفع', 'نشيط', 'حيوي',
        'رائع', 'مذهل', 'عظيم', 'ممتاز', 'خرافي', 'أسطوري',
        'يا الله', 'واو', 'مش معقول', 'لا يصدق'
      ],
      intensity: 0.7,
      response: 'energetic_motivational'
    },

    // مشاعر سلبية
    sadness: {
      keywords: [
        'حزين', 'مكتئب', 'محبط', 'يائس', 'منكسر', 'متألم', 'موجوع',
        'حزن', 'كآبة', 'اكتئاب', 'يأس', 'ألم', 'وجع', 'كسرة',
        'زعلان', 'مقهور', 'مجروح', 'متضايق', 'منكسر الخاطر'
      ],
      intensity: 0.8,
      response: 'comforting_healing'
    },

    anger: {
      keywords: [
        'غاضب', 'زعلان', 'متضايق', 'منفعل', 'ثائر', 'محتد', 'مستاء',
        'غضب', 'غيظ', 'سخط', 'احتداد', 'انفعال', 'ثورة',
        'مش عاجبني', 'مقرف', 'مزعج', 'محبط', 'مستفز'
      ],
      intensity: 0.7,
      response: 'calming_wisdom'
    },

    anxiety: {
      keywords: [
        'قلق', 'خائف', 'متوتر', 'مضطرب', 'مرتبك', 'مشوش', 'مذعور',
        'قلق', 'خوف', 'توتر', 'اضطراب', 'ارتباك', 'ذعر', 'هلع',
        'مش عارف', 'محتار', 'مش متأكد', 'خايف', 'مرعوب'
      ],
      intensity: 0.6,
      response: 'reassuring_supportive'
    },

    loneliness: {
      keywords: [
        'وحيد', 'منعزل', 'مهجور', 'متروك', 'مفارق', 'غريب',
        'وحدة', 'عزلة', 'هجر', 'فراق', 'غربة', 'انطوائية',
        'لوحدي', 'مفيش حد', 'مهجور', 'منسي', 'متروك'
      ],
      intensity: 0.7,
      response: 'companionship_warmth'
    },

    // مشاعر مختلطة
    nostalgia: {
      keywords: [
        'حنين', 'شوق', 'ذكريات', 'ماضي', 'أيام', 'زمان', 'كان',
        'افتقاد', 'اشتياق', 'ذكرى', 'تذكر', 'استرجاع',
        'زمان كان', 'أيام زمان', 'الماضي الجميل', 'ذكريات حلوة'
      ],
      intensity: 0.6,
      response: 'nostalgic_poetic'
    },

    hope: {
      keywords: [
        'أمل', 'رجاء', 'تفاؤل', 'ثقة', 'إيمان', 'يقين', 'طموح',
        'متفائل', 'واثق', 'مؤمن', 'طامح', 'راجي', 'مأمول',
        'إن شاء الله', 'ربنا يكرم', 'هيبقى أحسن', 'الخير جاي'
      ],
      intensity: 0.8,
      response: 'inspiring_uplifting'
    },

    // مشاعر متعلقة بالإبداع والسينما
    creativity: {
      keywords: [
        'إبداع', 'فن', 'سينما', 'فيلم', 'مسرحية', 'دراما', 'سيناريو',
        'مبدع', 'فنان', 'ممثل', 'مخرج', 'كاتب', 'مؤلف',
        'قصة', 'حوار', 'شخصية', 'مشهد', 'لقطة', 'مونتاج'
      ],
      intensity: 0.7,
      response: 'creative_inspiring'
    },

    drama: {
      keywords: [
        'دراما', 'مأساة', 'تراجيديا', 'صراع', 'توتر', 'عاطفة',
        'درامي', 'مؤثر', 'مبكي', 'محزن', 'مفجع', 'صادم',
        'مسلسل', 'مسرحية', 'عرض', 'أداء', 'تمثيل'
      ],
      intensity: 0.8,
      response: 'dramatic_powerful'
    }
  }

  // تحليل المشاعر من النص
  analyzeEmotion(text: string): EmotionalState {
    const words = this.tokenizeArabicText(text)
    const emotions: { [key: string]: number } = {}
    let totalIntensity = 0
    let matchCount = 0

    // تحليل كل كلمة
    for (const word of words) {
      for (const [emotion, data] of Object.entries(this.emotionalLexicon)) {
        if (data.keywords.some(keyword => word.includes(keyword) || keyword.includes(word))) {
          emotions[emotion] = (emotions[emotion] || 0) + data.intensity
          totalIntensity += data.intensity
          matchCount++
        }
      }
    }

    // تحديد المشاعر الأساسية
    const sortedEmotions = Object.entries(emotions)
      .sort(([,a], [,b]) => b - a)
      .map(([emotion]) => emotion)

    const primary = sortedEmotions[0] || 'neutral'
    const secondary = sortedEmotions.slice(1, 3)
    const _intensity = matchCount > 0 ? totalIntensity / matchCount : 0.5
    const confidence = Math.min(matchCount / words.length, 1)

    return {
      primary,
      secondary,
      _intensity,
      confidence,
      suggestions: this.generateSuggestions(primary, _intensity)
    }
  }

  // تقسيم النص العربي
  private tokenizeArabicText(text: string): string[] {
    return text
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1)
      .map(word => word.trim())
  }

  // إنشاء اقتراحات حسب المشاعر
  private generateSuggestions(emotion: string, _intensity: number): string[] {
    const suggestions: { [key: string]: string[] } = {
      happiness: [
        'شارك فرحتك مع الآخرين',
        'اكتب عن هذه اللحظة الجميلة',
        'احتفل بهذا الإنجاز'
      ],
      love: [
        'عبر عن مشاعرك بكلمات جميلة',
        'اكتب رسالة حب',
        'شارك هذا الحب مع العالم'
      ],
      sadness: [
        'تذكر أن الحزن مؤقت',
        'ابحث عن الجمال في الألم',
        'تحدث مع صديق مقرب'
      ],
      anger: [
        'خذ نفساً عميقاً',
        'فكر في الحلول بدلاً من المشاكل',
        'مارس الرياضة لتفريغ الطاقة'
      ],
      anxiety: [
        'ركز على اللحظة الحالية',
        'تذكر نجاحاتك السابقة',
        'تحدث عن مخاوفك'
      ],
      loneliness: [
        'تواصل مع الأصدقاء',
        'اكتشف هوايات جديدة',
        'تذكر أنك لست وحيداً'
      ],
      nostalgia: [
        'اكتب عن ذكرياتك الجميلة',
        'تواصل مع أصدقاء الماضي',
        'احتفظ بالذكريات الطيبة'
      ],
      hope: [
        'ضع خططاً للمستقبل',
        'شارك تفاؤلك مع الآخرين',
        'اعمل نحو أهدافك'
      ]
    }

    return suggestions[emotion] || ['تذكر أن كل شيء سيكون بخير']
  }

  // تحديد الاستجابة العاطفية المناسبة
  getEmotionalResponse(emotionalState: EmotionalState): EmotionalResponse {
    const responseMap: { [key: string]: EmotionalResponse } = {
      happiness: {
        tone: 'celebratory',
        approach: 'amplify_joy',
        literaryStyle: 'uplifting_poetic',
        recommendedModel: 'literary/naguib-mahfouz:custom'
      },
      love: {
        tone: 'romantic',
        approach: 'poetic_expression',
        literaryStyle: 'romantic_passionate',
        recommendedModel: 'literary/ahlam-mosteghanemi:custom'
      },
      excitement: {
        tone: 'energetic',
        approach: 'motivational',
        literaryStyle: 'dynamic_inspiring',
        recommendedModel: 'literary/ahmed-fouad-negm:custom'
      },
      sadness: {
        tone: 'gentle',
        approach: 'comforting_healing',
        literaryStyle: 'soothing_wise',
        recommendedModel: 'literary/naguib-mahfouz:custom'
      },
      anger: {
        tone: 'calm',
        approach: 'wisdom_perspective',
        literaryStyle: 'philosophical_calming',
        recommendedModel: 'literary/sonallah-ibrahim:custom'
      },
      anxiety: {
        tone: 'reassuring',
        approach: 'supportive_grounding',
        literaryStyle: 'peaceful_stabilizing',
        recommendedModel: 'literary/radwa-ashour:custom'
      },
      loneliness: {
        tone: 'warm',
        approach: 'companionship',
        literaryStyle: 'intimate_connecting',
        recommendedModel: 'literary/ihsan-abdel-quddous:custom'
      },
      nostalgia: {
        tone: 'wistful',
        approach: 'memory_celebration',
        literaryStyle: 'nostalgic_beautiful',
        recommendedModel: 'literary/abdel-rahman-abnoudy:custom'
      },
      hope: {
        tone: 'inspiring',
        approach: 'future_building',
        literaryStyle: 'optimistic_empowering',
        recommendedModel: 'literary/ahmed-fouad-negm:custom'
      },

      // مشاعر متعلقة بالسينما والدراما
      creativity: {
        tone: 'artistic',
        approach: 'creative_expression',
        literaryStyle: 'cinematic_visual',
        recommendedModel: 'cinema/youssef-chahine:custom'
      },

      drama: {
        tone: 'dramatic',
        approach: 'emotional_intensity',
        literaryStyle: 'theatrical_powerful',
        recommendedModel: 'cinema/salah-abu-seif:custom'
      }
    }

    return responseMap[emotionalState.primary] || {
      tone: 'neutral',
      approach: 'balanced',
      literaryStyle: 'conversational',
      recommendedModel: 'meta-llama/llama-3.3-8b-instruct:free'
    }
  }

  // إنشاء رسالة نظامية عاطفية
  generateEmotionalSystemMessage(
      emotionalState: EmotionalState,
      _response: EmotionalResponse,
      originalModel: string
    ): string {
    const basePrompts: { [key: string]: string } = {
      happiness: `المستخدم يشعر بالسعادة والفرح. تفاعل معه بحماس وشارك فرحته. اكتب بأسلوب مبهج ومحتفل يعكس هذه المشاعر الإيجابية.`,

      love: `المستخدم يتحدث عن الحب والمشاعر الرومانسية. تفاعل معه بحساسية وشاعرية. استخدم لغة جميلة ومعبرة تلامس القلب.`,

      excitement: `المستخدم متحمس ومتشوق. تفاعل معه بطاقة إيجابية وحماس. استخدم أسلوباً محفزاً ومشجعاً يزيد من حماسه.`,

      sadness: `المستخدم يشعر بالحزن أو الألم. تعامل معه بلطف وحنان. قدم له الدعم والمواساة بكلمات مهدئة وحكيمة. ذكره بأن الحزن مؤقت وأن هناك أملاً.`,

      anger: `المستخدم يشعر بالغضب أو الانزعاج. تعامل معه بهدوء وحكمة. ساعده على رؤية الأمور من منظور مختلف وقدم له نصائح للتهدئة.`,

      anxiety: `المستخدم يشعر بالقلق أو التوتر. تعامل معه بطمأنينة ودعم. ساعده على الشعور بالأمان وقدم له كلمات مطمئنة ومهدئة.`,

      loneliness: `المستخدم يشعر بالوحدة. كن له رفيقاً ودوداً. أشعره بأنه ليس وحيداً وأن هناك من يفهمه ويهتم به.`,

      nostalgia: `المستخدم يشعر بالحنين والشوق للماضي. تفاعل معه بفهم وشاعرية. ساعده على الاحتفال بذكرياته الجميلة بطريقة إيجابية.`,

      hope: `المستخدم يشعر بالأمل والتفاؤل. شجعه وادعم تفاؤله. استخدم كلمات ملهمة ومحفزة تقوي إيمانه بالمستقبل.`
    }

    const emotionalPrompt = basePrompts[emotionalState.primary] ||
      `تفاعل مع المستخدم بطريقة متفهمة ومناسبة لحالته النفسية.`

    // إضافة التوجيه الأدبي حسب النموذج
    let literaryGuidance = ''
    if (originalModel.includes('literary/')) {
      const writerName = this.getWriterName(originalModel)
      literaryGuidance = ` اكتب بأسلوب ${writerName} مع مراعاة الحالة العاطفية للمستخدم.`
    }

    return `${emotionalPrompt}${literaryGuidance} كن متعاطفاً وداعماً، واستخدم الأدب والشعر لتخفيف الألم أو تعزيز الفرح حسب الحاجة.`
  }

  // استخراج اسم الكاتب من معرف النموذج
  private getWriterName(modelId: string): string {
    const writerMap: { [key: string]: string } = {
      'literary/naguib-mahfouz:custom': 'نجيب محفوظ',
      'literary/sonallah-ibrahim:custom': 'صنع الله إبراهيم',
      'literary/ahlam-mosteghanemi:custom': 'أحلام مستغانمي',
      'literary/ihsan-abdel-quddous:custom': 'إحسان عبد القدوس',
      'literary/radwa-ashour:custom': 'رضوى عاشور',
      'literary/abdel-rahman-abnoudy:custom': 'عبد الرحمن الأبنودي',
      'literary/ahmed-fouad-negm:custom': 'أحمد فؤاد نجم'
    }

    return writerMap[modelId] || 'الكاتب'
  }

  // اقتراح النموذج الأدبي المناسب للحالة العاطفية
  suggestBestModel(emotionalState: EmotionalState): string {
    const response = this.getEmotionalResponse(emotionalState)
    return response.recommendedModel
  }

  // تحليل شامل للنص مع التوصيات
  getFullEmotionalAnalysis(text: string, currentModel: string) {
    const emotionalState = this.analyzeEmotion(text)
    const response = this.getEmotionalResponse(emotionalState)
    const systemMessage = this.generateEmotionalSystemMessage(emotionalState, response, currentModel)
    const suggestedModel = this.suggestBestModel(emotionalState)

    return {
      emotionalState,
      response,
      systemMessage,
      suggestedModel,
      shouldSwitchModel: suggestedModel !== currentModel && emotionalState.confidence > 0.6
    }
  }
}

// إنشاء مثيل واحد للاستخدام العام
export const emotionalAI = new EmotionalAI()
