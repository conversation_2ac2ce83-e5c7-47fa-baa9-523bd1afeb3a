import { app, BrowserWindow, ipcMain, shell } from 'electron'
import { join } from 'path'
import Store from 'electron-store'
import { AdvancedAIService } from '../services/AdvancedAIService'

const store = new Store()
let mainWindow: BrowserWindow | null = null
let advancedAI: AdvancedAIService

const isDev = process.env.NODE_ENV === 'development'

function createWindow(): void {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, '../preload/preload.js'),
    },
    show: false,
  })

  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
  })

  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

function setupIpcHandlers(): void {
  // إرسال رسالة
  ipcMain.handle('send-message', async (_event, { message, model }) => {
    try {
      console.log('🚀 إرسال رسالة:', { message, model });
      
      const result = await advancedAI.sendMessage(message, {
        model: model || 'app/chat-general'
      });

      return {
        success: true,
        response: result.response || 'رد فارغ',
        usedProvider: result.provider || 'app-models',
        modelUsed: result.model || model || 'app/chat-general'
      };
    } catch (error: any) {
      console.error('Error sending message:', error);
      return {
        success: false,
        error: `خطأ في إرسال الرسالة: ${error.message}`,
      };
    }
  });

  // الحصول على النماذج
  ipcMain.handle('get-available-models', async () => {
    try {
      const models = advancedAI.getAllAvailableModels()
      console.log('🤖 النماذج المتاحة:', models.length)
      return { success: true, models }
    } catch (error: any) {
      console.error('Error getting models:', error)
      return { success: false, error: error.message }
    }
  })

  // حالة الخدمات
  ipcMain.handle('get-services-status', async () => {
    try {
      const allModels = advancedAI.getAllAvailableModels();
      const appModels = allModels.filter(model => model.id.startsWith('app/'));

      const status = {
        lmStudio: { available: false, modelsCount: 0, status: 'غير مدعوم' },
        msty: {
          available: appModels.length > 0,
          modelsCount: appModels.length,
          status: appModels.length > 0 ? 'النماذج المدمجة متاحة' : 'غير متصل'
        },
        openRouter: { available: false, hasApiKey: false, status: 'غير متصل' }
      };

      return { success: true, status };
    } catch (error: any) {
      return {
        success: true,
        status: {
          lmStudio: { available: false, modelsCount: 0, status: 'غير مدعوم' },
          msty: { available: true, modelsCount: 5, status: 'النماذج المدمجة متاحة' },
          openRouter: { available: false, hasApiKey: false, status: 'غير متصل' }
        }
      };
    }
  });
}

app.whenReady().then(() => {
  advancedAI = new AdvancedAIService()
  setupIpcHandlers()
  createWindow()

  console.log('🚀 تم تهيئة الخدمة المتقدمة بنجاح');

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

app.on('web-contents-created', (_event, contents) => {
  contents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: 'deny' };
  });
})
