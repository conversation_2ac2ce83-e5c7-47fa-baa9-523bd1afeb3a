import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron'
import { join } from 'path'
import Store from 'electron-store'
import { OpenRouterAPI } from '../services/OpenRouterAPI'
import { ChatManager } from '../services/ChatManager'
import { FileExporter } from '../utils/FileExporter'
import { unifiedAI } from '../services/UnifiedAIService'

// إعداد التخزين المحلي
const store = new Store()

// متغيرات عامة
let mainWindow: BrowserWindow | null = null
let chatManager: ChatManager
let openRouterAPI: OpenRouterAPI

const isDev = process.env.NODE_ENV === 'development'

function createWindow(): void {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, '../preload/preload.js'),
    },
    icon: join(__dirname, '../../assets/icon.png'),
    titleBarStyle: 'default',
    show: false,
  })

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // إظهار النافذة عند الاستعداد
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
  })

  // إغلاق التطبيق عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // منع التنقل إلى روابط خارجية
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// تهيئة الخدمات
function initializeServices(): void {
  const apiKey = store.get('openrouter_api_key', '') as string
  openRouterAPI = new OpenRouterAPI(apiKey)
  chatManager = new ChatManager(store)

  // تهيئة الخدمة الموحدة
  unifiedAI.updateConfig({
    cloudApiKey: apiKey,
    preferredProvider: store.get('preferred_provider', 'local') as any,
    fallbackToOpenRouter: store.get('fallback_to_openrouter', true) as boolean
  })
}

// معالجات IPC
function setupIpcHandlers(): void {
  // إرسال رسالة للذكاء الاصطناعي
  ipcMain.handle('send-message', async (_event, { message, conversationId }) => {
    try {
      // تحقق من حالة الخدمات قبل إرسال الرسالة
      const servicesStatus = await unifiedAI.checkAvailability();
      console.log('حالة الخدمات:', servicesStatus);

      if (!servicesStatus.msty && !servicesStatus.openRouter) {
        console.log('❌ جميع الخدمات غير متاحة');
        return {
          success: true,
          response: `❌ لا يمكن الاتصال بأي من خدمات الذكاء الاصطناعي.

يرجى التأكد من:
1. تشغيل برنامج Ollama على جهازك
2. تعيين مفتاح صالح لـ OpenRouter في الإعدادات
3. التحقق من اتصال الإنترنت

حالة الخدمات:
- Ollama: ${servicesStatus.msty ? 'متصل' : 'غير متاح'}
- OpenRouter: ${servicesStatus.openRouter ? 'متصل' : 'غير متاح'}
`,
          conversation: await chatManager.getConversation(conversationId),
          usedProvider: 'none',
          modelUsed: 'system'
        };
      }

      const result = await chatManager.sendMessage(conversationId, message);
      return {
        success: true,
        response: result.response,
        conversation: result.conversation,
        usedProvider: result.usedProvider,
        modelUsed: result.modelUsed
      };
    } catch (error) {
      console.error('Error sending message:', error);
      return {
        success: false,
        error: `خطأ في إرسال الرسالة: ${(error as any).message}`,
        conversation: await chatManager.getConversation(conversationId)
      };
    }
  });

  // الحصول على قائمة النماذج المتاحة (الطريقة الجديدة)
  ipcMain.handle('get-available-models', async () => {
    try {
      const models = await chatManager.getAvailableModels()
      return { success: true, models }
    } catch (error) {
      console.error('Error getting models:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // إنشاء محادثة جديدة
  ipcMain.handle('create-conversation', async (_event, title) => {
    try {
      const conversation = await chatManager.createConversation(title)
      return { success: true, conversation }
    } catch (error) {
      console.error('Error creating conversation:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // الحصول على جميع المحادثات
  ipcMain.handle('get-conversations', async () => {
    try {
      const conversations = await chatManager.getConversations()
      return { success: true, conversations }
    } catch (error) {
      console.error('Error getting conversations:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // الحصول على محادثة محددة
  ipcMain.handle('get-conversation', async (_event, conversationId) => {
    try {
      const conversation = await chatManager.getConversation(conversationId)
      return { success: true, conversation }
    } catch (error) {
      console.error('Error getting conversation:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // حذف محادثة
  ipcMain.handle('delete-conversation', async (_event, conversationId) => {
    try {
      await chatManager.deleteConversation(conversationId)
      return { success: true }
    } catch (error) {
      console.error('Error deleting conversation:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // تصدير المحادثة
  ipcMain.handle('export-conversation', async (_event, { conversationId, format }) => {
    try {
      const conversation = await chatManager.getConversation(conversationId)
      if (!conversation) {
        throw new Error('Conversation not found')
      }

      const result = await dialog.showSaveDialog(mainWindow!, {
        defaultPath: `${conversation.title}.${format}`,
        filters: [
          { name: format.toUpperCase(), extensions: [format] }
        ]
      })

      if (!result.canceled && result.filePath) {
        const exporter = new FileExporter()
        await exporter.exportConversation(conversation, result.filePath, format)
        return { success: true, filePath: result.filePath }
      }

      return { success: false, error: 'Export cancelled' }
    } catch (error) {
      console.error('Error exporting conversation:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // حفظ الإعدادات
  ipcMain.handle('save-settings', async (_event, settings) => {
    try {
      Object.keys(settings).forEach(key => {
        store.set(key, settings[key]);
      });

      // تحديث مفتاح API إذا تم تغييره
      if (settings.openrouter_api_key) {
        openRouterAPI.updateApiKey(settings.openrouter_api_key);
      }
      return { success: true };
    } catch (error) {
      console.error('Error saving settings:', error);
      return { success: false, error: (error as any).message };
    }
  });

  // الحصول على الإعدادات
  ipcMain.handle('get-settings', async () => {
    try {
      const settings = {
        openrouter_api_key: store.get('openrouter_api_key', ''),
        default_model: store.get('default_model', 'meta-llama/llama-3.3-8b-instruct:free'),
        theme: store.get('theme', 'dark'),
        language: store.get('language', 'ar'),
        auto_save: store.get('auto_save', true),
        ollama_base_url: store.get('ollama_base_url', 'http://localhost:11434/v1'), // New property
      }
      return { success: true, settings }
    } catch (error) {
      console.error('Error getting settings:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // معالجات الخدمة الموحدة الجديدة

  // تحديث إعدادات الذكاء الاصطناعي
  ipcMain.handle('update-ai-settings', async (_event, settings) => {
    try {
      await chatManager.updateAISettings(settings)
      return { success: true }
    } catch (error) {
      console.error('Error updating AI settings:', error)
      return { success: false, error: (error as any).message }
    }
  })

  // الحصول على حالة الخدمات
  ipcMain.handle('get-services-status', async () => {
    try {
      // استخدام الخدمة الموحدة للحصول على الحالة الفعلية
      const servicesStatus = await unifiedAI.getServicesStatus();

      // تحويل الحالة إلى التنسيق المطلوب للواجهة
      const status = {
        msty: {
          available: servicesStatus.local.available,
          modelsCount: servicesStatus.local.modelsCount,
          status: servicesStatus.local.status
        },
        openRouter: {
          available: servicesStatus.cloud.available,
          hasApiKey: servicesStatus.cloud.hasApiKey,
          status: servicesStatus.cloud.status
        }
      };

      return { success: true, status };
    } catch (error) {
      console.error('Error getting services status:', error);
      // في حالة الخطأ، نرجع حالة غير متوفرة بشكل واضح
      return {
        success: false,
        error: (error as any).message,
        status: {
           msty: { available: false, modelsCount: 0, status: '❌ خطأ في جلب الحالة' },
           openRouter: { available: false, hasApiKey: false, status: '❌ خطأ في جلب الحالة' }
        }
      };
    }
  });

  // تشغيل LM Studio
  ipcMain.handle('launch-lm-studio', async () => {
    try {
      const success = await chatManager.launchLMStudio()
      return { success }
    } catch (error) {
      console.error('Error launching LM Studio:', error)
      return { success: false, error: (error as any).message }
    }
  })
}

// تهيئة التطبيق
app.whenReady().then(() => {
  initializeServices()
  setupIpcHandlers()
  createWindow()

  console.log('🚀 تم تهيئة الخدمة المتقدمة بنجاح');

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// إغلاق التطبيق على جميع المنصات عدا macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

// الأمان: منع إنشاء نوافذ جديدة
app.on('web-contents-created', (_event, contents) => {
  contents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: 'deny' };
  });
})
