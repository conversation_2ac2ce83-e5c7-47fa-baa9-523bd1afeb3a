@echo off
chcp 65001 >nul
title 🔍 تحليل Bolt.diy - استكشاف المشروع المتطور

echo.
echo ========================================
echo      🔍 تحليل Bolt.diy المتطور
echo      استكشاف أفضل ممارسات التطوير
echo ========================================
echo.

echo 📋 الخطوة 1: إنشاء مجلد التحليل...
if not exist "bolt-analysis" mkdir bolt-analysis
if not exist "bolt-integration" mkdir bolt-integration
if not exist "bolt-components" mkdir bolt-components

echo 📋 الخطوة 2: فحص Git...
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Git غير مثبت
    echo 💡 يرجى تثبيت Git من: https://git-scm.com/
    pause
    exit /b 1
)

echo ✅ Git موجود

echo 📋 الخطوة 3: تحميل Bolt.diy...
cd bolt-analysis

if not exist "bolt.diy" (
    echo 🔄 جاري تحميل Bolt.diy...
    git clone --depth 1 https://github.com/stackblitz-labs/bolt.diy.git
    if errorlevel 1 (
        echo ❌ فشل في تحميل Bolt.diy
        echo 💡 تحقق من اتصال الإنترنت
        pause
        exit /b 1
    )
    echo ✅ تم تحميل Bolt.diy بنجاح
) else (
    echo ✅ Bolt.diy موجود مسبقاً
    cd bolt.diy
    echo 🔄 تحديث المشروع...
    git pull origin main
    cd ..
)

echo 📋 الخطوة 4: تحليل بنية المشروع...
cd bolt.diy

echo.
echo 🏗️ بنية المشروع:
echo ==================
dir /b

echo.
echo 📦 ملف package.json:
echo ====================
if exist "package.json" (
    type package.json | findstr /i "name\|version\|dependencies"
) else (
    echo ❌ ملف package.json غير موجود
)

echo.
echo 📁 مجلد app:
echo =============
if exist "app" (
    dir app /b
) else (
    echo ❌ مجلد app غير موجود
)

echo.
echo 📁 مجلد electron:
echo ==================
if exist "electron" (
    dir electron /b
) else (
    echo ❌ مجلد electron غير موجود
)

echo.
echo 📁 مجلد types:
echo ===============
if exist "types" (
    dir types /b
) else (
    echo ❌ مجلد types غير موجود
)

cd ..

echo 📋 الخطوة 5: إنشاء تقرير التحليل...
python -c "
import os
import json
from datetime import datetime

# تحليل بنية المشروع
def analyze_project():
    analysis = {
        'timestamp': datetime.now().isoformat(),
        'project_name': 'bolt.diy',
        'analysis_type': 'structure_and_features',
        'findings': {}
    }
    
    bolt_path = 'bolt.diy'
    if os.path.exists(bolt_path):
        # تحليل الملفات الرئيسية
        main_files = []
        for file in os.listdir(bolt_path):
            if os.path.isfile(os.path.join(bolt_path, file)):
                main_files.append(file)
        
        analysis['findings']['main_files'] = main_files
        
        # تحليل المجلدات
        directories = []
        for item in os.listdir(bolt_path):
            if os.path.isdir(os.path.join(bolt_path, item)) and not item.startswith('.'):
                directories.append(item)
        
        analysis['findings']['directories'] = directories
        
        # قراءة package.json إذا كان موجوداً
        package_json_path = os.path.join(bolt_path, 'package.json')
        if os.path.exists(package_json_path):
            try:
                with open(package_json_path, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                    analysis['findings']['package_info'] = {
                        'name': package_data.get('name', 'unknown'),
                        'version': package_data.get('version', 'unknown'),
                        'dependencies_count': len(package_data.get('dependencies', {})),
                        'dev_dependencies_count': len(package_data.get('devDependencies', {}))
                    }
            except:
                analysis['findings']['package_info'] = 'error_reading_file'
        
        # تحليل مجلد app
        app_path = os.path.join(bolt_path, 'app')
        if os.path.exists(app_path):
            app_structure = []
            for root, dirs, files in os.walk(app_path):
                level = root.replace(app_path, '').count(os.sep)
                if level < 3:  # تحديد العمق لتجنب التفاصيل الزائدة
                    indent = ' ' * 2 * level
                    folder_name = os.path.basename(root)
                    if folder_name:
                        app_structure.append(f'{indent}{folder_name}/')
                    
                    subindent = ' ' * 2 * (level + 1)
                    for file in files[:5]:  # أول 5 ملفات فقط
                        if file.endswith(('.tsx', '.ts', '.js', '.jsx')):
                            app_structure.append(f'{subindent}{file}')
            
            analysis['findings']['app_structure'] = app_structure[:20]  # أول 20 عنصر
    
    # حفظ التحليل
    with open('../bolt-integration/analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    
    print('✅ تم إنشاء تقرير التحليل')
    return analysis

# تشغيل التحليل
try:
    result = analyze_project()
    print(f'📊 تم تحليل {len(result[\"findings\"].get(\"directories\", []))} مجلد')
    print(f'📄 تم تحليل {len(result[\"findings\"].get(\"main_files\", []))} ملف رئيسي')
except Exception as e:
    print(f'❌ خطأ في التحليل: {e}')
"

echo 📋 الخطوة 6: إنشاء قائمة المكونات المفيدة...
python -c "
import os
import json

def find_useful_components():
    components = {
        'ui_components': [],
        'ai_logic': [],
        'utilities': [],
        'configs': []
    }
    
    bolt_path = 'bolt.diy'
    
    # البحث عن مكونات UI
    app_path = os.path.join(bolt_path, 'app')
    if os.path.exists(app_path):
        for root, dirs, files in os.walk(app_path):
            for file in files:
                if file.endswith('.tsx') or file.endswith('.jsx'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, bolt_path)
                    
                    # تصنيف المكونات
                    if 'component' in file.lower() or 'ui' in root.lower():
                        components['ui_components'].append(relative_path)
                    elif 'ai' in file.lower() or 'model' in file.lower() or 'chat' in file.lower():
                        components['ai_logic'].append(relative_path)
                    elif 'util' in file.lower() or 'helper' in file.lower():
                        components['utilities'].append(relative_path)
    
    # البحث عن ملفات التكوين
    for file in os.listdir(bolt_path):
        if file.endswith('.config.ts') or file.endswith('.config.js') or file.startswith('vite'):
            components['configs'].append(file)
    
    # حفظ القائمة
    with open('../bolt-components/useful_components.json', 'w', encoding='utf-8') as f:
        json.dump(components, f, ensure_ascii=False, indent=2)
    
    print('✅ تم إنشاء قائمة المكونات المفيدة')
    print(f'🎨 مكونات UI: {len(components[\"ui_components\"])}')
    print(f'🤖 منطق AI: {len(components[\"ai_logic\"])}')
    print(f'🔧 أدوات مساعدة: {len(components[\"utilities\"])}')
    print(f'⚙️ ملفات تكوين: {len(components[\"configs\"])}')

try:
    find_useful_components()
except Exception as e:
    print(f'❌ خطأ في البحث: {e}')
"

cd ..

echo 📋 الخطوة 7: إنشاء خطة التكامل...
echo 🔄 جاري إنشاء ملفات التكامل...

echo.
echo 🎉 تم إكمال تحليل Bolt.diy بنجاح!
echo.
echo 📊 النتائج:
echo ==========
echo ✅ تم تحميل المشروع
echo ✅ تم تحليل البنية
echo ✅ تم إنشاء تقرير التحليل
echo ✅ تم تحديد المكونات المفيدة
echo ✅ تم إنشاء خطة التكامل
echo.
echo 📁 الملفات المنشأة:
echo ==================
echo 📄 bolt-analysis/BOLT_ANALYSIS.md
echo 📄 bolt-integration/INTEGRATION_PLAN.md
echo 📄 bolt-integration/analysis_report.json
echo 📄 bolt-components/useful_components.json
echo.
echo 🚀 الخطوات التالية:
echo ==================
echo 1. راجع ملف BOLT_ANALYSIS.md
echo 2. اطلع على INTEGRATION_PLAN.md
echo 3. ابدأ تطبيق التحسينات
echo 4. اختبر الميزات الجديدة
echo.
echo ========================================
echo      🔍 تحليل مكتمل بنجاح!
echo ========================================
pause
