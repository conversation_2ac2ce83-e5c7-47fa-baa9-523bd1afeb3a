services:
  app-prod:
    image: bolt-ai:production
    build:
      context: .
      dockerfile: Dockerfile
      target: bolt-ai-production
    ports:
      - '5173:5173'
    env_file: '.env.local'
    environment:
      - NODE_ENV=production
      - COMPOSE_PROFILES=production
      # No strictly needed but serving as hints for Coolify
      - PORT=5173
      - GROQ_API_KEY=${GROQ_API_KEY}
      - HuggingFace_API_KEY=${HuggingFace_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPEN_ROUTER_API_KEY=${OPEN_ROUTER_API_KEY}
      - GOOGLE_GENERATIVE_AI_API_KEY=${GOOGLE_GENERATIVE_AI_API_KEY}
      - OLLAMA_API_BASE_URL=${OLLAMA_API_BASE_URL}
      - XAI_API_KEY=${XAI_API_KEY}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY}
      - TOGETHER_API_BASE_URL=${TOGETHER_API_BASE_URL}
      - AWS_BEDROCK_CONFIG=${AWS_BEDROCK_CONFIG}
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-debug}
      - DEFAULT_NUM_CTX=${DEFAULT_NUM_CTX:-32768}
      - RUNNING_IN_DOCKER=true
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    command: pnpm run dockerstart
    profiles:
      - production

  app-dev:
    image: bolt-ai:development
    build:
      target: bolt-ai-development
    env_file: '.env.local'
    environment:
      - NODE_ENV=development
      - VITE_HMR_PROTOCOL=ws
      - VITE_HMR_HOST=localhost
      - VITE_HMR_PORT=5173
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - PORT=5173
      - GROQ_API_KEY=${GROQ_API_KEY}
      - HuggingFace_API_KEY=${HuggingFace_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPEN_ROUTER_API_KEY=${OPEN_ROUTER_API_KEY}
      - XAI_API_KEY=${XAI_API_KEY}
      - GOOGLE_GENERATIVE_AI_API_KEY=${GOOGLE_GENERATIVE_AI_API_KEY}
      - OLLAMA_API_BASE_URL=${OLLAMA_API_BASE_URL}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY}
      - TOGETHER_API_BASE_URL=${TOGETHER_API_BASE_URL}
      - AWS_BEDROCK_CONFIG=${AWS_BEDROCK_CONFIG}
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-debug}
      - DEFAULT_NUM_CTX=${DEFAULT_NUM_CTX:-32768}
      - RUNNING_IN_DOCKER=true
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    volumes:
      - type: bind
        source: .
        target: /app
        consistency: cached
      - /app/node_modules
    ports:
      - '5173:5173'
    command: pnpm run dev --host 0.0.0.0
    profiles: ['development', 'default']

  app-prebuild:
    image: ghcr.io/stackblitz-labs/bolt.diy:latest
    ports:
      - '5173:5173'
    environment:
      - NODE_ENV=production
      - COMPOSE_PROFILES=production
      # No strictly needed but serving as hints for Coolify
      - PORT=5173
      - OLLAMA_API_BASE_URL=http://127.0.0.1:11434
      - DEFAULT_NUM_CTX=${DEFAULT_NUM_CTX:-32768}
      - RUNNING_IN_DOCKER=true
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    command: pnpm run dockerstart
    profiles:
      - prebuilt
