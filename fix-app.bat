@echo off
title Fix and Build AI Chat Bot

echo ========================================
echo    Fix and Build - AI Chat Bot
echo ========================================
echo.

echo Step 1: Closing any running instances...
taskkill /F /IM "AI Chat Bot - *" /T 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Backing up important files...
if not exist "backup" mkdir backup
copy main.js backup\main.js /Y >nul
copy index.html backup\index.html /Y >nul

echo Step 3: Creating dist directory if it doesn't exist...
if not exist "dist" mkdir dist
if not exist "dist\main" mkdir dist\main

echo Step 4: Copying main.js to dist\main...
copy main.js dist\main\main.js /Y >nul
echo main.js copied successfully!

echo Step 5: Building the renderer...
call npm run build:renderer
if errorlevel 1 (
    echo Warning: Issues with renderer build, but continuing...
)

echo Step 6: Creating the application...
echo This process may take a few minutes...
call npx electron-builder --win --x64 --config.extraMetadata.main="main.js" --config.asar=true

echo.
echo Process completed!
echo.

if exist "release\win-unpacked" (
    echo Portable version created in release\win-unpacked folder
    echo You can run the application directly from there.
    explorer release\win-unpacked
)

echo.
pause
