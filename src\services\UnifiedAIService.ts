import { OpenRouterAPI, AIModel, ChatMessage } from './OpenRouterAPI';
import { OllamaAPI } from './OllamaAPI';

export type AIProvider = 'local' | 'cloud';

export interface UnifiedAIConfig {
  localModelsPath?: string;
  cloudApiKey?: string;
  preferredProvider?: AIProvider;
  fallbackToOpenRouter?: boolean;
}

export class UnifiedAIService {
  private localModels: AIModel[] = [];
  private cloudModels: AIModel[] = [];
  private ollamaAPI: OllamaAPI;
  private openRouterAPI: OpenRouterAPI | null = null;
  private config: UnifiedAIConfig;
  private mstyAvailable: boolean = false;
  private openRouterAvailable: boolean = false;

  constructor(config: UnifiedAIConfig) {
    this.config = config;
    this.ollamaAPI = new OllamaAPI('http://localhost:11434');

    // إنشاء واجهة OpenRouter إذا كان هناك مفتاح API
    if (config.cloudApiKey && config.cloudApiKey.trim() !== '') {
      this.openRouterAPI = new OpenRouterAPI(config.cloudApiKey);
    }

    // تهيئة النماذج المحلية الافتراضية
    this.localModels = [
      {
        id: 'local/chat-simple',
        name: '💫 محادثة بسيطة',
        description: 'نموذج محلي للمحادثة العامة',
        context_length: 2048,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/code-assistant',
        name: '💻 مساعد البرمجة',
        description: 'نموذج محلي متخصص في البرمجة',
        context_length: 4096,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/creative-writer',
        name: '✍️ الكاتب المبدع',
        description: 'نموذج محلي للكتابة الإبداعية',
        context_length: 4096,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/novel-assistant',
        name: '📚 مساعد الروايات',
        description: 'نموذج محلي متخصص في كتابة وتطوير الروايات',
        context_length: 8192,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/poetry-master',
        name: '🎵 سيد الشعر',
        description: 'نموذج محلي متخصص في كتابة وتحليل الشعر',
        context_length: 4096,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      }
    ];

    // فحص توفر الخدمات عند التهيئة
    this.checkServicesAvailability();
  }

  // فحص توفر الخدمات
  private async checkServicesAvailability(): Promise<void> {
    try {
      // فحص توفر Ollama
      this.mstyAvailable = await this.ollamaAPI.checkAvailability();

      // فحص توفر OpenRouter
      if (this.openRouterAPI) {
        this.openRouterAvailable = await this.openRouterAPI.testConnection();
      }

      console.log(`خدمات الذكاء الاصطناعي: Ollama: ${this.mstyAvailable}, OpenRouter: ${this.openRouterAvailable}`);
    } catch (error) {
      console.error('خطأ في فحص توفر الخدمات:', error);
    }
  }

  updateConfig(config: Partial<UnifiedAIConfig>): void {
    this.config = { ...this.config, ...config };

    // تحديث واجهة OpenRouter إذا تم تغيير مفتاح API
    if (config.cloudApiKey !== undefined) {
      if (config.cloudApiKey && config.cloudApiKey.trim() !== '') {
        if (this.openRouterAPI) {
          this.openRouterAPI.updateApiKey(config.cloudApiKey);
        } else {
          this.openRouterAPI = new OpenRouterAPI(config.cloudApiKey);
        }
        // إعادة فحص الاتصال بعد تحديث المفتاح
        this.openRouterAPI.testConnection().then(available => {
          this.openRouterAvailable = available;
        });
      } else {
        this.openRouterAPI = null;
        this.openRouterAvailable = false;
      }
    }
  }

  async checkAvailability(): Promise<{
    msty: boolean;
    openRouter: boolean;
    recommendedProvider: AIProvider;
  }> {
    // إعادة فحص توفر الخدمات
    await this.checkServicesAvailability();

    // تحديد المزود الموصى به بناءً على التوفر
    let recommendedProvider: AIProvider = 'local';
    if (this.openRouterAvailable) {
      recommendedProvider = 'cloud';
    } else if (this.mstyAvailable) {
      recommendedProvider = 'local';
    }

    return {
      msty: this.mstyAvailable,
      openRouter: this.openRouterAvailable,
      recommendedProvider
    };
  }

  async getAllModels(): Promise<{
    local: AIModel[]
    online: AIModel[]
    combined: AIModel[]
  }> {
    // تحديث النماذج المتاحة
    await this.refreshAvailableModels();

    return {
      local: this.localModels,
      online: this.cloudModels,
      combined: [...this.localModels, ...this.cloudModels]
    }
  }

  // تحديث قائمة النماذج المتاحة
  private async refreshAvailableModels(): Promise<void> {
    try {
      // تحديث نماذج Ollama إذا كانت متاحة
      if (this.mstyAvailable) {
        const ollamaModels = await this.ollamaAPI.getModels();
        // تحويل نماذج Ollama إلى تنسيق AIModel
        const formattedOllamaModels = ollamaModels.map(model => ({
          id: `ollama/${model.id}`,
          name: model.name,
          description: model.description,
          context_length: 4096, // قيمة افتراضية
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          architecture: { modality: 'text', tokenizer: 'local' },
          top_provider: { is_moderated: false }
        }));

        // دمج النماذج المحلية الافتراضية مع نماذج Ollama
        this.localModels = [...this.localModels.filter(m => m.id.startsWith('local/')), ...formattedOllamaModels];
      }

      // تحديث نماذج OpenRouter إذا كانت متاحة
      if (this.openRouterAvailable && this.openRouterAPI) {
        this.cloudModels = await this.openRouterAPI.getAvailableModels();
      } else {
        this.cloudModels = [];
      }
    } catch (error) {
      console.error('خطأ في تحديث النماذج المتاحة:', error);
    }
  }

  async sendMessage(
    message: string,
    model: string,
    conversationHistory: ChatMessage[] = [],
    forceProvider?: AIProvider
  ): Promise<{
    response: string
    usedProvider: AIProvider
    modelUsed: string
  }> {
    // تحديد المزود المناسب بناءً على نوع النموذج والتوفر
    let provider: AIProvider = forceProvider || 'local';
    let actualModel = model;

    // تحديد المزود بناءً على معرف النموذج
    if (model.startsWith('ollama/')) {
      provider = 'local';
    } else if (model.includes(':free') || model.includes(':custom')) {
      provider = 'cloud';
    } else if (model.startsWith('local/')) {
      provider = 'local';
    }

    // التحقق من توفر المزود المحدد
    if (provider === 'local' && !this.mstyAvailable) {
      // التبديل إلى OpenRouter إذا كان متاحاً
      if (this.openRouterAvailable) {
        provider = 'cloud';
        // استخدام نموذج مكافئ من OpenRouter
        actualModel = this.getEquivalentCloudModel(model);
      } else {
        throw new Error('خدمة Ollama غير متاحة. تأكد من تشغيلها على جهازك.');
      }
    } else if (provider === 'cloud' && !this.openRouterAvailable) {
      // التبديل إلى Ollama إذا كان متاحاً
      if (this.mstyAvailable) {
        provider = 'local';
        // استخدام نموذج مكافئ من Ollama
        actualModel = this.getEquivalentLocalModel(model);
      } else {
        throw new Error('خدمة OpenRouter غير متاحة. تأكد من تعيين مفتاح API صالح.');
      }
    }

    try {
      let response: string;

      // إرسال الرسالة إلى المزود المناسب
      if (provider === 'local') {
        // استخدام Ollama للنماذج المحلية
        if (actualModel.startsWith('ollama/')) {
          // استخدام نموذج Ollama مباشرة
          const ollamaModelId = actualModel.replace('ollama/', '');
          const ollamaResult = await this.ollamaAPI.sendMessage(message, ollamaModelId, conversationHistory);

          if (ollamaResult.success) {
            response = ollamaResult.message;
          } else {
            throw new Error(ollamaResult.error || 'خطأ في Ollama');
          }
        } else {
          // استخدام النماذج المحلية المضمنة
          response = await this.handleLocalModel(actualModel, message, conversationHistory);
        }
      } else {
        // استخدام OpenRouter للنماذج السحابية
        if (!this.openRouterAPI) {
          throw new Error('لم يتم تكوين OpenRouter. تأكد من تعيين مفتاح API صالح.');
        }

        response = await this.openRouterAPI.sendMessage(message, actualModel, conversationHistory);
      }

      return {
        response,
        usedProvider: provider,
        modelUsed: actualModel
      };
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
      throw error;
    }
  }

  // الحصول على نموذج سحابي مكافئ
  private getEquivalentCloudModel(localModel: string): string {
    const modelMap: {[key: string]: string} = {
      'local/chat-simple': 'meta-llama/llama-3.3-8b-instruct:free',
      'local/code-assistant': 'deepseek/deepseek-prover-v2:free',
      'local/creative-writer': 'literary/naguib-mahfouz:custom',
      'local/novel-assistant': 'literary/naguib-mahfouz:custom',
      'local/poetry-master': 'literary/ahmed-fouad-negm:custom'
    };

    return modelMap[localModel] || 'meta-llama/llama-3.3-8b-instruct:free';
  }

  // الحصول على نموذج محلي مكافئ
  private getEquivalentLocalModel(cloudModel: string): string {
    if (cloudModel.includes('literary/')) {
      return 'local/creative-writer';
    } else if (cloudModel.includes('cinema/')) {
      return 'local/creative-writer';
    } else if (cloudModel.includes('code')) {
      return 'local/code-assistant';
    }

    return 'local/chat-simple';
  }

  // معالجة النماذج المحلية المضمنة
  private async handleLocalModel(
    model: string,
    message: string,
    conversationHistory: ChatMessage[]
  ): Promise<string> {
    const modelType = model.split('/')[1];

    switch(modelType) {
      case 'chat-simple':
        return await this.generateSimpleChatResponse(message);
      case 'code-assistant':
        return await this.generateCodeResponse(message);
      case 'creative-writer':
        return await this.generateCreativeResponse(message, conversationHistory);
      case 'novel-assistant':
        return await this.generateNovelResponse(message, conversationHistory);
      case 'poetry-master':
        return await this.generatePoetryResponse(message, conversationHistory);
      default:
        return await this.generateSimpleChatResponse(message);
    }
  }

  private async generateSimpleChatResponse(message: string): Promise<string> {
    // استجابة محادثة بسيطة
    try {
      // محاولة استخدام Ollama إذا كان متاحاً
      if (this.mstyAvailable) {
        const result = await this.ollamaAPI.sendMessage(message, 'llama3.1:8b', []);
        if (result.success) {
          return result.message;
        }
      }
    } catch (error) {
      console.error('خطأ في استخدام Ollama للمحادثة البسيطة:', error);
    }

    // استجابة احتياطية
    const responses = [
      "أفهم ما تقول. كيف يمكنني مساعدتك أكثر؟",
      "هذه فكرة جيدة. دعنا نناقشها بالتفصيل.",
      "شكراً لمشاركة هذه المعلومات. هل تريد مني أن أشرح أي جزء؟",
      "نعم، هذا صحيح. هل تريد أن نتعمق أكثر في هذا الموضوع؟"
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  private async generateCodeResponse(message: string): Promise<string> {
    // استجابة مساعد البرمجة
    try {
      // محاولة استخدام Ollama إذا كان متاحاً
      if (this.mstyAvailable) {
        const result = await this.ollamaAPI.sendMessage(message, 'codellama:7b', []);
        if (result.success) {
          return result.message;
        }
      }
    } catch (error) {
      console.error('خطأ في استخدام Ollama لمساعد البرمجة:', error);
    }

    // استجابة احتياطية
    return "// سأساعدك في كتابة الكود\nfunction مرحبا() {\n    return 'مرحباً بك!';\n}";
  }

  private async generateCreativeResponse(message: string, history: ChatMessage[] = []): Promise<string> {
    // استجابة الكتابة الإبداعية
    try {
      // محاولة استخدام Ollama إذا كان متاحاً
      if (this.mstyAvailable) {
        const systemMessage = {
          role: 'system',
          content: 'أنت كاتب مبدع متخصص في الأدب العربي. ساعد المستخدم في كتابة محتوى إبداعي عالي الجودة بأسلوب أدبي جميل.'
        };

        const result = await this.ollamaAPI.sendMessage(
          message,
          'llama3.1:8b',
          [systemMessage as any, ...history]
        );

        if (result.success) {
          return result.message;
        }
      }
    } catch (error) {
      console.error('خطأ في استخدام Ollama للكتابة الإبداعية:', error);
    }

    // استجابة احتياطية
    const responses = [
      "كان يا ما كان في قديم الزمان...",
      "في يوم من الأيام الجميلة...",
      "دعني أشارك معك قصة مثيرة للاهتمام..."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  // استجابة مساعد الروايات
  private async generateNovelResponse(message: string, history: ChatMessage[] = []): Promise<string> {
    try {
      if (this.mstyAvailable) {
        const systemMessage = {
          role: 'system',
          content: 'أنت كاتب روايات محترف متخصص في الأدب العربي. ساعد المستخدم في تطوير الشخصيات والحبكة والحوار وعناصر الرواية الأخرى. قدم اقتراحات إبداعية وتحليلات عميقة.'
        };

        const result = await this.ollamaAPI.sendMessage(
          message,
          'llama3.1:8b',
          [systemMessage as any, ...history]
        );

        if (result.success) {
          return result.message;
        }
      }
    } catch (error) {
      console.error('خطأ في استخدام Ollama لمساعد الروايات:', error);
    }

    // استجابة احتياطية
    return "لتطوير روايتك، فكر في الشخصيات الرئيسية وتطورها عبر الأحداث. ابدأ بتحديد الصراع الأساسي والعقدة الرئيسية، ثم ارسم مسار الأحداث نحو الذروة والحل. تذكر أن الحوار الجيد يكشف عن شخصية الأبطال ويدفع الأحداث للأمام.";
  }

  // استجابة سيد الشعر
  private async generatePoetryResponse(message: string, history: ChatMessage[] = []): Promise<string> {
    try {
      if (this.mstyAvailable) {
        const systemMessage = {
          role: 'system',
          content: 'أنت شاعر عربي متمكن من مختلف بحور الشعر وأنماطه. ساعد المستخدم في كتابة وتحليل الشعر العربي بأنواعه: الفصيح والعامي والحر. قدم نصائح حول القافية والوزن والصور البلاغية.'
        };

        const result = await this.ollamaAPI.sendMessage(
          message,
          'llama3.1:8b',
          [systemMessage as any, ...history]
        );

        if (result.success) {
          return result.message;
        }
      }
    } catch (error) {
      console.error('خطأ في استخدام Ollama لسيد الشعر:', error);
    }

    // استجابة احتياطية
    return "الشعر ديوان العرب وأرقى أشكال التعبير. لكتابة قصيدة مؤثرة، اختر موضوعاً يلهمك واعمل على تطوير الصور الشعرية والاستعارات. انتبه للوزن والقافية إذا كنت تكتب شعراً عمودياً، وركز على جمال اللغة وعمق المعنى في الشعر الحر.";
  }

  async getServicesStatus(): Promise<{
    local: {
      available: boolean
      modelsCount: number
      status: string
    }
    cloud: {
      available: boolean
      hasApiKey: boolean
      status: string
    }
  }> {
    // إعادة فحص توفر الخدمات
    await this.checkServicesAvailability();

    return {
      local: {
        available: this.mstyAvailable,
        modelsCount: this.localModels.length,
        status: this.mstyAvailable ? 'متصل' : 'غير متاح'
      },
      cloud: {
        available: this.openRouterAvailable,
        hasApiKey: !!this.config.cloudApiKey,
        status: this.openRouterAvailable ? 'متصل' : (this.config.cloudApiKey ? 'غير متصل' : 'غير مكوّن')
      }
    };
  }

  // تصنيف النماذج حسب النوع
  async getModelsByCategory(): Promise<{
    general: AIModel[]
    novels: AIModel[]
    poetry: AIModel[]
    cinema: AIModel[]
    programming: AIModel[]
  }> {
    // تحديث النماذج المتاحة
    await this.refreshAvailableModels();

    const allModels = [...this.localModels, ...this.cloudModels];

    return {
      general: allModels.filter(model =>
        !model.id.includes('novel') &&
        !model.id.includes('poetry') &&
        !model.id.includes('literary') &&
        !model.id.includes('cinema') &&
        !model.id.includes('code')
      ),
      novels: allModels.filter(model =>
        model.id.includes('novel') ||
        (model.id.includes('literary') && !model.id.includes('poetry'))
      ),
      poetry: allModels.filter(model =>
        model.id.includes('poetry') ||
        model.id.includes('abnoudy') ||
        model.id.includes('negm')
      ),
      cinema: allModels.filter(model =>
        model.id.includes('cinema') ||
        model.id.includes('dialogue') ||
        model.id.includes('screenplay')
      ),
      programming: allModels.filter(model =>
        model.id.includes('code') ||
        model.id.includes('deepseek')
      )
    };
  }
}

// Create shared instance
export const unifiedAI = new UnifiedAIService({
  localModelsPath: './models'
});
