import { OpenRouterAPI, AIModel, ChatMessage } from './OpenRouterAPI';
import { OllamaAPI } from './OllamaAPI';

export type AIProvider = 'local' | 'cloud';

export interface UnifiedAIConfig {
  localModelsPath?: string;
  cloudApiKey?: string;
  preferredProvider?: AIProvider;
  fallbackToOpenRouter?: boolean;
}

export class UnifiedAIService {
  private localModels: AIModel[] = [];
  private cloudModels: AIModel[] = [];
  private ollamaAPI: OllamaAPI;
  private openRouterAPI: OpenRouterAPI | null = null;
  private config: UnifiedAIConfig;
  private ollamaAvailable: boolean = false;
  private openRouterAvailable: boolean = false;

  constructor(config: UnifiedAIConfig) {
    this.config = config;
    this.ollamaAPI = new OllamaAPI('http://localhost:11434/v1');

    // إنشاء واجهة OpenRouter إذا كان هناك مفتاح API
    if (config.cloudApiKey && config.cloudApiKey.trim() !== '') {
      this.openRouterAPI = new OpenRouterAPI(config.cloudApiKey);
    }

    // تهيئة النماذج المحلية الافتراضية
    this.localModels = [
      {
        id: 'local/chat-simple',
        name: '💫 محادثة بسيطة',
        description: 'نموذج محلي للمحادثة العامة',
        context_length: 2048,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/code-assistant',
        name: '💻 مساعد البرمجة',
        description: 'نموذج محلي متخصص في البرمجة',
        context_length: 4096,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/creative-writer',
        name: '✍️ الكاتب المبدع',
        description: 'نموذج محلي للكتابة الإبداعية',
        context_length: 4096,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/novel-assistant',
        name: '📚 مساعد الروايات',
        description: 'نموذج محلي متخصص في كتابة وتطوير الروايات',
        context_length: 8192,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      },
      {
        id: 'local/poetry-master',
        name: '🎵 سيد الشعر',
        description: 'نموذج محلي متخصص في كتابة وتحليل الشعر',
        context_length: 4096,
        pricing: { prompt: 'مجاني', completion: 'مجاني' },
        architecture: { modality: 'text', tokenizer: 'local' },
        top_provider: {
          is_moderated: true
        }
      }
    ];

    // فحص توفر الخدمات عند التهيئة
    this.checkServicesAvailability();
  }

  // فحص توفر الخدمات
  private async checkServicesAvailability(): Promise<void> {
    try {
      // فحص توفر Ollama
      this.ollamaAvailable = await this.ollamaAPI.checkAvailability();

      // فحص توفر OpenRouter
      if (this.openRouterAPI) {
        this.openRouterAvailable = await this.openRouterAPI.testConnection();
      }

      console.log(`خدمات الذكاء الاصطناعي: Ollama: ${this.ollamaAvailable}, OpenRouter: ${this.openRouterAvailable}`);
    } catch (error) {
      console.error('خطأ في فحص توفر الخدمات:', error);
    }
  }

  updateConfig(config: Partial<UnifiedAIConfig>): void {
    this.config = { ...this.config, ...config };

    // تحديث واجهة OpenRouter إذا تم تغيير مفتاح API
    if (config.cloudApiKey !== undefined) {
      if (config.cloudApiKey && config.cloudApiKey.trim() !== '') {
        if (this.openRouterAPI) {
          this.openRouterAPI.updateApiKey(config.cloudApiKey);
        } else {
          this.openRouterAPI = new OpenRouterAPI(config.cloudApiKey);
        }
        // إعادة فحص الاتصال بعد تحديث المفتاح
        this.openRouterAPI.testConnection().then(available => {
          this.openRouterAvailable = available;
        });
      } else {
        this.openRouterAPI = null;
        this.openRouterAvailable = false;
      }
    }
  }

  async checkAvailability(): Promise<{
    msty: boolean;
    openRouter: boolean;
    recommendedProvider: AIProvider;
  }> {
    // إعادة فحص توفر الخدمات
    await this.checkServicesAvailability();

    // تحديد المزود الموصى به بناءً على التوفر
    let recommendedProvider: AIProvider = 'local';
    if (this.openRouterAvailable) {
      recommendedProvider = 'cloud';
    } else if (this.ollamaAvailable) {
      recommendedProvider = 'local';
    }

    return {
      msty: this.ollamaAvailable,
      openRouter: this.openRouterAvailable,
      recommendedProvider
    };
  }

  async getAllModels(): Promise<{
    local: AIModel[]
    online: AIModel[]
    combined: AIModel[]
  }> {
    // تحديث النماذج المتاحة
    await this.refreshAvailableModels();

    return {
      local: this.localModels,
      online: this.cloudModels,
      combined: [...this.localModels, ...this.cloudModels]
    }
  }

  // تحديث قائمة النماذج المتاحة
  private async refreshAvailableModels(): Promise<void> {
    try {
      // تحديث نماذج Ollama إذا كانت متاحة
      if (this.ollamaAvailable) {
        const ollamaModels = await this.ollamaAPI.getModels();
        // تحويل نماذج Ollama إلى تنسيق AIModel
        const formattedOllamaModels = ollamaModels.map(model => ({
          id: `ollama/${model.id}`,
          name: model.name,
          description: model.description,
          context_length: 4096, // قيمة افتراضية
          pricing: { prompt: 'مجاني', completion: 'مجاني' },
          architecture: { modality: 'text', tokenizer: 'local' },
          top_provider: { is_moderated: false }
        }));

        // دمج النماذج المحلية الافتراضية مع نماذج Ollama
        this.localModels = [...this.localModels.filter(m => m.id.startsWith('local/')), ...formattedOllamaModels];
      }

      // تحديث نماذج OpenRouter إذا كانت متاحة
      if (this.openRouterAvailable && this.openRouterAPI) {
        this.cloudModels = await this.openRouterAPI.getAvailableModels();
      } else {
        this.cloudModels = [];
      }
    } catch (error) {
      console.error('خطأ في تحديث النماذج المتاحة:', error);
    }
  }

  async sendMessage(
    message: string,
    model: string,
    conversationHistory: ChatMessage[] = [],
    forceProvider?: AIProvider
  ): Promise<{
    response: string
    usedProvider: AIProvider
    modelUsed: string
  }> {
    // تحديد المزود المناسب بناءً على نوع النموذج والتوفر
    let provider: AIProvider = forceProvider || 'local';
    let actualModel = model;

    // تحديد المزود بناءً على معرف النموذج
    if (model.startsWith('ollama/')) {
      provider = 'local';
    } else if (model.includes(':free') || model.includes(':custom')) {
      provider = 'cloud';
    } else if (model.startsWith('local/')) {
      provider = 'local';
    }

    // التحقق من توفر المزود المحدد
    if (provider === 'local' && !this.ollamaAvailable) {
      // التبديل إلى OpenRouter إذا كان متاحاً
      if (this.openRouterAvailable) {
        provider = 'cloud';
        // استخدام نموذج مكافئ من OpenRouter
        actualModel = this.getEquivalentCloudModel(model);
      } else {
        throw new Error('خدمة Ollama غير متاحة. تأكد من تشغيلها على جهازك.');
      }
    } else if (provider === 'cloud' && !this.openRouterAvailable) {
      // التبديل إلى Ollama إذا كان متاحاً
      if (this.ollamaAvailable) {
        provider = 'local';
        // استخدام نموذج مكافئ من Ollama
        actualModel = this.getEquivalentLocalModel(model);
      } else {
        throw new Error('خدمة OpenRouter غير متاحة. تأكد من تعيين مفتاح API صالح.');
      }
    }

    try {
      let response: string;

      // إرسال الرسالة إلى المزود المناسب
      if (provider === 'local') {
        // استخدام Ollama للنماذج المحلية
        if (actualModel.startsWith('ollama/')) {
          // استخدام نموذج Ollama مباشرة
          const ollamaModelId = actualModel.replace('ollama/', '');
          response = await this.ollamaAPI.sendMessage(message, ollamaModelId, conversationHistory);
        } else {
          // استخدام النماذج المدمجة في التطبيق
          response = await this.handleAppModel(actualModel, message, conversationHistory);
        }
      } else {
        // استخدام OpenRouter للنماذج السحابية
        if (!this.openRouterAPI) {
          throw new Error('لم يتم تكوين OpenRouter. تأكد من تعيين مفتاح API صالح.');
        }

        response = await this.openRouterAPI.sendMessage(message, actualModel, conversationHistory);
      }

      return {
        response,
        usedProvider: provider,
        modelUsed: actualModel
      };
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
      throw error;
    }
  }

  // الحصول على نموذج سحابي مكافئ
  private getEquivalentCloudModel(localModel: string): string {
    const modelMap: {[key: string]: string} = {
      'local/chat-simple': 'meta-llama/llama-3.3-8b-instruct:free',
      'local/code-assistant': 'deepseek/deepseek-prover-v2:free',
      'local/creative-writer': 'literary/naguib-mahfouz:custom',
      'local/novel-assistant': 'literary/naguib-mahfouz:custom',
      'local/poetry-master': 'literary/ahmed-fouad-negm:custom'
    };

    return modelMap[localModel] || 'meta-llama/llama-3.3-8b-instruct:free';
  }

  // الحصول على نموذج محلي مكافئ
  private getEquivalentLocalModel(cloudModel: string): string {
    if (cloudModel.includes('literary/')) {
      return 'local/creative-writer';
    } else if (cloudModel.includes('cinema/')) {
      return 'local/creative-writer';
    } else if (cloudModel.includes('code')) {
      return 'local/code-assistant';
    }

    return 'local/chat-simple';
  }

  // معالجة النماذج المدمجة في التطبيق
  private async handleAppModel(
    model: string,
    message: string,
    conversationHistory: ChatMessage[]
  ): Promise<string> {
    console.log(`🤖 معالجة النموذج المدمج: ${model}`);
    const modelType = model.split('/')[1];

    switch(modelType) {
      case 'chat-general':
        return await this.generateAppChatResponse(message);
      case 'code-expert':
        return await this.generateAppCodeResponse(message);
      case 'creative-writer':
        return await this.generateAppCreativeResponse(message, conversationHistory);
      case 'arabic-expert':
        return await this.generateAppArabicResponse(message, conversationHistory);
      case 'smart-assistant':
        return await this.generateAppSmartResponse(message, conversationHistory);
      default:
        return await this.generateAppChatResponse(message);
    }
  }

  // دوال النماذج المدمجة
  private async generateAppChatResponse(message: string): Promise<string> {
    console.log('🤖 تشغيل نموذج المحادثة العامة المدمج');

    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('مرحبا') || lowerMessage.includes('السلام') || lowerMessage.includes('أهلا')) {
      return "مرحباً بك! أنا مساعدك الذكي المدمج في التطبيق. كيف يمكنني مساعدتك اليوم؟";
    } else if (lowerMessage.includes('كيف حالك') || lowerMessage.includes('كيفك')) {
      return "أنا بخير، شكراً لسؤالك! أنا هنا لمساعدتك في أي شيء تحتاجه. ما الذي يمكنني فعله لك؟";
    } else if (lowerMessage.includes('شكرا') || lowerMessage.includes('شكراً')) {
      return "العفو! سعيد لأنني استطعت مساعدتك. هل تحتاج إلى أي شيء آخر؟";
    } else if (lowerMessage.includes('وداعا') || lowerMessage.includes('مع السلامة')) {
      return "وداعاً! كان من دواعي سروري مساعدتك. أراك قريباً!";
    } else {
      const responses = [
        "هذا سؤال مثير للاهتمام! دعني أفكر فيه معك.",
        "أفهم ما تقصده. يمكنني مساعدتك في هذا الأمر.",
        "هذا موضوع جيد للنقاش. ما رأيك لو تعمقنا فيه أكثر؟",
        "شكراً لمشاركة هذا معي. كيف يمكنني مساعدتك بشكل أفضل؟"
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }
  }

  private async generateAppCodeResponse(message: string): Promise<string> {
    console.log('💻 تشغيل نموذج خبير البرمجة المدمج');

    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('javascript') || lowerMessage.includes('js')) {
      return `// مثال JavaScript من النموذج المدمج
function حل_المشكلة() {
    console.log("مرحباً من خبير البرمجة المدمج!");

    // يمكنني مساعدتك في:
    // - كتابة الكود
    // - إصلاح الأخطاء
    // - شرح المفاهيم
    // - تحسين الأداء

    return "جاهز لمساعدتك!";
}

حل_المشكلة();`;
    } else {
      return `// خبير البرمجة المدمج في التطبيق
/*
 * مرحباً! أنا خبير البرمجة المدمج في التطبيق
 * يمكنني مساعدتك في:
 *
 * ✅ كتابة الكود بلغات مختلفة
 * ✅ إصلاح الأخطاء والمشاكل
 * ✅ شرح المفاهيم البرمجية
 * ✅ تحسين الأداء والكود
 * ✅ مراجعة الكود وتقييمه
 *
 * فقط اسألني عن أي شيء متعلق بالبرمجة!
 */

console.log("جاهز لمساعدتك في البرمجة! 💻");`;
    }
  }

  private async generateAppCreativeResponse(message: string, _history: ChatMessage[] = []): Promise<string> {
    console.log('✍️ تشغيل نموذج الكاتب الإبداعي المدمج');

    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('قصة') || lowerMessage.includes('حكاية')) {
      return `✨ **بداية قصة إبداعية من النموذج المدمج**

في زمن ليس ببعيد، في مدينة تحتضن الأحلام بين أزقتها الضيقة...

كان هناك ${lowerMessage.includes('طفل') ? 'طفل صغير' : 'شخص'} يحمل في قلبه حلماً كبيراً. كل صباح، كان ينظر من نافذة غرفته إلى الأفق البعيد، يتخيل المغامرات التي تنتظره.

*هل تريد مني أن أكمل هذه القصة؟ أم تفضل أن نبدأ قصة جديدة من اختيارك؟*`;
    } else if (lowerMessage.includes('شعر') || lowerMessage.includes('قصيدة')) {
      return `🎭 **إبداع شعري من النموذج المدمج**

يا صديق الحرف والكلمات
أنا هنا لأساعدك في رحلة الإبداع

في بحر الشعر نبحر معاً
نصوغ من الكلمات لآلئ

*أخبرني عن الموضوع الذي تريد أن نكتب عنه، وسأساعدك في صياغة أبيات جميلة!*`;
    } else {
      return `✨ **الكاتب الإبداعي المدمج**

مرحباً بك في عالم الإبداع! أنا النموذج الإبداعي المدمج في التطبيق.

يمكنني مساعدتك في:

📖 **كتابة القصص** - من القصص القصيرة إلى الروايات الطويلة
🎭 **الشعر والأدب** - بجميع أنواعه وبحوره
✍️ **الكتابة الإبداعية** - مقالات، خواطر، ونصوص أدبية
🎨 **تطوير الأفكار** - تحويل الأفكار البسيطة إلى أعمال إبداعية

*ما نوع الإبداع الذي تريد أن نعمل عليه معاً؟*`;
    }
  }

  private async generateAppArabicResponse(_message: string, _history: ChatMessage[] = []): Promise<string> {
    console.log('🇸🇦 تشغيل نموذج خبير اللغة العربية المدمج');

    return `🇸🇦 **خبير اللغة العربية المدمج**

أهلاً وسهلاً بك! أنا خبير اللغة العربية المدمج في التطبيق.

يمكنني مساعدتك في:

📚 **النحو والصرف** - قواعد اللغة العربية وتطبيقاتها
📖 **البلاغة والأدب** - الاستعارات والكنايات والمحسنات البديعية
✍️ **الإملاء والكتابة** - القواعد الإملائية والكتابة الصحيحة
🎭 **الشعر العربي** - البحور والقوافي والأوزان
📝 **التعبير والإنشاء** - كتابة المقالات والخطابات
🔍 **تحليل النصوص** - فهم وتفسير النصوص الأدبية

*ما الموضوع اللغوي الذي تريد أن نناقشه؟*`;
  }

  private async generateAppSmartResponse(message: string, _history: ChatMessage[] = []): Promise<string> {
    console.log('🧠 تشغيل المساعد الذكي المدمج');

    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('مساعدة') || lowerMessage.includes('ساعدني')) {
      return `🧠 **المساعد الذكي المدمج**

أنا هنا لمساعدتك! يمكنني:

🤖 **الإجابة على الأسئلة** - في مختلف المجالات
💡 **تقديم الاقتراحات** - حلول إبداعية للمشاكل
📊 **التحليل والتفكير** - تحليل المعلومات والبيانات
🎯 **التخطيط والتنظيم** - مساعدتك في تنظيم أفكارك
🔍 **البحث والاستكشاف** - العثور على المعلومات المفيدة

*ما نوع المساعدة التي تحتاجها؟*`;
    } else {
      return `🧠 **تحليل ذكي للرسالة**

لقد قمت بتحليل رسالتك وإليك ما يمكنني تقديمه:

📝 **الموضوع**: ${message.length > 50 ? 'موضوع مفصل ومعقد' : 'موضوع بسيط ومباشر'}
🎯 **التركيز**: ${lowerMessage.includes('؟') ? 'سؤال يحتاج إجابة' : 'بيان أو طلب'}
💭 **الاقتراح**: دعنا نتعمق أكثر في هذا الموضوع لأقدم لك أفضل مساعدة ممكنة.

*هل تريد مني توضيح أي جانب معين من موضوعك؟*`;
    }
  }

  async getServicesStatus(): Promise<{
    local: {
      available: boolean
      modelsCount: number
      status: string
    }
    cloud: {
      available: boolean
      hasApiKey: boolean
      status: string
    }
  }> {
    // إعادة فحص توفر الخدمات
    await this.checkServicesAvailability();

    return {
      local: {
        available: this.ollamaAvailable,
        modelsCount: this.localModels.length,
        status: this.ollamaAvailable ? 'متصل' : 'غير متاح'
      },
      cloud: {
        available: this.openRouterAvailable,
        hasApiKey: !!this.config.cloudApiKey,
        status: this.openRouterAvailable ? 'متصل' : (this.config.cloudApiKey ? 'غير متصل' : 'غير مكوّن')
      }
    };
  }

  // تصنيف النماذج حسب النوع
  async getModelsByCategory(): Promise<{
    general: AIModel[]
    novels: AIModel[]
    poetry: AIModel[]
    cinema: AIModel[]
    programming: AIModel[]
  }> {
    // تحديث النماذج المتاحة
    await this.refreshAvailableModels();

    const allModels = [...this.localModels, ...this.cloudModels];

    return {
      general: allModels.filter(model =>
        !model.id.includes('novel') &&
        !model.id.includes('poetry') &&
        !model.id.includes('literary') &&
        !model.id.includes('cinema') &&
        !model.id.includes('code')
      ),
      novels: allModels.filter(model =>
        model.id.includes('novel') ||
        (model.id.includes('literary') && !model.id.includes('poetry'))
      ),
      poetry: allModels.filter(model =>
        model.id.includes('poetry') ||
        model.id.includes('abnoudy') ||
        model.id.includes('negm')
      ),
      cinema: allModels.filter(model =>
        model.id.includes('cinema') ||
        model.id.includes('dialogue') ||
        model.id.includes('screenplay')
      ),
      programming: allModels.filter(model =>
        model.id.includes('code') ||
        model.id.includes('deepseek')
      )
    };
  }
}

// Create shared instance
export const unifiedAI = new UnifiedAIService({
  localModelsPath: './models'
});
