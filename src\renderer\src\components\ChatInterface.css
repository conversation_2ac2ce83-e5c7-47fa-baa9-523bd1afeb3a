.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-primary);
}

/* شريط الأدوات العلوي */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  min-height: 70px;
}

.chat-title h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  font-weight: 600;
}

.message-count {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  display: block;
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* منطقة الرسائل */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  background: linear-gradient(to bottom, var(--bg-primary), var(--bg-secondary));
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 800px;
  margin: 0 auto;
}

/* رسالة الترحيب */
.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 500px;
  padding: var(--spacing-xl);
}

.welcome-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.welcome-content h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: var(--font-size-xl);
}

.welcome-content p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* قوالب الكتابة الإبداعية */
.creative-templates {
  margin: 20px 0;
  padding: 15px;
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 123, 255, 0.1);
}

.creative-templates h4 {
  margin-bottom: 15px;
  color: var(--primary-color);
  font-weight: 600;
  text-align: center;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.template-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-button:hover {
  background-color: var(--bg-hover);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.template-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.template-button span:last-child {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.welcome-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* الرسائل */
.message {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  animation: fadeIn 0.3s ease-in;
}

.message-user {
  flex-direction: row-reverse;
}

.message-user .message-content {
  background: linear-gradient(135deg, var(--primary-color), #0056b3);
  color: white;
  border-radius: 18px 18px 4px 18px;
}

.message-assistant .message-content {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 18px 18px 18px 4px;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), #0056b3);
  color: white;
  flex-shrink: 0;
}

.message-user .message-avatar {
  background: linear-gradient(135deg, var(--success-color), #1e7e34);
}

.message-content {
  max-width: 70%;
  padding: var(--spacing-md);
  position: relative;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.message-role {
  font-weight: 600;
  color: inherit;
}

.message-timestamp {
  opacity: 0.7;
  font-size: 0.75rem;
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-text p {
  margin: 0;
}

.message-text h1,
.message-text h2,
.message-text h3,
.message-text h4,
.message-text h5,
.message-text h6 {
  margin: var(--spacing-md) 0 var(--spacing-sm) 0;
}

.message-text ul,
.message-text ol {
  margin: var(--spacing-sm) 0;
  padding-right: var(--spacing-lg);
}

.message-text blockquote {
  border-right: 4px solid var(--primary-color);
  padding-right: var(--spacing-md);
  margin: var(--spacing-md) 0;
  font-style: italic;
  opacity: 0.8;
}

.message-text code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.message-text pre {
  background-color: #f8f9fa;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  overflow-x: auto;
  margin: var(--spacing-md) 0;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message:hover .message-actions {
  opacity: 1;
}

.btn-icon {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: 4px;
  cursor: pointer;
  color: inherit;
  opacity: 0.6;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

/* مؤشر الكتابة */
.typing-indicator {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  animation: fadeIn 0.3s ease-in;
}

.typing-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), #0056b3);
  color: white;
}

.typing-content {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 18px 18px 18px 4px;
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-style: italic;
}

/* رسالة الخطأ */
.error-message {
  background-color: var(--danger-color);
  color: white;
  padding: var(--spacing-md);
  margin: var(--spacing-md);
  border-radius: var(--border-radius);
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: slideInDown 0.3s ease-out;
}

.error-message button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.error-message button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* منطقة إدخال الرسائل */
.chat-input-container {
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  padding: var(--spacing-md) var(--spacing-lg);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    min-height: auto;
  }
  
  .chat-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .messages-container {
    padding: var(--spacing-sm);
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .welcome-features {
    grid-template-columns: 1fr;
  }
  
  .chat-input-container {
    padding: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: 90%;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
  }
  
  .welcome-content {
    padding: var(--spacing-md);
  }
}

/* أنيميشن إضافي */
@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين النصوص الطويلة */
.message-text {
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* تحسين الكود */
.message-text pre code {
  background: none;
  padding: 0;
}

/* تحسين الجداول */
.message-text table {
  border-collapse: collapse;
  width: 100%;
  margin: var(--spacing-md) 0;
}

.message-text th,
.message-text td {
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm);
  text-align: right;
}

.message-text th {
  background-color: var(--bg-secondary);
  font-weight: 600;
}
