.message-input-form {
  width: 100%;
  position: relative;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  background-color: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: 24px;
  padding: var(--spacing-sm);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.input-container:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.input-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: none;
  border-radius: 50%;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.input-action-btn:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
  transform: scale(1.05);
}

.input-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.textarea-container {
  flex: 1;
  position: relative;
  min-height: 36px;
}

.message-textarea {
  width: 100%;
  min-height: 36px;
  max-height: 120px;
  border: none;
  outline: none;
  background: none;
  resize: none;
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  padding: 6px 12px;
  border-radius: 18px;
  transition: background-color 0.2s ease;
}

.message-textarea:focus {
  background-color: var(--bg-secondary);
}

.message-textarea::placeholder {
  color: var(--text-secondary);
}

.message-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.character-counter {
  position: absolute;
  bottom: 2px;
  left: 8px;
  font-size: 0.7rem;
  color: var(--text-secondary);
  pointer-events: none;
}

.character-counter .warning {
  color: var(--warning-color);
  font-weight: 600;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.voice-btn {
  position: relative;
}

.voice-btn.recording {
  background-color: var(--danger-color);
  color: white;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  background-color: var(--border-color);
  color: var(--text-secondary);
}

.send-btn.active {
  background: linear-gradient(135deg, var(--primary-color), #0056b3);
  color: white;
  transform: scale(1);
}

.send-btn.active:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: scale(1);
}

/* شريط التحميل */
.loading-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--bg-secondary);
  border-radius: 0 0 24px 24px;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), #0056b3);
  animation: loading 2s infinite;
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* نصائح الاستخدام */
.input-hints {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.recording-indicator {
  color: var(--danger-color);
  font-weight: 600;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.5;
  }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .input-container {
    padding: var(--spacing-xs);
    border-radius: 20px;
  }
  
  .input-action-btn,
  .send-btn {
    width: 32px;
    height: 32px;
  }
  
  .message-textarea {
    font-size: var(--font-size-sm);
    padding: 4px 8px;
  }
  
  .input-hints {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .input-container {
    border-radius: 16px;
  }
  
  .input-action-btn,
  .send-btn {
    width: 28px;
    height: 28px;
  }
  
  .input-actions {
    gap: 2px;
  }
  
  .character-counter {
    display: none;
  }
}

/* تحسين إمكانية الوصول */
.input-action-btn:focus,
.send-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.message-textarea:focus {
  outline: none;
}

/* تأثيرات إضافية */
.send-btn.active {
  animation: sendReady 0.3s ease-out;
}

@keyframes sendReady {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* تحسين النص العربي */
.message-textarea[dir="rtl"] {
  text-align: right;
}

.message-textarea[dir="ltr"] {
  text-align: left;
}

/* تحسين التمرير */
.message-textarea::-webkit-scrollbar {
  width: 4px;
}

.message-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.message-textarea::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

.message-textarea::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}
