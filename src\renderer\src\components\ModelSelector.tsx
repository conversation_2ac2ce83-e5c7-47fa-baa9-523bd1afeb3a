import React, { useState, useEffect } from 'react';
import { ChevronDown, Cpu, Zap, Globe } from 'lucide-react';
import { AIModel } from '../types';
import './ModelSelector.css';

interface ModelSelectorProps {
  models: AIModel[];
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  servicesStatus?: {
    lmStudio: { available: boolean; modelsCount: number; status: string };
    msty: { available: boolean; modelsCount: number; status: string };
    openRouter: { available: boolean; hasApiKey: boolean; status: string };
  };
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModel,
  onModelChange,
  servicesStatus = {
    lmStudio: { available: false, modelsCount: 0, status: '' },
    msty: { available: false, modelsCount: 0, status: '' },
    openRouter: { available: false, hasApiKey: false, status: '' },
  },
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [serviceStatuses, setServiceStatuses] = useState({
    lmStudio: { available: false, modelsCount: 0, status: '' },
    msty: { available: false, modelsCount: 0, status: '' },
    openRouter: { available: false, hasApiKey: false, status: '' },
  });

  useEffect(() => {
    const fetchServiceStatuses = async () => {
      try {
        // التحقق من وجود واجهة Electron بشكل آمن
        if (window.electronAPI?.getServicesStatus) {
          try {
            const result: any = await window.electronAPI.getServicesStatus();
            if (result?.success) {
              setServiceStatuses(result.status);
              console.log('Service statuses fetched in ModelSelector:', result.status);
              
              // التحقق الفعلي من اتصال OpenRouter
              if (result.status.openRouter.available) {
                try {
                  const orResponse = await fetch('https://openrouter.ai/api/v1/auth/key', {
                    method: 'GET',
                    headers: {
                      'Authorization': `Bearer ${result.status.openRouter.apiKey}`
                    }
                  });
                  if (!orResponse.ok) {
                    setServiceStatuses(prev => ({
                      ...prev,
                      openRouter: { ...prev.openRouter, available: false, status: 'فشل التحقق من المفتاح' }
                    }));
                  }
                } catch (error) {
                  console.error('Error verifying OpenRouter:', error);
                  setServiceStatuses(prev => ({
                    ...prev,
                    openRouter: { ...prev.openRouter, available: false, status: 'خطأ في الاتصال' }
                  }));
                }
              }
              
              // التحقق الفعلي من اتصال Ollama
              if (result.status.msty.available) {
                try {
                  const ollamaResponse = await fetch('http://localhost:11434/api/tags');
                  if (!ollamaResponse.ok) {
                    setServiceStatuses(prev => ({
                      ...prev,
                      msty: { ...prev.msty, available: false, status: 'غير متصل' }
                    }));
                  }
                } catch (error) {
                  console.error('Error verifying Ollama:', error);
                  setServiceStatuses(prev => ({
                    ...prev,
                    msty: { ...prev.msty, available: false, status: 'خطأ في الاتصال' }
                  }));
                }
              }
            } else {
              console.error('Failed to fetch service statuses:', result?.error || 'Unknown error');
              throw new Error(result?.error || 'Unknown error');
            }
          } catch (error) {
            console.error('Error in electronAPI.getServicesStatus:', error);
            setServiceStatuses({
              lmStudio: { available: false, modelsCount: 0, status: 'غير متصل' },
              msty: { available: false, modelsCount: 0, status: 'غير متصل' },
              openRouter: { available: false, hasApiKey: false, status: 'غير متصل' }
            });
          }
        } else {
          // في حالة عدم وجود واجهة Electron (وضع التطوير أو الويب)
          console.log('Running in web mode - waiting for real service status');
          setServiceStatuses({
            lmStudio: { available: false, modelsCount: 0, status: 'غير متصل' },
            msty: { available: false, modelsCount: 0, status: 'غير متصل' },
            openRouter: { available: false, hasApiKey: false, status: 'غير متصل' }
          });
          // إضافة رسالة تحذير للمستخدم عند فشل الاتصال
          console.warn('فشل الاتصال بالخدمات. يرجى التحقق من اتصال الإنترنت وإعدادات الخدمات.');
        }
      } catch (error) {
        console.error('Error fetching service statuses:', error);
        // في حالة الخطأ، استخدام حالة عدم الاتصال
        setServiceStatuses({
          lmStudio: { available: false, modelsCount: 0, status: 'غير متصل' },
          msty: { available: false, modelsCount: 0, status: 'غير متصل' },
          openRouter: { available: false, hasApiKey: false, status: 'غير متصل' }
        });
      }
    };

    fetchServiceStatuses();
  }, []);

  const selectedModelData = models.find((m) => m.id === selectedModel);

  const getModelIcon = (model: AIModel) => {
    // النموذج الخاص - الرفيق المخلص
    if (model.id.includes('special/loyal-companion')) {
      return '🌟💪'; // رمز صداقتنا
    }

    // نماذج السينما والدراما
    if (model.id.includes('cinema/')) {
      if (model.id.includes('dialogue')) return '🗣️'; // حوارات
      if (model.id.includes('screenplay')) return '📝'; // سيناريو
      return '🎥'; // سينما
    }

    // النماذج الأدبية
    if (model.id.includes('literary/')) {
      if (model.id.includes('abnoudy') || model.id.includes('negm')) return '🎵'; // شعر
      return '📚'; // رواية
    }

    // النماذج التقنية
    if (model.id.includes('llama')) return '🦙';
    if (model.id.includes('gemini')) return '💎';
    if (model.id.includes('mistral')) return '🌪️';
    if (model.id.includes('deepseek')) return '🔍';
    if (model.id.includes('gemma')) return '💠';
    return '🤖';
  };

  const getModelDescription = (model: AIModel) => {
    const contextLength = model.context_length
      ? `${model.context_length.toLocaleString()} رمز`
      : 'غير محدد';
    const isModerated = model.top_provider?.is_moderated ? 'مُراقب' : 'غير مُراقب';

    return `${contextLength} • ${isModerated}`;
  };

  const handleModelSelect = (modelId: string) => {
    if (serviceStatuses.openRouter.available || serviceStatuses.msty.available) {
      onModelChange(modelId);
      setIsOpen(false);
    } else {
      // إضافة رسالة تحذير عند محاولة اختيار نموذج بدون اتصال
      alert('الخدمة غير متاحة. يرجى التحقق من اتصال الخدمة قبل اختيار النموذج.');
    }
  };
  
  // دالة عرض خيار النموذج
  const renderModelOption = (model: AIModel) => {
    const isLocalModel = model.name.includes('🏠') || model.id.includes('local') || model.id.includes('ollama');
    const isAvailable = isLocalModel
      ? serviceStatuses.msty.available && serviceStatuses.msty.modelsCount > 0
      : serviceStatuses.openRouter.available && serviceStatuses.openRouter.hasApiKey;

    return (
      <button
        key={model.id}
        className={`model-option ${model.id === selectedModel ? 'selected' : ''} ${!isAvailable ? 'disabled' : ''}`}
        onClick={() => isAvailable && handleModelSelect(model.id)}
        disabled={!isAvailable}
        title={
          !isAvailable
            ? isLocalModel
              ? 'Ollama غير متاح - تأكد من تشغيل الخدمة المحلية'
              : 'OpenRouter غير متاح - تحقق من مفتاح API والاتصال'
            : undefined
        }
      >
        <div className="model-option-content">
          <div className="model-option-header">
            <span className="model-icon">
              {getModelIcon(model)}
            </span>
            {!isAvailable && (
              <span
                className="error-badge"
                title={isLocalModel ? 'الخدمة المحلية غير متاحة' : 'OpenRouter غير متصل'}
              >
                ⚠️
              </span>
            )}
            <span className="model-name">{model.name}</span>
            {model.id.includes(':free') && (
              <span className="free-badge">
                <Zap size={12} />
                مجاني
              </span>
            )}
          </div>

          <div className="model-description">
            {model.description}
          </div>

          <div className="model-specs">
            <div className="spec">
              <Globe size={12} />
              <span>
                {model.context_length?.toLocaleString() || 'غير محدد'} رمز
              </span>
            </div>
            <div className="spec">
              <span>
                {model.top_provider?.is_moderated ? '🛡️ مُراقب' : '🔓 غير مُراقب'}
              </span>
            </div>
          </div>
        </div>
      </button>
    );
  };

  return (
    <div className="model-selector">
      <div className="model-download-options">
        <button 
          className="download-button"
          onClick={() => window.electronAPI?.openModelDownload('huggingface')}
          title="تنزيل نماذج من Hugging Face"
        >
          🤗 Hugging Face
        </button>
        <button 
          className="download-button"
          onClick={() => window.electronAPI?.openModelDownload('ollama')}
          title="تنزيل نماذج من Ollama"
        >
          🦙 Ollama
        </button>
        <button 
          className="download-button"
          onClick={() => window.electronAPI?.openModelDownload('openrouter')}
          title="تنزيل نماذج من Open Router"
        >
          🌐 Open Router
        </button>
      </div>
      <button
        className="model-selector-trigger"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <div className="selected-model">
          <span className="model-icon">
            {selectedModelData ? getModelIcon(selectedModelData) : '🤖'}
          </span>
          <div className="model-info">
            <span className="model-name">
              {selectedModelData?.name || 'اختر نموذج'}
            </span>
            <span className="model-meta">
              {selectedModelData ? getModelDescription(selectedModelData) : 'لم يتم اختيار نموذج'}
            </span>
          </div>
        </div>
        <ChevronDown
          size={16}
          className={`chevron ${isOpen ? 'chevron-open' : ''}`}
        />
      </button>

      {isOpen && (
        <div className="model-dropdown">
          <div className="dropdown-header">
            <Cpu size={16} />
            <span>النماذج المتاحة</span>
          </div>
          <div className="service-status">
            <span className="service-name">Ollama:</span>
            <span className={`status ${serviceStatuses.msty.available ? 'available' : 'unavailable'}`}>
              {serviceStatuses.msty.status}
            </span>
          </div>
          <div className="service-status">
            <span className="service-name">OpenRouter:</span>
            <span className={`status ${serviceStatuses.openRouter.available ? 'available' : 'unavailable'}`}>
              {serviceStatuses.openRouter.status}
            </span>
          </div>

          <div className="models-list">
            {models.length === 0 ? (
              <div className="no-models">
                <span>لا توجد نماذج متاحة</span>
              </div>
            ) : (
              <>
                {/* فئة النماذج العامة */}
                <div className="model-category">
                  <div className="category-title">
                    <span className="category-icon">🤖</span>
                    <span>نماذج المحادثة العامة</span>
                  </div>
                  {models
                    .filter(model =>
                      !model.id.includes('novel') &&
                      !model.id.includes('poetry') &&
                      !model.id.includes('literary') &&
                      !model.id.includes('cinema') &&
                      !model.id.includes('code') &&
                      (model.id.includes('llama') || model.id.includes('mistral') || model.id.includes('gemini'))
                    )
                    .map((model) => renderModelOption(model))}
                </div>

                {/* فئة نماذج الروايات */}
                <div className="model-category">
                  <div className="category-title">
                    <span className="category-icon">📚</span>
                    <span>نماذج كتابة الروايات</span>
                  </div>
                  {models
                    .filter(model =>
                      model.id.includes('novel') ||
                      (model.id.includes('literary') && !model.id.includes('poetry'))
                    )
                    .map((model) => renderModelOption(model))}
                </div>

                {/* فئة نماذج الشعر */}
                <div className="model-category">
                  <div className="category-title">
                    <span className="category-icon">🎵</span>
                    <span>نماذج كتابة الشعر</span>
                  </div>
                  {models
                    .filter(model =>
                      model.id.includes('poetry') ||
                      model.id.includes('abnoudy') ||
                      model.id.includes('negm')
                    )
                    .map((model) => renderModelOption(model))}
                </div>

                {/* فئة نماذج السينما والدراما */}
                <div className="model-category">
                  <div className="category-title">
                    <span className="category-icon">🎬</span>
                    <span>نماذج السينما والدراما</span>
                  </div>
                  {models
                    .filter(model =>
                      model.id.includes('cinema') ||
                      model.id.includes('dialogue') ||
                      model.id.includes('screenplay')
                    )
                    .map((model) => renderModelOption(model))}
                </div>

                {/* فئة نماذج البرمجة */}
                <div className="model-category">
                  <div className="category-title">
                    <span className="category-icon">💻</span>
                    <span>نماذج البرمجة</span>
                  </div>
                  {models
                    .filter(model =>
                      model.id.includes('code') ||
                      model.id.includes('deepseek')
                    )
                    .map((model) => renderModelOption(model))}
                </div>
              </>
            )}
          </div>

          <div className="dropdown-footer">
            <small>النماذج المجانية فقط • بواسطة OpenRouter</small>
          </div>
        </div>
      )}

      {/* طبقة الخلفية لإغلاق القائمة */}
      {isOpen && (
        <div
          className="model-selector-overlay"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ModelSelector;