import React, { useState, useEffect } from 'react'
import { 
  X, 
  Save, 
  Key, 
  Palette, 
  Globe, 
  Volume2, 
  Download, 
  Upload,
  Shield,
  Cpu,
  Settings as SettingsIcon,
  Info
} from 'lucide-react'
import { AIModel, AppSettings } from '../types'
import './SettingsPanel.css'

interface SettingsPanelProps {
  settings: AppSettings
  availableModels: AIModel[]
  onSaveSettings: (settings: Partial<AppSettings>) => Promise<any>
  onClose: () => void
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  settings,
  availableModels,
  onSaveSettings,
  onClose
}) => {
  const [formData, setFormData] = useState<Partial<AppSettings>>(settings)
  const [activeTab, setActiveTab] = useState('general')
  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState<string | null>(null)

  useEffect(() => {
    setFormData(settings)
  }, [settings])

  const handleInputChange = (key: keyof AppSettings, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }))
  }

  const handleSave = async () => {
    setIsSaving(true)
    setSaveMessage(null)

    try {
      const result = await onSaveSettings(formData)
      if (result.success) {
        setSaveMessage('تم حفظ الإعدادات بنجاح')
        setTimeout(() => setSaveMessage(null), 3000)
      } else {
        setSaveMessage('فشل في حفظ الإعدادات: ' + result.error)
      }
    } catch (error) {
      setSaveMessage('حدث خطأ أثناء حفظ الإعدادات')
    } finally {
      setIsSaving(false)
    }
  }

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(formData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'ai-chat-settings.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string)
          setFormData(prev => ({ ...prev, ...importedSettings }))
          setSaveMessage('تم استيراد الإعدادات بنجاح')
        } catch (error) {
          setSaveMessage('فشل في استيراد الإعدادات: ملف غير صحيح')
        }
      }
      reader.readAsText(file)
    }
  }

  const tabs = [
    { id: 'general', label: 'عام', icon: SettingsIcon },
    { id: 'api', label: 'API', icon: Key },
    { id: 'appearance', label: 'المظهر', icon: Palette },
    { id: 'advanced', label: 'متقدم', icon: Cpu },
    { id: 'about', label: 'حول', icon: Info }
  ]

  const renderGeneralTab = () => (
    <div className="settings-tab-content">
      <div className="setting-group">
        <h3>النموذج الافتراضي</h3>
        <select
          value={formData.default_model || ''}
          onChange={(e) => handleInputChange('default_model', e.target.value)}
          className="form-control"
        >
          <option value="">اختر النموذج الافتراضي</option>
          {availableModels.map(model => (
            <option key={model.id} value={model.id}>
              {model.name}
            </option>
          ))}
        </select>
        <small className="setting-description">
          النموذج الذي سيتم استخدامه افتراضياً في المحادثات الجديدة
        </small>
      </div>

      <div className="setting-group">
        <h3>اللغة</h3>
        <select
          value={formData.language || 'ar'}
          onChange={(e) => handleInputChange('language', e.target.value)}
          className="form-control"
        >
          <option value="ar">العربية</option>
          <option value="en">English</option>
        </select>
      </div>

      <div className="setting-group">
        <h3>الحفظ التلقائي</h3>
        <label className="checkbox-label">
          <input
            type="checkbox"
            checked={formData.auto_save || false}
            onChange={(e) => handleInputChange('auto_save', e.target.checked)}
          />
          <span className="checkmark"></span>
          حفظ المحادثات تلقائياً
        </label>
      </div>

      <div className="setting-group">
        <h3>إظهار الطوابع الزمنية</h3>
        <label className="checkbox-label">
          <input
            type="checkbox"
            checked={formData.show_timestamps || false}
            onChange={(e) => handleInputChange('show_timestamps', e.target.checked)}
          />
          <span className="checkmark"></span>
          إظهار وقت إرسال الرسائل
        </label>
      </div>

      <div className="setting-group">
        <h3>تفعيل الأصوات</h3>
        <label className="checkbox-label">
          <input
            type="checkbox"
            checked={formData.enable_sound || false}
            onChange={(e) => handleInputChange('enable_sound', e.target.checked)}
          />
          <span className="checkmark"></span>
          تشغيل أصوات الإشعارات
        </label>
      </div>
    </div>
  )

  const renderAPITab = () => (
    <div className="settings-tab-content">
      <div className="setting-group">
        <h3>مفتاح OpenRouter API</h3>
        <input
          type="password"
          value={formData.openrouter_api_key || ''}
          onChange={(e) => handleInputChange('openrouter_api_key', e.target.value)}
          placeholder="sk-or-v1-..."
          className="form-control"
        />
        <small className="setting-description">
          مفتاح API الخاص بك من OpenRouter. يمكنك الحصول عليه من{' '}
          <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer">
            openrouter.ai
          </a>
        </small>
      </div>

      <div className="setting-group">
        <h3>الحد الأقصى للرموز</h3>
        <input
          type="number"
          value={formData.max_tokens || 2048}
          onChange={(e) => handleInputChange('max_tokens', parseInt(e.target.value))}
          min="100"
          max="8192"
          className="form-control"
        />
        <small className="setting-description">
          الحد الأقصى لعدد الرموز في الاستجابة (100-8192)
        </small>
      </div>

      <div className="setting-group">
        <h3>درجة الحرارة</h3>
        <input
          type="range"
          value={formData.temperature || 0.7}
          onChange={(e) => handleInputChange('temperature', parseFloat(e.target.value))}
          min="0"
          max="2"
          step="0.1"
          className="range-input"
        />
        <div className="range-value">{formData.temperature || 0.7}</div>
        <small className="setting-description">
          تحكم في إبداعية الاستجابات (0 = محافظ، 2 = إبداعي)
        </small>
      </div>
    </div>
  )

  const renderAppearanceTab = () => (
    <div className="settings-tab-content">
      <div className="setting-group">
        <h3>السمة</h3>
        <div className="theme-options">
          <label className="theme-option">
            <input
              type="radio"
              name="theme"
              value="light"
              checked={formData.theme === 'light'}
              onChange={(e) => handleInputChange('theme', e.target.value)}
            />
            <div className="theme-preview light">
              <div className="theme-header"></div>
              <div className="theme-content"></div>
            </div>
            <span>فاتح</span>
          </label>

          <label className="theme-option">
            <input
              type="radio"
              name="theme"
              value="dark"
              checked={formData.theme === 'dark'}
              onChange={(e) => handleInputChange('theme', e.target.value)}
            />
            <div className="theme-preview dark">
              <div className="theme-header"></div>
              <div className="theme-content"></div>
            </div>
            <span>مظلم</span>
          </label>
        </div>
      </div>

      <div className="setting-group">
        <h3>حجم الخط</h3>
        <select
          value={formData.font_size || 'medium'}
          onChange={(e) => handleInputChange('font_size', e.target.value)}
          className="form-control"
        >
          <option value="small">صغير</option>
          <option value="medium">متوسط</option>
          <option value="large">كبير</option>
        </select>
      </div>
    </div>
  )

  const renderAdvancedTab = () => (
    <div className="settings-tab-content">
      <div className="setting-group">
        <h3>تصدير الإعدادات</h3>
        <button
          className="btn btn-outline"
          onClick={handleExportSettings}
        >
          <Download size={16} />
          تصدير الإعدادات
        </button>
        <small className="setting-description">
          تصدير جميع الإعدادات إلى ملف JSON
        </small>
      </div>

      <div className="setting-group">
        <h3>استيراد الإعدادات</h3>
        <label className="file-input-label">
          <Upload size={16} />
          استيراد الإعدادات
          <input
            type="file"
            accept=".json"
            onChange={handleImportSettings}
            className="file-input"
          />
        </label>
        <small className="setting-description">
          استيراد الإعدادات من ملف JSON
        </small>
      </div>

      <div className="setting-group">
        <h3>إعادة تعيين الإعدادات</h3>
        <button
          className="btn btn-danger"
          onClick={() => {
            if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
              setFormData({
                theme: 'light',
                language: 'ar',
                auto_save: true,
                show_timestamps: true,
                enable_sound: false,
                font_size: 'medium',
                max_tokens: 2048,
                temperature: 0.7
              })
            }
          }}
        >
          إعادة تعيين
        </button>
        <small className="setting-description">
          إعادة جميع الإعدادات إلى القيم الافتراضية
        </small>
      </div>
    </div>
  )

  const renderAboutTab = () => (
    <div className="settings-tab-content">
      <div className="about-section">
        <div className="app-info">
          <h2>بوت الدردشة الذكي</h2>
          <p className="version">الإصدار 1.0.0</p>
          <p className="description">
            تطبيق دردشة ذكي متقدم يستخدم نماذج الذكاء الاصطناعي المفتوحة المصدر
            لتوفير تجربة محادثة طبيعية ومفيدة.
          </p>
        </div>

        <div className="features-list">
          <h3>الميزات الرئيسية</h3>
          <ul>
            <li>دعم نماذج ذكاء اصطناعي متعددة</li>
            <li>واجهة مستخدم حديثة ومتجاوبة</li>
            <li>دعم اللغة العربية والإنجليزية</li>
            <li>تشفير البيانات وحماية الخصوصية</li>
            <li>تصدير المحادثات بصيغ متعددة</li>
            <li>عمل دون اتصال بالإنترنت</li>
          </ul>
        </div>

        <div className="credits">
          <h3>الشكر والتقدير</h3>
          <ul>
            <li>OpenRouter API للوصول إلى النماذج</li>
            <li>Electron لبناء التطبيق</li>
            <li>React لواجهة المستخدم</li>
            <li>TypeScript للتطوير الآمن</li>
          </ul>
        </div>

        <div className="contact-info">
          <h3>الدعم والمساعدة</h3>
          <p>
            للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى زيارة صفحة المشروع
            أو التواصل مع فريق التطوير.
          </p>
        </div>
      </div>
    </div>
  )

  return (
    <div className="settings-overlay">
      <div className="settings-panel">
        {/* رأس لوحة الإعدادات */}
        <div className="settings-header">
          <h2>الإعدادات</h2>
          <button className="close-btn" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        {/* علامات التبويب */}
        <div className="settings-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* محتوى الإعدادات */}
        <div className="settings-content">
          {activeTab === 'general' && renderGeneralTab()}
          {activeTab === 'api' && renderAPITab()}
          {activeTab === 'appearance' && renderAppearanceTab()}
          {activeTab === 'advanced' && renderAdvancedTab()}
          {activeTab === 'about' && renderAboutTab()}
        </div>

        {/* تذييل لوحة الإعدادات */}
        <div className="settings-footer">
          {saveMessage && (
            <div className={`save-message ${saveMessage.includes('فشل') ? 'error' : 'success'}`}>
              {saveMessage}
            </div>
          )}
          
          <div className="footer-actions">
            <button
              className="btn btn-secondary"
              onClick={onClose}
            >
              إلغاء
            </button>
            <button
              className="btn btn-primary"
              onClick={handleSave}
              disabled={isSaving}
            >
              <Save size={16} />
              {isSaving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel
