@echo off
title ULTIMATE FIX - AI Chat Bot

echo ========================================
echo      ULTIMATE FIX - AI Chat Bot
echo ========================================
echo.

echo Step 1: Clean up...
if exist "package-lock.json" del "package-lock.json"
if exist "yarn.lock" del "yarn.lock"
if exist "dist" rmdir /s /q "dist"
if exist "dist-renderer" rmdir /s /q "dist-renderer"
echo Clean up completed!
echo.

echo Step 2: Install core dependencies...
echo Installing Electron...
npm install electron@latest --save-dev --no-optional --silent

echo Installing TypeScript...
npm install typescript@latest --save-dev --no-optional --silent

echo Installing Vite and React...
npm install vite@latest @vitejs/plugin-react@latest --save-dev --no-optional --silent
npm install react@latest react-dom@latest --save --no-optional --silent

echo Installing Types...
npm install @types/node @types/react @types/react-dom @types/electron --save-dev --no-optional --silent

echo Installing other dependencies...
npm install concurrently electron-store axios crypto-js lucide-react uuid --save --no-optional --silent

echo Dependencies installed!
echo.

echo Step 3: Build TypeScript...
npx tsc -p tsconfig.main.json
echo TypeScript build completed!
echo.

echo Step 4: Run the app...
echo Starting Electron...
npx electron .

echo.
echo ULTIMATE FIX completed!
pause
