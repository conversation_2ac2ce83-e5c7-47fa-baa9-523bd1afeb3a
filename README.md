# بوت الدردشة الذكي - AI Chat Bot

تطبيق دردشة ذكي متقدم يعمل على نظام الويندوز باستخدام تقنيات الذكاء الاصطناعي المفتوحة المصدر.

## الميزات الرئيسية

### 🤖 نماذج الذكاء الاصطناعي المتقدمة
- دعم نماذج متعددة من OpenRouter API
- إمكانية التبديل بين النماذج حسب الحاجة
- نماذج مجانية عالية الجودة:
  - `meta-llama/llama-3.3-8b-instruct:free`
  - `deepseek/deepseek-prover-v2:free`
  - `google/gemini-2.0-flash-exp:free`
  - `mistralai/mistral-7b-instruct:free`
  - `google/gemma-2-9b-it:free`

### 🌐 دعم متعدد اللغات
- دعم كامل للغة العربية والإنجليزية
- واجهة مستخدم باللغة العربية
- تحسين عرض النصوص العربية

### 💾 إدارة المحادثات
- حفظ المحادثات تلقائياً
- تشفير البيانات المحلية
- تنظيم المحادثات حسب التاريخ
- البحث في المحادثات

### 📤 تصدير المحادثات
- تصدير بصيغة TXT
- تصدير بصيغة PDF
- تصدير بصيغة JSON
- تصدير بصيغة HTML

### 🎨 واجهة مستخدم حديثة
- تصميم متجاوب وحديث
- دعم الوضع المظلم والفاتح
- أنيميشن سلس وتفاعلي
- تحسين تجربة المستخدم

### 🔒 الأمان والخصوصية
- تشفير البيانات المحلية
- عدم تخزين البيانات على خوادم خارجية
- حماية مفاتيح API
- إمكانية حذف البيانات بأمان

## متطلبات النظام

- **نظام التشغيل**: Windows 10/11 (64-bit)
- **الذاكرة**: 4 GB RAM كحد أدنى، 8 GB مُوصى به
- **مساحة التخزين**: 500 MB مساحة فارغة
- **الاتصال**: اتصال بالإنترنت للنماذج الخارجية

## التثبيت والتشغيل

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من [nodejs.org](https://nodejs.org/)

### 2. استنساخ المشروع
```bash
git clone <repository-url>
cd ai-chat-bot
```

### 3. تثبيت التبعيات
```bash
npm install
```

### 4. إعداد مفتاح API
- احصل على مفتاح API من [OpenRouter](https://openrouter.ai/)
- أو استخدم المفتاح المُدمج: `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`

### 5. تشغيل التطبيق في وضع التطوير
```bash
npm run dev
```

### 6. بناء التطبيق للإنتاج
```bash
npm run build
npm run dist
```

## استخدام التطبيق

### البدء السريع
1. افتح التطبيق
2. انتظر تحميل النماذج المتاحة
3. اختر النموذج المناسب من القائمة العلوية
4. ابدأ الكتابة في حقل الرسالة
5. اضغط Enter أو زر الإرسال

### إدارة المحادثات
- **محادثة جديدة**: اضغط على زر "محادثة جديدة"
- **تحديد محادثة**: اضغط على المحادثة من الشريط الجانبي
- **حذف محادثة**: اضغط على النقاط الثلاث → حذف
- **تصدير محادثة**: اضغط على النقاط الثلاث → تصدير

### الإعدادات
- **الوصول للإعدادات**: اضغط على أيقونة الإعدادات في الشريط الجانبي
- **تغيير السمة**: اختر بين الوضع الفاتح والمظلم
- **تحديث مفتاح API**: أدخل مفتاحك الخاص في تبويب API
- **تخصيص النموذج الافتراضي**: اختر النموذج المفضل

## البنية التقنية

### التقنيات المستخدمة
- **Electron**: لبناء تطبيق سطح المكتب
- **React**: لواجهة المستخدم
- **TypeScript**: للتطوير الآمن
- **Vite**: لأدوات البناء السريعة
- **OpenRouter API**: للوصول إلى نماذج الذكاء الاصطناعي

### هيكل المشروع
```
src/
├── main/                 # العملية الرئيسية لـ Electron
│   └── main.ts
├── preload/             # ملفات preload للأمان
│   └── preload.ts
├── renderer/            # واجهة المستخدم
│   ├── src/
│   │   ├── components/  # مكونات React
│   │   ├── types.ts     # تعريفات الأنواع
│   │   ├── App.tsx      # المكون الرئيسي
│   │   └── main.tsx     # نقطة الدخول
│   └── index.html
├── services/            # خدمات التطبيق
│   ├── OpenRouterAPI.ts
│   └── ChatManager.ts
└── utils/               # أدوات مساعدة
    └── FileExporter.ts
```

## النماذج المدعومة

### النماذج المجانية
1. **Llama 3.3 8B Instruct**: نموذج Meta المتقدم للمحادثات
2. **DeepSeek Prover V2**: متخصص في المنطق والبرهان
3. **Gemini 2.0 Flash**: نموذج Google السريع
4. **Mistral 7B Instruct**: نموذج فرنسي متقدم
5. **Gemma 2 9B IT**: نموذج Google للمحادثات التقنية

### خصائص النماذج
- **السياق**: حتى 8192 رمز
- **اللغات**: دعم العربية والإنجليزية
- **الاستجابة**: سريعة وعالية الجودة
- **التكلفة**: مجانية بالكامل

## الأمان والخصوصية

### حماية البيانات
- تشفير AES-256 للبيانات المحلية
- عدم إرسال البيانات لخوادم غير مصرح بها
- حماية مفاتيح API في التخزين المحلي
- إمكانية حذف جميع البيانات

### الخصوصية
- لا يتم جمع بيانات شخصية
- المحادثات محفوظة محلياً فقط
- لا توجد تتبع أو تحليلات خارجية
- شفافية كاملة في استخدام البيانات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بـ API
```
الحل: تحقق من مفتاح API في الإعدادات
```

#### التطبيق لا يبدأ
```
الحل: تأكد من تثبيت Node.js وتشغيل npm install
```

#### النماذج لا تظهر
```
الحل: تحقق من الاتصال بالإنترنت وصحة مفتاح API
```

#### مشكلة في عرض النصوص العربية
```
الحل: تأكد من تثبيت خطوط النظام العربية
```

## المساهمة في المشروع

### كيفية المساهمة
1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. تطوير الميزة مع الاختبارات
4. إرسال Pull Request

### معايير الكود
- استخدام TypeScript
- اتباع معايير ESLint
- كتابة تعليقات واضحة
- اختبار الميزات الجديدة

## الدعم والمساعدة

### الحصول على المساعدة
- قراءة هذا الدليل أولاً
- البحث في المشاكل المعروفة
- إنشاء Issue جديد مع تفاصيل المشكلة
- التواصل مع فريق التطوير

### الإبلاغ عن الأخطاء
عند الإبلاغ عن خطأ، يرجى تضمين:
- وصف مفصل للمشكلة
- خطوات إعادة إنتاج الخطأ
- لقطات شاشة إن أمكن
- معلومات النظام والإصدار

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الشكر والتقدير

- **OpenRouter** لتوفير الوصول إلى نماذج الذكاء الاصطناعي
- **Electron** لإطار عمل التطبيقات المكتبية
- **React** لمكتبة واجهة المستخدم
- **TypeScript** للتطوير الآمن
- **المجتمع المفتوح المصدر** للأدوات والمكتبات

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. قد تحدث تغييرات في الواجهة والميزات.

للحصول على آخر التحديثات، تابع المشروع على GitHub.
