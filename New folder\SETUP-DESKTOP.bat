@echo off
title AI Chat Bot - Desktop Setup

echo ========================================
echo      AI Chat Bot - Desktop Setup
echo ========================================
echo.

echo Creating desktop shortcuts...

set "DESKTOP=%USERPROFILE%\Desktop"
set "PROJECT_PATH=%CD%"

echo Desktop: %DESKTOP%
echo Project: %PROJECT_PATH%
echo.

echo Creating AI Chat Bot shortcut...

echo @echo off > "%DESKTOP%\AI Chat Bot.bat"
echo title AI Chat Bot >> "%DESKTOP%\AI Chat Bot.bat"
echo cd /d "%PROJECT_PATH%" >> "%DESKTOP%\AI Chat Bot.bat"
echo echo Starting AI Chat Bot... >> "%DESKTOP%\AI Chat Bot.bat"
echo npx electron . >> "%DESKTOP%\AI Chat Bot.bat"
echo if errorlevel 1 ( >> "%DESKTOP%\AI Chat Bot.bat"
echo     echo Failed to start! >> "%DESKTOP%\AI Chat Bot.bat"
echo     echo Try running ULTIMATE-FIX.bat first >> "%DESKTOP%\AI Chat Bot.bat"
echo     pause >> "%DESKTOP%\AI Chat Bot.bat"
echo ^) >> "%DESKTOP%\AI Chat Bot.bat"

echo Creating Fix shortcut...

echo @echo off > "%DESKTOP%\Fix AI Chat Bot.bat"
echo title Fix AI Chat Bot >> "%DESKTOP%\Fix AI Chat Bot.bat"
echo cd /d "%PROJECT_PATH%" >> "%DESKTOP%\Fix AI Chat Bot.bat"
echo echo Fixing and starting AI Chat Bot... >> "%DESKTOP%\Fix AI Chat Bot.bat"
echo call ULTIMATE-FIX.bat >> "%DESKTOP%\Fix AI Chat Bot.bat"

echo Creating Info file...

echo AI Chat Bot - Desktop Application > "%DESKTOP%\AI Chat Bot Info.txt"
echo ================================== >> "%DESKTOP%\AI Chat Bot Info.txt"
echo. >> "%DESKTOP%\AI Chat Bot Info.txt"
echo How to use: >> "%DESKTOP%\AI Chat Bot Info.txt"
echo 1. Double-click "AI Chat Bot.bat" to start >> "%DESKTOP%\AI Chat Bot Info.txt"
echo 2. If it doesn't work, run "Fix AI Chat Bot.bat" >> "%DESKTOP%\AI Chat Bot Info.txt"
echo 3. Wait for installation to complete >> "%DESKTOP%\AI Chat Bot Info.txt"
echo. >> "%DESKTOP%\AI Chat Bot Info.txt"
echo Project location: %PROJECT_PATH% >> "%DESKTOP%\AI Chat Bot Info.txt"

echo.
echo SUCCESS! Created desktop shortcuts:
echo - AI Chat Bot.bat
echo - Fix AI Chat Bot.bat  
echo - AI Chat Bot Info.txt
echo.

explorer "%DESKTOP%"

echo Setup completed!
pause
