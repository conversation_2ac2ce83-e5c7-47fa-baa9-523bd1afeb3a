/* محر<PERSON> الكود المتطور */
.code-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.code-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
  height: 100vh;
}

/* شريط الأدوات العلوي */
.editor-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-bottom: 1px solid var(--border-color);
  min-height: 50px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

/* اختيار اللغة */
.language-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-selector:hover {
  border-color: var(--primary-color);
}

.language-selector:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* إعدادات المحرر */
.editor-settings {
  padding: 16px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.setting-group label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.setting-group input[type="range"] {
  width: 80px;
}

.setting-group input[type="checkbox"] {
  margin: 0;
}

/* حاوية المحرر الرئيسية */
.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-main {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

/* أرقام الأسطر */
.line-numbers {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: 16px 8px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  text-align: right;
  border-left: 1px solid var(--border-color);
  user-select: none;
  white-space: pre;
  overflow: hidden;
  min-width: 50px;
}

/* منطقة النص */
.code-textarea {
  flex: 1;
  padding: 16px;
  border: none;
  outline: none;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  tab-size: 2;
  direction: ltr;
  text-align: left;
}

.code-textarea::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.code-textarea:focus {
  background: var(--bg-primary);
}

/* منطقة الإخراج */
.output-container {
  height: 200px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

.output-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
}

.output-content {
  flex: 1;
  padding: 12px 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: var(--text-primary);
  background: #1a1a1a;
  color: #e0e0e0;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* شريط الأدوات السفلي */
.editor-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  min-height: 50px;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-right {
  display: flex;
  align-items: center;
}

.code-stats {
  font-size: 12px;
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
}

/* أزرار المحرر */
.code-editor .btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.code-editor .btn:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.code-editor .btn:active {
  transform: translateY(0);
}

.code-editor .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.code-editor .btn.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
  color: white;
  border-color: var(--primary-color);
  font-weight: 600;
}

.code-editor .btn.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, var(--primary-color) 100%);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.code-editor .btn.btn-outline {
  background: transparent;
  border-color: var(--border-color);
}

.code-editor .btn.btn-outline:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-color);
}

.code-editor .btn.btn-danger {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.code-editor .btn.btn-danger:hover {
  background: rgba(220, 53, 69, 0.1);
  border-color: var(--danger-color);
}

.code-editor .btn.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

/* حركة الدوران */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .editor-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .editor-settings {
    flex-direction: column;
    gap: 12px;
  }

  .output-container {
    height: 150px;
  }

  .editor-footer {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .footer-left {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .line-numbers {
    display: none;
  }

  .code-textarea {
    padding: 12px;
    font-size: 13px;
  }

  .output-container {
    height: 120px;
  }

  .code-editor .btn {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* تحسين شريط التمرير */
.output-content::-webkit-scrollbar {
  width: 6px;
}

.output-content::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.output-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.output-content::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* تأثيرات إضافية */
.code-editor {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين إمكانية الوصول */
.code-editor .btn:focus,
.language-selector:focus,
.code-textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* تحسين النصوص الطويلة */
.code-textarea,
.output-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}
