@echo off
chcp 65001 >nul
title تشغيل التطبيق - الحل البديل

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🧠 الحل العبقري البديل                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 تشغيل المشروع بطريقة بديلة...
echo.

:: تشغيل Electron مباشرة
echo 🚀 تشغيل Electron مباشرة...
echo.

:: فحص وجود Electron
if exist "node_modules\.bin\electron.cmd" (
    echo ✅ Electron موجود
    echo 🔄 تشغيل التطبيق...
    node_modules\.bin\electron.cmd .
) else (
    echo ⚠️ Electron غير موجود، محاولة تثبيت سريع...
    npm install electron --save-dev --no-progress --silent
    if exist "node_modules\.bin\electron.cmd" (
        echo ✅ تم تثبيت Electron
        echo 🔄 تشغيل التطبيق...
        node_modules\.bin\electron.cmd .
    ) else (
        echo ❌ فشل في تثبيت Electron
        echo 💡 جرب تشغيل: npm install
        pause
        exit /b 1
    )
)

echo.
echo 📝 انتهى التشغيل
pause
