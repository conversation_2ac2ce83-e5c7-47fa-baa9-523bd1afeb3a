/* مكتبة القوالب المتقدمة */
.prompts-library-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.prompts-library-panel {
  background: var(--bg-primary);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 90vw;
  max-width: 1200px;
  height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border-color);
  animation: slideUp 0.3s ease-out;
}

/* رأس اللوحة */
.prompts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
}

.prompts-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 12px;
}

.prompts-title p {
  margin: 4px 0 0 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.close-button {
  background: var(--bg-tertiary);
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 18px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.05);
}

/* إحصائيات سريعة */
.prompts-stats {
  display: flex;
  gap: 24px;
  padding: 16px 32px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-color);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

/* شريط البحث والفلاتر */
.prompts-controls {
  padding: 20px 32px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.search-box {
  margin-bottom: 16px;
}

.search-box input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.category-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.filter-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.filter-btn.active {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

/* قائمة القوالب */
.prompts-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 32px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  align-content: start;
}

.prompt-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.prompt-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--accent-color);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.prompt-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.prompt-icon {
  font-size: 20px;
}

.prompt-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.prompt-actions {
  display: flex;
  gap: 8px;
}

.favorite-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.favorite-btn:hover {
  background: var(--bg-tertiary);
  transform: scale(1.1);
}

.favorite-btn.active {
  color: #fbbf24;
}

.prompt-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.prompt-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 12px;
}

.prompt-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.tag.more {
  background: var(--accent-color);
  color: white;
}

.prompt-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.difficulty-badge {
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.usage-count {
  font-size: 11px;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 4px 8px;
  border-radius: 12px;
}

.prompt-variables {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.variables-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.variable {
  background: linear-gradient(135deg, var(--accent-color), #8b5cf6);
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

/* حالة عدم وجود قوالب */
.no-prompts {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.no-prompts-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-prompts h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.no-prompts p {
  margin: 0;
  font-size: 14px;
}

/* تذييل اللوحة */
.prompts-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.footer-info {
  color: var(--text-secondary);
  font-size: 14px;
}

.secondary-btn {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-btn:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* الرسوم المتحركة */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
  .prompts-library-panel {
    width: 95vw;
    height: 90vh;
  }

  .prompts-header,
  .prompts-controls,
  .prompts-footer {
    padding: 16px 20px;
  }

  .prompts-list {
    padding: 16px 20px;
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .prompts-stats {
    padding: 12px 20px;
    gap: 16px;
  }

  .category-filters {
    gap: 6px;
  }

  .filter-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}
