@echo off
chcp 65001 >nul
title 🚀 AI Chat Bot - Quick Dev Start

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🚀 AI Chat Bot - تشغيل سريع للتطوير                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: تحديد المسار الحالي
set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

echo 🔍 فحص سريع للمتطلبات...

:: فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر: 
node --version

:: فحص npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر: 
npm --version

:: فحص التبعيات
if not exist "node_modules" (
    echo ⚠️ التبعيات غير مثبتة، جاري التثبيت...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات!
        pause
        exit /b 1
    )
)

echo.
echo 🚀 بدء تشغيل التطبيق في وضع التطوير...
echo 📝 ملاحظة: سيتم إعادة تحميل التطبيق تلقائياً عند تغيير الملفات
echo 🌐 سيتم فتح التطبيق في المتصفح على: http://localhost:5173
echo.

:: تشغيل التطبيق
start "AI Chat Bot - Development Server" cmd /k "npm run dev"

:: انتظار قليل ثم فتح المتصفح
echo ⏳ انتظار بدء الخادم...
timeout /t 8 /nobreak >nul

echo 🌐 فتح التطبيق في المتصفح...
start http://localhost:5173

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              ✅ تم بدء التطبيق!                            ║
echo ║                                                                              ║
echo ║  🔗 رابط التطبيق: http://localhost:5173                                    ║
echo ║  🔧 وضع التطوير: نشط                                                       ║
echo ║  🔄 إعادة التحميل: تلقائية                                                  ║
echo ║                                                                              ║
echo ║  💡 نصائح:                                                                   ║
echo ║  • اتركه يعمل في الخلفية أثناء التطوير                                     ║
echo ║  • أي تغيير في الكود سيحدث التطبيق تلقائياً                               ║
echo ║  • اضغط Ctrl+C في نافذة الخادم لإيقاف التطبيق                            ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🎉 استمتع بالتطوير!
pause
