# 🟣 دليل استخدام Msty مع AI Chat Bot

## ما هو Msty؟

**Msty** هو تطبيق ذكاء اصطناعي محلي متقدم يوفر:
- 🏠 **تشغيل محلي**: خصوصية كاملة لبياناتك
- 🚀 **أداء سريع**: استجابة فورية بدون انتظار
- 🎯 **نماذج متعددة**: دعم لأحدث نماذج الذكاء الاصطناعي
- 💡 **سهولة الاستخدام**: واجهة بسيطة وأنيقة

## تحميل وتثبيت Msty

### 1. التحميل
- 🌐 **الموقع الرسمي**: [https://msty.app/](https://msty.app/)
- 📥 **حجم التحميل**: حوالي 100 MB
- 💻 **المتطلبات**: Windows 10/11, macOS, Linux

### 2. التثبيت
1. قم بتحميل الملف من الموقع الرسمي
2. شغل ملف التثبيت
3. اتبع التعليمات البسيطة
4. شغل Msty لأول مرة

### 3. إعداد النماذج
1. افتح Msty
2. اذهب إلى قسم "Models"
3. حمل نموذج مناسب مثل:
   - **Llama 3.1 8B** (موصى به للبداية)
   - **Mistral 7B** (سريع وفعال)
   - **CodeLlama** (للبرمجة)

## ربط Msty مع AI Chat Bot

### الطريقة الأولى: التشغيل التلقائي
```bash
# شغل الملف المخصص
RUN-WITH-MSTY.bat
```

### الطريقة الثانية: التشغيل اليدوي
1. شغل Msty أولاً
2. تأكد من تحميل نموذج
3. شغل AI Chat Bot
4. اذهب إلى الإعدادات
5. اختر "Msty" كخدمة مفضلة

## إعدادات Msty في التطبيق

### فتح إعدادات الذكاء الاصطناعي
1. افتح AI Chat Bot
2. اضغط على أيقونة الإعدادات ⚙️
3. اختر "LM Studio Settings"
4. ستجد قسم Msty

### حالة الاتصال
- ✅ **متصل**: Msty يعمل ومتاح
- ❌ **غير متصل**: تأكد من تشغيل Msty
- 🟡 **تحميل**: انتظر تحميل النموذج

### اختيار الخدمة المفضلة
- 🟣 **Msty (محلي)**: للخصوصية والسرعة
- 🏠 **LM Studio (محلي)**: بديل محلي آخر
- 🌐 **OpenRouter (سحابي)**: للنماذج المتقدمة

## نصائح للاستخدام الأمثل

### 1. اختيار النموذج المناسب
- **للدردشة العامة**: Llama 3.1 8B
- **للبرمجة**: CodeLlama 7B
- **للكتابة**: Mistral 7B
- **للترجمة**: متعدد اللغات

### 2. تحسين الأداء
- 🔧 **ذاكرة الوصول العشوائي**: 8GB+ موصى بها
- 💾 **مساحة التخزين**: 10GB+ للنماذج
- ⚡ **معالج الرسوميات**: يحسن السرعة

### 3. إدارة النماذج
- احتفظ بنموذج واحد مُحمل للسرعة
- احذف النماذج غير المستخدمة لتوفير المساحة
- حدث النماذج دورياً

## استكشاف الأخطاء وإصلاحها

### مشكلة: Msty لا يتصل
**الحلول:**
1. تأكد من تشغيل Msty
2. تحقق من تحميل نموذج
3. أعد تشغيل Msty
4. تحقق من المنفذ (Port 10000)

### مشكلة: بطء في الاستجابة
**الحلول:**
1. استخدم نموذج أصغر
2. أغلق التطبيقات الأخرى
3. تأكد من توفر ذاكرة كافية
4. استخدم معالج رسوميات إن أمكن

### مشكلة: رسائل خطأ
**الحلول:**
1. أعد تشغيل Msty
2. تحقق من تحديثات Msty
3. أعد تشغيل AI Chat Bot
4. تحقق من سجل الأخطاء

## مقارنة مع البدائل

| الميزة | Msty | LM Studio | OpenRouter |
|--------|------|-----------|------------|
| **الخصوصية** | ✅ محلي | ✅ محلي | ❌ سحابي |
| **السرعة** | ⚡ سريع | ⚡ سريع | 🌐 يعتمد على الإنترنت |
| **سهولة الاستخدام** | 🎯 ممتاز | 🎯 جيد | 🎯 بسيط |
| **النماذج المتاحة** | 🔢 متعدد | 🔢 متعدد | 🔢 كثير جداً |
| **التكلفة** | 💰 مجاني | 💰 مجاني | 💰 مدفوع جزئياً |

## الدعم والمساعدة

### الموارد الرسمية
- 🌐 **الموقع**: [https://msty.app/](https://msty.app/)
- 📚 **الوثائق**: متوفرة على الموقع
- 💬 **المجتمع**: Discord و Reddit

### الدعم المحلي
- 📧 **البريد الإلكتروني**: للمشاكل التقنية
- 💬 **الدردشة**: داخل التطبيق
- 🔧 **الإعدادات**: مساعد تلقائي

---

## 🎉 استمتع بتجربة الذكاء الاصطناعي المحلي!

**Msty + AI Chat Bot = تجربة ذكاء اصطناعي متكاملة وآمنة** 🚀
