---
name: Feature
about: A pretty vague description of how a capability of our software can be added or improved.
title: ''
labels:
  - feature
assignees: ''
---

# Motivation

<!-- What capability should be either established or improved? How is life of the target audience better after it's been done? -->

# Scope

<!-- This is kind-of the definition-of-done for a feature.
Try to keep the scope as small as possible and prefer creating multiple, small features which each solve a single problem / make something better
-->

# Options

<!-- If you already have an idea how this can be implemented, please describe it here.
This allows potential other contributors to join forces and provide meaningful feedback prio to even starting work on it.
-->

# Related

<!-- Link to the epic or other issues or PRs which are related to this feature. -->
