<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات AI Chat Bot</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .icon-preview {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(5px);
        }
        
        .icon-display {
            width: 128px;
            height: 128px;
            margin: 0 auto 15px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .icon-display canvas {
            width: 100%;
            height: 100%;
        }
        
        .icon-size {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        .download-all {
            text-align: center;
            margin-top: 30px;
        }
        
        .download-all button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .download-all button:hover {
            transform: translateY(-2px);
        }
        
        .instructions {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            backdrop-filter: blur(5px);
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            border-right: 4px solid #ffa726;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎨 إنشاء أيقونات AI Chat Bot</h1>
        
        <div class="preview-grid" id="previewGrid">
            <!-- سيتم إنشاء المعاينات هنا -->
        </div>
        
        <div class="download-all">
            <button onclick="downloadAllIcons()">
                📦 تحميل جميع الأيقونات
            </button>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <div class="step">
                <strong>1.</strong> اضغط "تحميل جميع الأيقونات" لحفظ كل الأحجام
            </div>
            <div class="step">
                <strong>2.</strong> ضع الملفات في مجلد assets بالمشروع
            </div>
            <div class="step">
                <strong>3.</strong> استخدم icon.ico للـ Windows installer
            </div>
            <div class="step">
                <strong>4.</strong> استخدم icon.png للأيقونة العامة
            </div>
        </div>
    </div>

    <script>
        const iconSizes = [
            { size: 16, name: 'icon-16.png', label: '16x16 - صغير' },
            { size: 32, name: 'icon-32.png', label: '32x32 - متوسط' },
            { size: 48, name: 'icon-48.png', label: '48x48 - عادي' },
            { size: 64, name: 'icon-64.png', label: '64x64 - كبير' },
            { size: 128, name: 'icon-128.png', label: '128x128 - كبير جداً' },
            { size: 256, name: 'icon.png', label: '256x256 - أساسي' },
            { size: 512, name: 'icon-512.png', label: '512x512 - عالي الدقة' }
        ];

        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // تدرج الخلفية العصري
            const bgGradient = ctx.createLinearGradient(0, 0, size, size);
            bgGradient.addColorStop(0, '#667eea');
            bgGradient.addColorStop(0.5, '#764ba2');
            bgGradient.addColorStop(1, '#f093fb');
            
            // رسم الخلفية المستديرة
            const cornerRadius = size * 0.23;
            ctx.fillStyle = bgGradient;
            roundRect(ctx, size * 0.03, size * 0.03, size * 0.94, size * 0.94, cornerRadius);
            ctx.fill();
            
            // رسم الروبوت
            const robotGradient = ctx.createLinearGradient(0, 0, size, size);
            robotGradient.addColorStop(0, '#ffffff');
            robotGradient.addColorStop(1, '#f8f9fa');
            
            // رأس الروبوت
            ctx.fillStyle = robotGradient;
            roundRect(ctx, size * 0.27, size * 0.20, size * 0.45, size * 0.35, size * 0.18);
            ctx.fill();
            
            // العيون
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(size * 0.37, size * 0.31, size * 0.047, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(size * 0.63, size * 0.31, size * 0.047, 0, 2 * Math.PI);
            ctx.fill();
            
            // بريق العيون
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.beginPath();
            ctx.arc(size * 0.37, size * 0.31, size * 0.023, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(size * 0.63, size * 0.31, size * 0.023, 0, 2 * Math.PI);
            ctx.fill();
            
            // الفم
            ctx.fillStyle = '#667eea';
            roundRect(ctx, size * 0.43, size * 0.41, size * 0.14, size * 0.04, size * 0.02);
            ctx.fill();
            
            // الجسم
            ctx.fillStyle = robotGradient;
            roundRect(ctx, size * 0.31, size * 0.55, size * 0.38, size * 0.27, size * 0.08);
            ctx.fill();
            
            // شاشة الصدر
            ctx.fillStyle = 'rgba(102, 126, 234, 0.8)';
            roundRect(ctx, size * 0.43, size * 0.61, size * 0.14, size * 0.09, size * 0.03);
            ctx.fill();
            
            // خطوط الشاشة
            ctx.fillStyle = 'rgba(255,255,255,0.9)';
            roundRect(ctx, size * 0.45, size * 0.63, size * 0.10, size * 0.015, size * 0.008);
            ctx.fill();
            roundRect(ctx, size * 0.45, size * 0.65, size * 0.08, size * 0.015, size * 0.008);
            ctx.fill();
            
            // شارة AI
            const aiGradient = ctx.createLinearGradient(0, 0, size, size);
            aiGradient.addColorStop(0, '#ff6b6b');
            aiGradient.addColorStop(1, '#ffa726');
            
            ctx.fillStyle = aiGradient;
            ctx.beginPath();
            ctx.arc(size * 0.78, size * 0.22, size * 0.125, 0, 2 * Math.PI);
            ctx.fill();
            
            // نص AI
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${size * 0.08}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('AI', size * 0.78, size * 0.22);
            
            return canvas;
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadAllIcons() {
            iconSizes.forEach(iconInfo => {
                const canvas = createIcon(iconInfo.size);
                setTimeout(() => {
                    downloadIcon(canvas, iconInfo.name);
                }, iconSizes.indexOf(iconInfo) * 200);
            });
        }
        
        // إنشاء المعاينات
        function createPreviews() {
            const grid = document.getElementById('previewGrid');
            
            iconSizes.forEach(iconInfo => {
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                const canvas = createIcon(iconInfo.size);
                const display = document.createElement('div');
                display.className = 'icon-display';
                display.appendChild(canvas);
                
                const label = document.createElement('div');
                label.className = 'icon-size';
                label.textContent = iconInfo.label;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = '📥 تحميل';
                downloadBtn.onclick = () => downloadIcon(canvas, iconInfo.name);
                
                preview.appendChild(display);
                preview.appendChild(label);
                preview.appendChild(downloadBtn);
                grid.appendChild(preview);
            });
        }
        
        // إنشاء المعاينات عند تحميل الصفحة
        window.onload = createPreviews;
    </script>
</body>
</html>
