.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.loading-content {
  text-align: center;
  color: white;
  z-index: 2;
  max-width: 500px;
  padding: var(--spacing-xl);
  animation: fadeInUp 0.8s ease-out;
}

/* شعار التطبيق */
.app-logo-large {
  margin-bottom: var(--spacing-xl);
}

.logo-icon {
  color: white;
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s infinite;
}

.app-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-sm) 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  animation: slideInDown 0.8s ease-out 0.2s both;
}

.app-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
  animation: slideInDown 0.8s ease-out 0.4s both;
}

/* مؤشر التحميل */
.loading-indicator {
  margin: var(--spacing-xl) 0;
  animation: slideInUp 0.8s ease-out 0.6s both;
}

.spinner {
  margin-bottom: var(--spacing-md);
}

.spinner-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: var(--font-size-lg);
  margin: 0;
  opacity: 0.9;
  font-weight: 500;
}

/* شريط التقدم */
.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin: var(--spacing-lg) 0;
  animation: slideInUp 0.8s ease-out 0.8s both;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  border-radius: 2px;
  animation: progressFill 3s ease-in-out infinite;
}

@keyframes progressFill {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 100%;
    transform: translateX(0%);
  }
  100% {
    width: 100%;
    transform: translateX(100%);
  }
}

/* خطوات التحميل */
.loading-steps {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
  animation: slideInUp 0.8s ease-out 1s both;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.step.active {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.step.active .step-number {
  background-color: white;
  color: var(--primary-color);
}

.step-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
  white-space: nowrap;
}

/* معلومات الميزات */
.loading-info {
  margin: var(--spacing-xl) 0;
  animation: slideInUp 0.8s ease-out 1.2s both;
}

.feature-highlight {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-base);
  opacity: 0.9;
}

.feature-highlight:last-child {
  margin-bottom: 0;
}

.feature-icon {
  font-size: 1.2rem;
}

.feature-text {
  font-weight: 500;
}

/* تذييل شاشة التحميل */
.loading-footer {
  margin-top: var(--spacing-xl);
  opacity: 0.7;
  animation: slideInUp 0.8s ease-out 1.4s both;
}

.version-info {
  font-size: var(--font-size-sm);
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 500;
}

.copyright {
  font-size: var(--font-size-sm);
  margin: 0;
  font-weight: 300;
}

/* خلفية متحركة */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 3s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  bottom: 10%;
  right: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.3;
  }
}

/* أنيميشن الدخول */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .loading-content {
    padding: var(--spacing-lg);
    max-width: 90%;
  }
  
  .app-title {
    font-size: 2rem;
  }
  
  .app-subtitle {
    font-size: var(--font-size-base);
  }
  
  .loading-steps {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .step {
    flex-direction: row;
    justify-content: center;
  }
  
  .feature-highlight {
    font-size: var(--font-size-sm);
  }
  
  .floating-shape {
    display: none;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1.5rem;
  }
  
  .loading-content {
    padding: var(--spacing-md);
  }
  
  .loading-steps {
    gap: var(--spacing-sm);
  }
  
  .step-text {
    font-size: 0.8rem;
  }
  
  .feature-highlight {
    font-size: 0.8rem;
  }
}

/* تحسين إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
  .loading-content,
  .logo-icon,
  .spinner-icon,
  .progress-fill,
  .floating-shape {
    animation: none;
  }
  
  .step {
    opacity: 1;
  }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .loading-screen {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
}

/* تأثيرات إضافية */
.loading-content {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* تحسين النصوص */
.app-title,
.app-subtitle,
.loading-text,
.step-text,
.feature-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
