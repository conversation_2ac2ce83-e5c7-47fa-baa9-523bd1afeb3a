@echo off
title AI Chat Bot - مع Msty
color 0A

echo ========================================
echo           AI Chat Bot
echo        Desktop Application
echo         مع دعم Msty المتقدم
echo ========================================
echo.

echo 🚀 بدء تشغيل AI Chat Bot مع Msty...
echo.

:: فحص وتشغيل Msty
echo 🔍 البحث عن Msty...
set "MSTY_FOUND=0"

:: البحث في المسارات المحتملة لـ Msty
if exist "%LOCALAPPDATA%\Programs\Msty\Msty.exe" (
    set "MSTY_PATH=%LOCALAPPDATA%\Programs\Msty\Msty.exe"
    set "MSTY_FOUND=1"
    echo ✅ تم العثور على Msty في: %LOCALAPPDATA%\Programs\Msty\
) else if exist "%APPDATA%\Msty\Msty.exe" (
    set "MSTY_PATH=%APPDATA%\Msty\Msty.exe"
    set "MSTY_FOUND=1"
    echo ✅ تم العثور على Msty في: %APPDATA%\Msty\
) else if exist "C:\Program Files\Msty\Msty.exe" (
    set "MSTY_PATH=C:\Program Files\Msty\Msty.exe"
    set "MSTY_FOUND=1"
    echo ✅ تم العثور على Msty في: C:\Program Files\Msty\
) else (
    echo ⚠️  Msty غير مثبت أو غير موجود في المسارات المعتادة
    echo.
    echo 💡 يمكنك تحميل Msty من: https://msty.app/
    echo 📥 Msty هو تطبيق ممتاز للذكاء الاصطناعي المحلي
    echo.
    echo ⏳ سيتم تشغيل التطبيق بدون Msty...
    timeout /t 3 /nobreak >nul
    goto :run_app
)

if "%MSTY_FOUND%"=="1" (
    echo 🚀 تشغيل Msty...
    start "" "%MSTY_PATH%"
    echo ✅ تم تشغيل Msty بنجاح
    echo ⏳ انتظار تحميل Msty...
    timeout /t 5 /nobreak >nul
    
    :: فحص ما إذا كان Msty يعمل على المنفذ الافتراضي
    echo 🔍 فحص اتصال Msty...
    curl -s http://localhost:10000/v1/models >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ Msty متصل ويعمل على المنفذ 10000
    ) else (
        echo ⚠️  Msty يعمل ولكن قد يحتاج وقت إضافي للتحميل
        echo 💡 سيحاول التطبيق الاتصال تلقائياً
    )
)

:run_app
echo.
echo 🔧 تشغيل AI Chat Bot...
echo.

:: تشغيل التطبيق
if exist "node_modules" (
    echo Method 1: Using npm start...
    npm start
    if not errorlevel 1 goto success
)

echo Method 2: Using electron directly...
npx electron .
if not errorlevel 1 goto success

echo Method 3: Using npm run dev...
npm run dev
if not errorlevel 1 goto success

echo Method 4: Building and running...
npm run build
if not errorlevel 1 (
    npm run electron
    if not errorlevel 1 goto success
)

echo.
echo ❌ فشل في تشغيل التطبيق
echo 💡 تأكد من:
echo    - تثبيت Node.js
echo    - تثبيت المتطلبات: npm install
echo    - صحة ملفات المشروع
echo.
pause
exit /b 1

:success
echo.
echo ✅ تم تشغيل AI Chat Bot بنجاح!
echo 🎉 استمتع بالدردشة مع الذكاء الاصطناعي
echo.

if "%MSTY_FOUND%"=="1" (
    echo 💡 نصائح لاستخدام Msty:
    echo    - تأكد من تحميل نموذج في Msty
    echo    - استخدم النماذج المحلية للخصوصية
    echo    - Msty يدعم العديد من النماذج المتقدمة
    echo.
)

echo اضغط أي مفتاح للخروج...
pause >nul
exit /b 0
