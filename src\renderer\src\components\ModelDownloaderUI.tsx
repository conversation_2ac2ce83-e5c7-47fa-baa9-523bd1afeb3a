import React, { useState, useEffect } from 'react';
import { ModelInfo } from '../../../services/ModelDownloader';
import './ModelDownloaderUI.css';
import { Download, Trash2, Check, AlertCircle, Loader } from 'lucide-react';

interface ModelDownloaderUIProps {
  onClose: () => void;
}

interface DownloadStatus {
  [modelId: string]: {
    isDownloading: boolean;
    progress: number;
    error?: string;
  };
}

const ModelDownloaderUI: React.FC<ModelDownloaderUIProps> = ({ onClose }) => {
  const [availableModels, setAvailableModels] = useState<ModelInfo[]>([]);
  const [downloadStatus, setDownloadStatus] = useState<DownloadStatus>({});
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    // جلب قائمة النماذج المتاحة
    const fetchAvailableModels = async () => {
      try {
        const models = await window.electronAPI.getAvailableModelsForDownload();
        if (models.success) {
          setAvailableModels(models.models);
        } else {
          console.error('فشل في جلب النماذج المتاحة:', models.error);
        }
      } catch (error) {
        console.error('خطأ في جلب النماذج المتاحة:', error);
      }
    };

    fetchAvailableModels();
  }, []);

  const handleDownloadModel = async (model: ModelInfo) => {
    // تحديث حالة التنزيل
    setDownloadStatus(prev => ({
      ...prev,
      [model.id]: {
        isDownloading: true,
        progress: 0
      }
    }));

    try {
      // بدء تنزيل النموذج
      const result = await window.electronAPI.downloadModel(model.id);
      
      if (result.success) {
        // تحديث حالة النموذج بعد التنزيل الناجح
        setAvailableModels(prev => 
          prev.map(m => 
            m.id === model.id ? { ...m, isDownloaded: true } : m
          )
        );
        
        // تحديث حالة التنزيل
        setDownloadStatus(prev => ({
          ...prev,
          [model.id]: {
            isDownloading: false,
            progress: 100
          }
        }));
      } else {
        // تحديث حالة التنزيل في حالة الفشل
        setDownloadStatus(prev => ({
          ...prev,
          [model.id]: {
            isDownloading: false,
            progress: 0,
            error: result.message
          }
        }));
      }
    } catch (error) {
      console.error(`خطأ في تنزيل النموذج ${model.id}:`, error);
      
      // تحديث حالة التنزيل في حالة الخطأ
      setDownloadStatus(prev => ({
        ...prev,
        [model.id]: {
          isDownloading: false,
          progress: 0,
          error: 'حدث خطأ أثناء التنزيل'
        }
      }));
    }
  };

  const handleDeleteModel = async (model: ModelInfo) => {
    try {
      const result = await window.electronAPI.deleteModel(model.id);
      
      if (result.success) {
        // تحديث حالة النموذج بعد الحذف الناجح
        setAvailableModels(prev => 
          prev.map(m => 
            m.id === model.id ? { ...m, isDownloaded: false } : m
          )
        );
      } else {
        console.error(`فشل في حذف النموذج ${model.id}:`, result.message);
      }
    } catch (error) {
      console.error(`خطأ في حذف النموذج ${model.id}:`, error);
    }
  };

  // تصفية النماذج حسب الفئة والبحث
  const filteredModels = availableModels.filter(model => {
    const matchesCategory = activeCategory === 'all' || model.category === activeCategory;
    const matchesSearch = model.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         model.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // تصنيف النماذج: المنزلة أولاً، ثم حسب الاسم
  const sortedModels = [...filteredModels].sort((a, b) => {
    if (a.isDownloaded && !b.isDownloaded) return -1;
    if (!a.isDownloaded && b.isDownloaded) return 1;
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="model-downloader-overlay">
      <div className="model-downloader-container">
        <div className="model-downloader-header">
          <h2>تنزيل نماذج الذكاء الاصطناعي</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="model-downloader-search">
          <input
            type="text"
            placeholder="البحث عن نموذج..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="model-downloader-categories">
          <button 
            className={`category-button ${activeCategory === 'all' ? 'active' : ''}`}
            onClick={() => setActiveCategory('all')}
          >
            الكل
          </button>
          <button 
            className={`category-button ${activeCategory === 'general' ? 'active' : ''}`}
            onClick={() => setActiveCategory('general')}
          >
            عام
          </button>
          <button 
            className={`category-button ${activeCategory === 'novels' ? 'active' : ''}`}
            onClick={() => setActiveCategory('novels')}
          >
            روايات
          </button>
          <button 
            className={`category-button ${activeCategory === 'poetry' ? 'active' : ''}`}
            onClick={() => setActiveCategory('poetry')}
          >
            شعر
          </button>
          <button 
            className={`category-button ${activeCategory === 'programming' ? 'active' : ''}`}
            onClick={() => setActiveCategory('programming')}
          >
            برمجة
          </button>
        </div>
        
        <div className="models-list">
          {sortedModels.length === 0 ? (
            <div className="no-models">
              لا توجد نماذج متاحة تطابق معايير البحث
            </div>
          ) : (
            sortedModels.map(model => (
              <div key={model.id} className="model-card">
                <div className="model-info">
                  <h3>{model.name}</h3>
                  <p className="model-description">{model.description}</p>
                  <div className="model-meta">
                    <span className="model-size">{model.size}</span>
                    <span className="model-category">{getCategoryName(model.category)}</span>
                  </div>
                </div>
                
                <div className="model-actions">
                  {model.isDownloaded ? (
                    <button 
                      className="delete-button"
                      onClick={() => handleDeleteModel(model)}
                      title="حذف النموذج"
                    >
                      <Trash2 size={18} />
                    </button>
                  ) : downloadStatus[model.id]?.isDownloading ? (
                    <div className="download-progress">
                      <Loader size={18} className="spinner" />
                      <span>{downloadStatus[model.id]?.progress || 0}%</span>
                    </div>
                  ) : (
                    <button 
                      className="download-button"
                      onClick={() => handleDownloadModel(model)}
                      title="تنزيل النموذج"
                    >
                      <Download size={18} />
                    </button>
                  )}
                  
                  {model.isDownloaded && (
                    <span className="downloaded-badge" title="تم التنزيل">
                      <Check size={16} />
                    </span>
                  )}
                  
                  {downloadStatus[model.id]?.error && (
                    <span className="error-badge" title={downloadStatus[model.id].error}>
                      <AlertCircle size={16} />
                    </span>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
        
        <div className="model-downloader-footer">
          <p>
            <strong>ملاحظة:</strong> تتطلب النماذج المحلية تثبيت Ollama. 
            <a href="https://ollama.ai" target="_blank" rel="noopener noreferrer">تثبيت Ollama</a>
          </p>
        </div>
      </div>
    </div>
  );
};

// دالة مساعدة للحصول على اسم الفئة بالعربية
function getCategoryName(category: string): string {
  switch (category) {
    case 'general': return 'عام';
    case 'novels': return 'روايات';
    case 'poetry': return 'شعر';
    case 'cinema': return 'سينما';
    case 'programming': return 'برمجة';
    default: return category;
  }
}

export default ModelDownloaderUI;