.model-selector {
  position: relative;
  display: inline-block;
}

.model-selector-trigger {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  font-size: var(--font-size-sm);
}

.model-selector-trigger:hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.selected-model {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.model-icon {
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.model-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.model-name {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.model-meta {
  font-size: 0.75rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.chevron {
  transition: transform 0.2s ease;
  color: var(--text-secondary);
}

.chevron-open {
  transform: rotate(180deg);
}

/* القائمة المنسدلة */
.model-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  z-index: 1000;
  margin-top: 4px;
  max-height: 400px;
  overflow: hidden;
  animation: dropdownSlide 0.2s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

/* حالة الخدمات */
.service-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color-light);
  font-size: 0.85rem;
}

.service-name {
  font-weight: 500;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.status.available {
  background-color: rgba(40, 167, 69, 0.2);
  color: #155724;
}

.status.unavailable {
  background-color: rgba(220, 53, 69, 0.2);
  color: #721c24;
}

.models-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-models {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

.model-option {
  width: 100%;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-color);
}

.model-option:last-child {
  border-bottom: none;
}

.model-option:hover {
  background-color: var(--bg-secondary);
}

.model-option.selected {
  background-color: rgba(0, 123, 255, 0.1);
  border-color: var(--primary-color);
}

.model-option.selected .model-name {
  color: var(--primary-color);
  font-weight: 600;
}

.model-option-content {
  padding: var(--spacing-md);
  text-align: right;
}

.model-option-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.model-option-header .model-name {
  flex: 1;
  font-weight: 500;
  color: var(--text-primary);
  text-align: right;
}

.free-badge {
  display: flex;
  align-items: center;
  gap: 2px;
  background: linear-gradient(135deg, var(--success-color), #1e7e34);
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.model-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.4;
}

.model-specs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.spec {
  display: flex;
  align-items: center;
  gap: 4px;
}

.moderation-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
}

.moderation-status.moderated {
  background-color: rgba(255, 193, 7, 0.2);
  color: #856404;
}

.moderation-status.unmoderated {
  background-color: rgba(40, 167, 69, 0.2);
  color: #155724;
}

.dropdown-footer {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.dropdown-footer small {
  color: var(--text-secondary);
  font-size: 0.7rem;
}

/* طبقة الخلفية */
.model-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
}

/* تصنيف النماذج */
.model-category {
  margin-top: 8px;
  padding: 8px;
  border-top: 1px solid var(--border-color-light);
}

.category-title {
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.category-icon {
  font-size: 1rem;
}

.error-badge {
  color: var(--error-color);
  font-size: 0.9rem;
}
