# 🔧 دليل حل المشاكل - للطلاب المبتدئين

## 🚨 المشاكل الشائعة وحلولها

### ❌ **مشكلة: "node is not recognized"**

**السبب:** Node.js غير مثبت أو غير مُضاف لمتغير PATH

**الحل خطوة بخطوة:**
1. اذهب إلى [nodejs.org](https://nodejs.org/)
2. حمل النسخة LTS (الموصى بها)
3. شغل الملف المحمل
4. اختر "Next" في كل خطوة
5. **مهم:** تأكد من تحديد "Add to PATH" 
6. أعد تشغيل الكمبيوتر
7. جرب تشغيل التطبيق مرة أخرى

---

### ❌ **مشكلة: "npm install failed"**

**السبب:** مشكلة في الشبكة أو الصلاحيات

**الحل:**
```
الطريقة 1: إعادة المحاولة
1. أغلق النافذة
2. شغل "تشغيل-التطبيق.bat" مرة أخرى

الطريقة 2: تنظيف الذاكرة المؤقتة
1. اضغط Win + R
2. اكتب: cmd
3. اكتب: npm cache clean --force
4. جرب التشغيل مرة أخرى

الطريقة 3: تشغيل كمدير
1. انقر بالزر الأيمن على "تشغيل-التطبيق.bat"
2. اختر "تشغيل كمدير"
```

---

### ❌ **مشكلة: التطبيق لا يفتح**

**السبب:** التثبيت لم يكتمل أو مشكلة في المنفذ

**الحل:**
```
1. انتظر 2-3 دقائق إضافية
2. افتح المتصفح يدوياً
3. اذهب إلى: http://localhost:3000
4. إذا لم يعمل، جرب: http://localhost:5173
```

---

### ❌ **مشكلة: "Port already in use"**

**السبب:** المنفذ مُستخدم من برنامج آخر

**الحل:**
```
1. اضغط Ctrl + C في النافذة السوداء
2. اكتب: Y
3. شغل التطبيق مرة أخرى
```

---

### ❌ **مشكلة: رسالة خطأ API**

**السبب:** مشكلة في مفتاح API أو الاتصال

**الحل:**
```
1. تحقق من الاتصال بالإنترنت
2. في التطبيق، اذهب للإعدادات
3. تبويب "API"
4. أدخل مفتاح جديد من openrouter.ai
5. أو استخدم المفتاح المُدمج
```

---

### ❌ **مشكلة: النصوص العربية لا تظهر صحيحة**

**السبب:** مشكلة في ترميز الخطوط

**الحل:**
```
1. في المتصفح، اضغط F12
2. اذهب لتبويب Console
3. أعد تحميل الصفحة (F5)
4. أو جرب متصفح آخر (Chrome, Firefox)
```

---

## 🆘 **إذا لم تنجح الحلول السابقة**

### **الحل الشامل: إعادة التثبيت**

```
1. احذف مجلد "node_modules" (إن وُجد)
2. احذف ملف "package-lock.json" (إن وُجد)
3. أعد تشغيل الكمبيوتر
4. شغل "تشغيل-التطبيق.bat" مرة أخرى
```

### **التحقق من متطلبات النظام**

```
✅ Windows 10/11 (64-bit)
✅ 4 GB RAM كحد أدنى
✅ 2 GB مساحة فارغة
✅ اتصال بالإنترنت
✅ Node.js v18 أو أحدث
```

---

## 📞 **طلب المساعدة**

إذا استمرت المشاكل، اجمع هذه المعلومات:

```
1. إصدار Windows: (اضغط Win + R، اكتب winver)
2. إصدار Node.js: (في cmd اكتب: node --version)
3. رسالة الخطأ الكاملة
4. لقطة شاشة للخطأ
```

---

## 💡 **نصائح لتجنب المشاكل**

```
✅ لا تغلق النافذة السوداء أثناء التثبيت
✅ تأكد من الاتصال بالإنترنت
✅ لا تشغل أكثر من نسخة من التطبيق
✅ أغلق برامج مكافحة الفيروسات مؤقتاً
✅ شغل التطبيق من مجلد ثابت (ليس سطح المكتب)
```

---

## 🎯 **اختبار سريع للتأكد من التثبيت**

```
1. اضغط Win + R
2. اكتب: cmd
3. اكتب: node --version
4. يجب أن يظهر: v18.x.x أو أحدث
5. اكتب: npm --version  
6. يجب أن يظهر: 9.x.x أو أحدث
```

إذا ظهرت الأرقام، فالتثبيت صحيح! 🎉

---

**تذكر:** الصبر مفتاح النجاح! التثبيت الأول قد يستغرق 10-15 دقيقة. 😊
