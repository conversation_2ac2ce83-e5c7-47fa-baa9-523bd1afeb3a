// ملف تكامل الخدمات الخارجية
import axios from 'axios';

class ExternalAPIService {
  constructor() {
    this.openRouterBaseURL = 'https://openrouter.ai/api';
    this.ollamaBaseURL = 'http://localhost:11434';
    this.openRouterAPIKey = process.env.OPENROUTER_API_KEY;
  }

  async checkOpenRouterConnection() {
    try {
      const response = await axios.get(`${this.openRouterBaseURL}/auth/key`, {
        headers: {
          'Authorization': `Bearer ${this.openRouterAPIKey}`
        }
      });
      return response.status === 200;
    } catch (error) {
      console.error('OpenRouter connection error:', error);
      return false;
    }
  }

  async checkOllamaConnection() {
    try {
      const response = await axios.get(`${this.ollamaBaseURL}/api/tags`);
      return response.status === 200;
    } catch (error) {
      console.error('Ollama connection error:', error);
      return false;
    }
  }

  async sendToOpenRouter(model, prompt) {
    if (!await this.checkOpenRouterConnection()) {
      throw new Error('OpenRouter service is not available');
    }

    try {
      const response = await axios.post(
        `${this.openRouterBaseURL}/v1/chat/completions`,
        {
          model,
          messages: [{ role: 'user', content: prompt }]
        },
        {
          headers: {
            'Authorization': `Bearer ${this.openRouterAPIKey}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('OpenRouter API error:', error);
      throw error;
    }
  }

  async sendToOllama(model, prompt) {
    if (!await this.checkOllamaConnection()) {
      throw new Error('Ollama service is not available');
    }

    try {
      const response = await axios.post(
        `${this.ollamaBaseURL}/api/generate`,
        {
          model,
          prompt,
          stream: false
        }
      );
      return response.data;
    } catch (error) {
      console.error('Ollama API error:', error);
      throw error;
    }
  }
}

export default new ExternalAPIService();