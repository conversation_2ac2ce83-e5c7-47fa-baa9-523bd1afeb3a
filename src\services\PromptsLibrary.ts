/**
 * مكتبة Prompts متقدمة مستوحاة من Msty
 * توفر مجموعة شاملة من القوالب والتعليمات للذكاء الاصطناعي
 */

export interface PromptTemplate {
  id: string
  name: string
  description: string
  category: PromptCategory
  prompt: string
  variables?: string[]
  tags: string[]
  difficulty: 'مبتدئ' | 'متوسط' | 'متقدم'
  language: 'ar' | 'en' | 'both'
  author?: string
  rating?: number
  usageCount?: number
}

export type PromptCategory = 
  | 'writing' 
  | 'coding' 
  | 'analysis' 
  | 'creative' 
  | 'business' 
  | 'education' 
  | 'research'
  | 'translation'
  | 'summarization'
  | 'conversation'

export class PromptsLibrary {
  private prompts: PromptTemplate[] = []
  private favorites: string[] = []
  private recentlyUsed: string[] = []

  constructor() {
    this.initializeDefaultPrompts()
    this.loadUserData()
  }

  // تهيئة المكتبة بالقوالب الافتراضية
  private initializeDefaultPrompts(): void {
    this.prompts = [
      // قوالب الكتابة الإبداعية
      {
        id: 'creative-story',
        name: 'كاتب القصص الإبداعية',
        description: 'مساعد متخصص في كتابة القصص والروايات',
        category: 'creative',
        prompt: `أنت كاتب قصص محترف ومبدع. مهمتك هي مساعدة المستخدم في كتابة قصص مشوقة وإبداعية.

عند كتابة القصة:
- ابدأ بمقدمة جذابة تشد انتباه القارئ
- طور الشخصيات بعمق وأعطها أبعاداً إنسانية
- اخلق صراعاً مثيراً وحبكة متماسكة
- استخدم الوصف الحسي والصور البلاغية
- اختتم بنهاية مؤثرة ومرضية

اكتب باللغة العربية الفصحى مع الحفاظ على الطلاقة والجمال اللغوي.`,
        variables: ['نوع_القصة', 'الشخصية_الرئيسية', 'المكان', 'الزمان'],
        tags: ['إبداع', 'قصص', 'أدب', 'كتابة'],
        difficulty: 'متوسط',
        language: 'ar',
        author: 'AI Chat Bot',
        rating: 4.8,
        usageCount: 0
      },
      
      // قوالب البرمجة
      {
        id: 'code-reviewer',
        name: 'مراجع الكود المحترف',
        description: 'خبير في مراجعة وتحسين الكود البرمجي',
        category: 'coding',
        prompt: `أنت مراجع كود محترف وخبير في البرمجة. مهمتك هي مراجعة الكود وتقديم تحسينات.

عند مراجعة الكود:
- تحقق من الأمان والأداء
- اقترح تحسينات في البنية والتصميم
- تأكد من اتباع أفضل الممارسات
- اشرح المشاكل بوضوح مع الحلول
- قدم أمثلة محسنة للكود

قدم ملاحظاتك بشكل بناء ومفيد مع أمثلة عملية.`,
        variables: ['لغة_البرمجة', 'نوع_المشروع'],
        tags: ['برمجة', 'مراجعة', 'تحسين', 'أمان'],
        difficulty: 'متقدم',
        language: 'both',
        author: 'AI Chat Bot',
        rating: 4.9,
        usageCount: 0
      },

      // قوالب التحليل
      {
        id: 'data-analyst',
        name: 'محلل البيانات الخبير',
        description: 'متخصص في تحليل البيانات واستخراج الرؤى',
        category: 'analysis',
        prompt: `أنت محلل بيانات خبير ومتخصص في استخراج الرؤى من البيانات.

عند تحليل البيانات:
- ابدأ بفهم السياق والهدف من التحليل
- استخدم الأساليب الإحصائية المناسبة
- اكتشف الأنماط والاتجاهات المهمة
- قدم رؤى قابلة للتنفيذ
- اشرح النتائج بطريقة واضحة ومفهومة

قدم تحليلاً شاملاً مع توصيات عملية.`,
        variables: ['نوع_البيانات', 'الهدف_من_التحليل'],
        tags: ['تحليل', 'بيانات', 'إحصاء', 'رؤى'],
        difficulty: 'متقدم',
        language: 'ar',
        author: 'AI Chat Bot',
        rating: 4.7,
        usageCount: 0
      },

      // قوالب الأعمال
      {
        id: 'business-strategist',
        name: 'استراتيجي الأعمال',
        description: 'خبير في وضع الاستراتيجيات وحل المشاكل التجارية',
        category: 'business',
        prompt: `أنت استراتيجي أعمال محترف مع خبرة واسعة في حل المشاكل التجارية.

عند وضع الاستراتيجيات:
- حلل الوضع الحالي والتحديات
- حدد الفرص والمخاطر
- ضع خطة عمل واضحة ومرحلية
- اقترح مؤشرات أداء قابلة للقياس
- قدم بدائل واستراتيجيات متعددة

فكر بطريقة استراتيجية وقدم حلولاً عملية وقابلة للتنفيذ.`,
        variables: ['نوع_العمل', 'التحدي_الرئيسي', 'الهدف'],
        tags: ['أعمال', 'استراتيجية', 'تخطيط', 'حلول'],
        difficulty: 'متقدم',
        language: 'ar',
        author: 'AI Chat Bot',
        rating: 4.6,
        usageCount: 0
      },

      // قوالب التعليم
      {
        id: 'educational-tutor',
        name: 'المعلم الشخصي',
        description: 'مدرس متخصص في شرح المفاهيم المعقدة ببساطة',
        category: 'education',
        prompt: `أنت معلم محترف ومتخصص في التعليم التفاعلي والشرح المبسط.

عند التدريس:
- ابدأ بتقييم مستوى المتعلم
- اشرح المفاهيم بطريقة متدرجة وواضحة
- استخدم أمثلة عملية وقريبة من الواقع
- تأكد من فهم المتعلم قبل الانتقال للنقطة التالية
- قدم تمارين وأنشطة تفاعلية

كن صبوراً ومشجعاً واجعل التعلم ممتعاً وفعالاً.`,
        variables: ['الموضوع', 'مستوى_المتعلم', 'طريقة_التعلم_المفضلة'],
        tags: ['تعليم', 'شرح', 'تدريس', 'تعلم'],
        difficulty: 'متوسط',
        language: 'ar',
        author: 'AI Chat Bot',
        rating: 4.8,
        usageCount: 0
      },

      // قوالب الترجمة
      {
        id: 'professional-translator',
        name: 'المترجم المحترف',
        description: 'مترجم خبير يحافظ على المعنى والسياق الثقافي',
        category: 'translation',
        prompt: `أنت مترجم محترف ومتخصص في الترجمة الدقيقة والثقافية.

عند الترجمة:
- احتفظ بالمعنى الأصلي والسياق
- راعِ الفروق الثقافية واللغوية
- استخدم المصطلحات المناسبة للمجال
- حافظ على أسلوب النص الأصلي
- تأكد من الطلاقة والوضوح في اللغة المستهدفة

قدم ترجمة احترافية تنقل الرسالة بدقة وفعالية.`,
        variables: ['اللغة_المصدر', 'اللغة_المستهدفة', 'نوع_النص'],
        tags: ['ترجمة', 'لغات', 'ثقافة', 'تواصل'],
        difficulty: 'متقدم',
        language: 'both',
        author: 'AI Chat Bot',
        rating: 4.7,
        usageCount: 0
      }
    ]
  }

  // تحميل بيانات المستخدم (المفضلة والمستخدمة مؤخراً)
  private loadUserData(): void {
    try {
      const favoritesData = localStorage.getItem('prompts-favorites')
      const recentData = localStorage.getItem('prompts-recent')
      
      if (favoritesData) {
        this.favorites = JSON.parse(favoritesData)
      }
      
      if (recentData) {
        this.recentlyUsed = JSON.parse(recentData)
      }
    } catch (error) {
      console.error('خطأ في تحميل بيانات المستخدم:', error)
    }
  }

  // حفظ بيانات المستخدم
  private saveUserData(): void {
    try {
      localStorage.setItem('prompts-favorites', JSON.stringify(this.favorites))
      localStorage.setItem('prompts-recent', JSON.stringify(this.recentlyUsed))
    } catch (error) {
      console.error('خطأ في حفظ بيانات المستخدم:', error)
    }
  }

  // الحصول على جميع القوالب
  getAllPrompts(): PromptTemplate[] {
    return this.prompts
  }

  // البحث في القوالب
  searchPrompts(query: string): PromptTemplate[] {
    const searchTerm = query.toLowerCase()
    return this.prompts.filter(prompt => 
      prompt.name.toLowerCase().includes(searchTerm) ||
      prompt.description.toLowerCase().includes(searchTerm) ||
      prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  // الحصول على القوالب حسب الفئة
  getPromptsByCategory(category: PromptCategory): PromptTemplate[] {
    return this.prompts.filter(prompt => prompt.category === category)
  }

  // الحصول على القوالب المفضلة
  getFavoritePrompts(): PromptTemplate[] {
    return this.prompts.filter(prompt => this.favorites.includes(prompt.id))
  }

  // الحصول على القوالب المستخدمة مؤخراً
  getRecentPrompts(): PromptTemplate[] {
    return this.recentlyUsed
      .map(id => this.prompts.find(p => p.id === id))
      .filter(Boolean) as PromptTemplate[]
  }

  // إضافة قالب للمفضلة
  addToFavorites(promptId: string): void {
    if (!this.favorites.includes(promptId)) {
      this.favorites.push(promptId)
      this.saveUserData()
    }
  }

  // إزالة قالب من المفضلة
  removeFromFavorites(promptId: string): void {
    this.favorites = this.favorites.filter(id => id !== promptId)
    this.saveUserData()
  }

  // تسجيل استخدام قالب
  recordUsage(promptId: string): void {
    // إزالة من القائمة إذا كان موجوداً
    this.recentlyUsed = this.recentlyUsed.filter(id => id !== promptId)
    
    // إضافة في المقدمة
    this.recentlyUsed.unshift(promptId)
    
    // الاحتفاظ بآخر 10 فقط
    this.recentlyUsed = this.recentlyUsed.slice(0, 10)
    
    // تحديث عداد الاستخدام
    const prompt = this.prompts.find(p => p.id === promptId)
    if (prompt) {
      prompt.usageCount = (prompt.usageCount || 0) + 1
    }
    
    this.saveUserData()
  }

  // الحصول على قالب بالمعرف
  getPromptById(id: string): PromptTemplate | undefined {
    return this.prompts.find(prompt => prompt.id === id)
  }

  // معالجة المتغيرات في القالب
  processPromptVariables(prompt: PromptTemplate, variables: Record<string, string>): string {
    let processedPrompt = prompt.prompt
    
    if (prompt.variables) {
      prompt.variables.forEach(variable => {
        const value = variables[variable] || `[${variable}]`
        processedPrompt = processedPrompt.replace(
          new RegExp(`\\{${variable}\\}`, 'g'), 
          value
        )
      })
    }
    
    return processedPrompt
  }

  // إضافة قالب مخصص
  addCustomPrompt(prompt: Omit<PromptTemplate, 'id' | 'usageCount'>): string {
    const id = `custom-${Date.now()}`
    const newPrompt: PromptTemplate = {
      ...prompt,
      id,
      usageCount: 0,
      author: 'مستخدم'
    }
    
    this.prompts.push(newPrompt)
    return id
  }

  // حذف قالب مخصص
  deleteCustomPrompt(id: string): boolean {
    if (!id.startsWith('custom-')) {
      return false // لا يمكن حذف القوالب الافتراضية
    }
    
    this.prompts = this.prompts.filter(p => p.id !== id)
    this.removeFromFavorites(id)
    this.recentlyUsed = this.recentlyUsed.filter(rid => rid !== id)
    this.saveUserData()
    
    return true
  }

  // الحصول على إحصائيات الاستخدام
  getUsageStats(): {
    totalPrompts: number
    favoriteCount: number
    recentCount: number
    mostUsed: PromptTemplate[]
    categoryCounts: Record<PromptCategory, number>
  } {
    const categoryCounts = this.prompts.reduce((acc, prompt) => {
      acc[prompt.category] = (acc[prompt.category] || 0) + 1
      return acc
    }, {} as Record<PromptCategory, number>)

    const mostUsed = this.prompts
      .filter(p => p.usageCount && p.usageCount > 0)
      .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
      .slice(0, 5)

    return {
      totalPrompts: this.prompts.length,
      favoriteCount: this.favorites.length,
      recentCount: this.recentlyUsed.length,
      mostUsed,
      categoryCounts
    }
  }
}

// إنشاء مثيل مشترك
export const promptsLibrary = new PromptsLibrary()
