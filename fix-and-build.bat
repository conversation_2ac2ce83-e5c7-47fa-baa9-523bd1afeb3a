@echo off
chcp 65001 >nul
title إصلاح وبناء التطبيق - AI Chat Bot

echo ========================================
echo    إصلاح وبناء التطبيق - AI Chat Bot
echo ========================================
echo.

echo الخطوة 1: إغلاق أي عمليات سابقة للتطبيق...
taskkill /F /IM "AI Chat Bot - بوت الدردشة الذكي.exe" /T 2>nul
timeout /t 2 /nobreak >nul

echo الخطوة 2: إنشاء نسخة احتياطية من الملفات المهمة...
if not exist "backup" mkdir backup
copy main.js backup\main.js /Y >nul
copy index.html backup\index.html /Y >nul

echo الخطوة 3: تعديل ملف package.json...
echo تأكد من تضمين جميع الملفات المطلوبة...

echo الخطوة 4: بناء التطبيق...
call npm run build:renderer
if errorlevel 1 (
    echo تحذير: مشاكل في بناء Renderer، لكن سنحاول المتابعة...
)

echo الخطوة 5: إنشاء مجلد dist إذا لم يكن موجوداً...
if not exist "dist" mkdir dist
if not exist "dist\main" mkdir dist\main

echo الخطوة 6: نسخ main.js إلى مجلد dist\main...
copy main.js dist\main\main.js /Y >nul
echo تم نسخ main.js بنجاح!

echo الخطوة 7: إنشاء ملف التثبيت...
echo هذه العملية قد تستغرق بضع دقائق...
call npx electron-builder --win --x64 --config.extraMetadata.main="main.js" --config.files=["dist/**/*","node_modules/**/*","assets/**/*","main.js","index.html"] --config.asar=true

echo.
echo تم الانتهاء من العملية!
echo.

if exist "release\win-unpacked" (
    echo تم إنشاء نسخة محمولة من التطبيق في مجلد release\win-unpacked
    echo يمكنك تشغيل التطبيق مباشرة من هناك.
    explorer release\win-unpacked
)

echo.
pause
