@echo off
cls

echo AI Chat Bot - Starting...
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

REM Build if needed
if not exist "dist" (
    echo Building application...
    npm run build
)

echo Starting AI Chat Bot...
echo Opening browser at: http://localhost:5173
echo.

REM Start the app
start "AI Chat Bot" cmd /k "npm run dev"

REM Wait and open browser
timeout /t 5 /nobreak >nul
start http://localhost:5173

echo.
echo AI Chat Bot is running!
echo Close this window to stop the application.
pause
