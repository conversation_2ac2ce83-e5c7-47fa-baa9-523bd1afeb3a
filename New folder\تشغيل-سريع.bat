@echo off
chcp 65001 >nul
title تشغيل التطبيق - الإصدار المحسن

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 تشغيل التطبيق المحسن                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت! يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
echo.

echo 🔍 فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر
echo.

echo 📦 فحص التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install --silent --no-progress
    if errorlevel 1 (
        echo ❌ فشل في تثبيت التبعيات!
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ التبعيات موجودة
)

echo.
echo 🚀 تشغيل التطبيق...
echo.

npm run dev

echo.
echo 📝 انتهى التشغيل
pause
