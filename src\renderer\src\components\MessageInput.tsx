import React, { useState, useRef, useEffect } from 'react'
import { Send, Paperclip, Mic, Square } from 'lucide-react'
import './MessageInput.css'

interface MessageInputProps {
  onSendMessage: (message: string) => void
  isLoading: boolean
  placeholder?: string
  maxLength?: number
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  isLoading,
  placeholder = 'اكتب رسالتك هنا...',
  maxLength = 4000
}) => {
  const [message, setMessage] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)

  // تعديل ارتفاع النص تلقائياً
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [message])

  // التركيز على حقل الإدخال عند التحميل
  useEffect(() => {
    if (textareaRef.current && !isLoading) {
      textareaRef.current.focus()
    }
  }, [isLoading])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim() && !isLoading) {
      onSendMessage(message.trim())
      setMessage('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    if (value.length <= maxLength) {
      setMessage(value)
    }
  }

  // التسجيل الصوتي (مستقبلياً)
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      
      const audioChunks: Blob[] = []
      
      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data)
      }
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
        // هنا يمكن إضافة معالجة الصوت وتحويله لنص
        console.log('تم إنهاء التسجيل:', audioBlob)
        stream.getTracks().forEach(track => track.stop())
      }
      
      mediaRecorder.start()
      setIsRecording(true)
    } catch (error) {
      console.error('خطأ في بدء التسجيل:', error)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  const canSend = message.trim().length > 0 && !isLoading

  return (
    <form className="message-input-form" onSubmit={handleSubmit}>
      <div className="input-container">
        {/* زر المرفقات (مستقبلياً) */}
        <button
          type="button"
          className="input-action-btn"
          title="إرفاق ملف"
          disabled
        >
          <Paperclip size={18} />
        </button>

        {/* حقل إدخال النص */}
        <div className="textarea-container">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={isLoading}
            className="message-textarea"
            rows={1}
            dir="auto"
          />
          
          {/* عداد الأحرف */}
          <div className="character-counter">
            <span className={message.length > maxLength * 0.9 ? 'warning' : ''}>
              {message.length}/{maxLength}
            </span>
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="input-actions">
          {/* زر التسجيل الصوتي */}
          <button
            type="button"
            className={`input-action-btn voice-btn ${isRecording ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startRecording}
            title={isRecording ? 'إيقاف التسجيل' : 'تسجيل صوتي'}
            disabled={isLoading}
          >
            {isRecording ? <Square size={18} /> : <Mic size={18} />}
          </button>

          {/* زر الإرسال */}
          <button
            type="submit"
            className={`send-btn ${canSend ? 'active' : ''}`}
            disabled={!canSend}
            title="إرسال الرسالة (Enter)"
          >
            <Send size={18} />
          </button>
        </div>
      </div>

      {/* شريط التحميل */}
      {isLoading && (
        <div className="loading-bar">
          <div className="loading-progress"></div>
        </div>
      )}

      {/* نصائح الاستخدام */}
      <div className="input-hints">
        <span>اضغط Enter للإرسال، Shift+Enter لسطر جديد</span>
        {isRecording && (
          <span className="recording-indicator">
            🔴 جاري التسجيل...
          </span>
        )}
      </div>
    </form>
  )
}

export default MessageInput
