@echo off
chcp 65001 >nul
title بناء Setup احترافي - AI Chat Bot

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                📦 بناء Setup احترافي 📦                     ║
echo ║                   AI Chat Bot                                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 بناء ملف تثبيت احترافي للتطبيق...
echo.

echo 📋 الخطوة 1: التحقق من المتطلبات...

:: فحص Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت!
    echo 💡 يرجى تثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

:: فحص npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo 📦 الخطوة 2: تثبيت electron-builder...
npm install electron-builder --save-dev --no-optional --silent
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت electron-builder
    echo 🔄 محاولة تثبيت عالمي...
    npm install -g electron-builder --silent
)
echo ✅ تم تثبيت electron-builder

echo.
echo 🔨 الخطوة 3: بناء التطبيق...

:: بناء TypeScript
echo 🔄 بناء TypeScript...
npx tsc -p tsconfig.main.json
if errorlevel 1 (
    echo ⚠️ تحذير: مشاكل في TypeScript، لكن سنحاول المتابعة...
)

:: بناء Renderer
echo 🔄 بناء Renderer...
npm run build:renderer
if errorlevel 1 (
    echo ⚠️ تحذير: مشاكل في بناء Renderer، لكن سنحاول المتابعة...
)

echo ✅ تم بناء التطبيق

echo.
echo 📦 الخطوة 4: إنشاء ملف التثبيت...

:: إنشاء مجلد release إذا لم يكن موجود
if not exist "release" mkdir release

:: بناء التطبيق للتوزيع
echo 🔄 إنشاء ملف Setup...
npm run dist
if errorlevel 1 (
    echo ❌ فشل في إنشاء ملف Setup!
    echo 💡 جرب تشغيل: npm run build ثم npm run dist
    pause
    exit /b 1
)

echo ✅ تم إنشاء ملف Setup بنجاح!

echo.
echo 🎉 تم الانتهاء من بناء Setup!
echo.

:: عرض الملفات المُنشأة
echo 📁 الملفات المُنشأة في مجلد release:
if exist "release" (
    dir release /b
) else (
    echo ⚠️ مجلد release غير موجود
)

echo.
echo 💡 يمكنك الآن:
echo    1. العثور على ملف Setup في مجلد release
echo    2. تشغيل ملف Setup لتثبيت التطبيق
echo    3. مشاركة ملف Setup مع الآخرين
echo.

:: فتح مجلد release
if exist "release" (
    echo 📂 فتح مجلد release...
    explorer release
)

echo ✨ انتهى بناء Setup الاحترافي!
pause
