@echo off
title Create Simple Installer - AI Chat Bot

echo ========================================
echo    AI Chat Bot - Create Installer
echo ========================================
echo.

echo Step 1: Cleaning previous build...
if exist "dist" (
    echo Removing old dist folder...
    rmdir /s /q dist
)
if exist "release" (
    echo Removing old release folder...
    rmdir /s /q release
)

echo Step 2: Building application...
echo Building TypeScript...
call npx tsc -p tsconfig.main.json
if errorlevel 1 (
    echo Warning: TypeScript build had issues, but continuing...
)

echo Building Renderer...
call npm run build:renderer
if errorlevel 1 (
    echo Warning: Renderer build had issues, but continuing...
)

echo Step 3: Copying main.js and other files...
echo Ensuring all files are in place...

echo Step 4: Creating installer...
echo This may take a few minutes...
call npx electron-builder --win --x64 --publish never --config.files="[\"dist/**/*\",\"node_modules/**/*\",\"assets/**/*\",\"main.js\",\"index.html\",\"src/**/*\"]" --config.asar=true

echo Step 5: Checking for installer...
dir release\*.exe /s /b
if errorlevel 1 (
    echo No installer found, trying alternative method...
    call npx electron-builder --win nsis --x64 --publish never
)

echo.
echo Process completed! Check the release folder for the installer.
echo.

if exist "release" (
    explorer release
)

pause
