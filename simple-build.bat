@echo off
title Simple Build - AI Chat Bot

echo ========================================
echo    Simple Build - AI Chat Bot
echo ========================================
echo.

echo Step 1: Closing any running instances...
taskkill /F /IM "AI Chat Bot - *" /T 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Cleaning previous build...
if exist "simple-build" rmdir /s /q simple-build
mkdir simple-build

echo Step 3: Building the renderer...
call npm run build:renderer
if errorlevel 1 (
    echo Warning: Issues with renderer build, but continuing...
)

echo Step 4: Copying files to build directory...
xcopy /E /I /Y dist simple-build\dist
xcopy /E /I /Y assets simple-build\assets
copy main.js simple-build\
copy index.js simple-build\
copy index.html simple-build\
copy package.json simple-build\

echo Step 5: Installing dependencies in build directory...
cd simple-build
call npm install --production
cd ..

echo Step 6: Creating portable application...
call npx electron-builder --dir --win --x64 --config.asar=false --config.directories.output=simple-release --config.extraMetadata.main="index.js"

echo.
echo Process completed!
echo.

if exist "simple-release\win-unpacked" (
    echo Portable version created in simple-release\win-unpacked folder
    echo You can run the application directly from there.
    explorer simple-release\win-unpacked
)

echo.
pause
