# AI Chat Bot - PowerShell Launcher
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🤖 AI Chat Bot - تطبيق الدردشة الذكي" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔄 إيقاف العمليات السابقة..." -ForegroundColor Blue
try {
    Stop-Process -Name "electron" -Force -ErrorAction SilentlyContinue
    Stop-Process -Name "node" -Force -ErrorAction SilentlyContinue
} catch {
    # تجاهل الأخطاء
}

Write-Host ""
Write-Host "🚀 بدء تشغيل التطبيق..." -ForegroundColor Green
Write-Host ""
Write-Host "📋 معلومات مهمة:" -ForegroundColor Yellow
Write-Host "   • هذا تطبيق سطح مكتب (Electron)" -ForegroundColor White
Write-Host "   • ابحث عن نافذة 'AI Chat Bot'" -ForegroundColor White
Write-Host "   • النافذة ستظهر خلال ثوانٍ قليلة" -ForegroundColor White
Write-Host ""

Write-Host "🔧 تشغيل التطبيق..." -ForegroundColor Magenta

# تشغيل التطبيق
& npx electron .

Write-Host ""
Write-Host "✅ تم إغلاق التطبيق" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"
