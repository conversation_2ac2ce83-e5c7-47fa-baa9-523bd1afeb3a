@echo off
title AI Chat Bot - Create Installer

echo ========================================
echo    AI Chat Bot - Create Installer
echo ========================================
echo.

echo Step 1: Install electron-builder...
npm install electron-builder --save-dev --silent
if errorlevel 1 (
    echo Installing globally...
    npm install -g electron-builder --silent
)

echo Step 2: Build the app...
echo Building TypeScript...
npx tsc -p tsconfig.main.json

echo Step 3: Create installer...
echo This may take a few minutes...
npm run dist

if errorlevel 1 (
    echo Build failed! Trying alternative...
    npx electron-builder --win
)

echo.
echo Installer created in 'release' folder!
if exist "release" (
    echo Opening release folder...
    explorer release
)

echo.
echo Setup completed!
pause
