// نظام تشخيص ذكي للمشروع
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class SmartDiagnostic {
  constructor() {
    this.issues = [];
    this.solutions = [];
    this.status = {
      ollama: false,
      openrouter: false,
      app: false,
      config: false
    };
  }

  // فحص شامل للنظام
  async runFullDiagnostic() {
    console.log('🔍 بدء التشخيص الذكي الشامل...\n');
    
    await this.checkOllama();
    await this.checkOpenRouter();
    await this.checkAppConfig();
    await this.checkAppBuild();
    
    this.generateReport();
    this.provideSolutions();
  }

  // فحص Ollama
  async checkOllama() {
    console.log('🦙 فحص Ollama...');
    
    try {
      const response = await axios.get('http://localhost:11434/api/tags', { timeout: 5000 });
      
      if (response.status === 200) {
        const models = response.data.models || [];
        this.status.ollama = true;
        console.log(`✅ Ollama متصل (${models.length} نماذج)`);
        
        if (models.length === 0) {
          this.issues.push('لا توجد نماذج في Ollama');
          this.solutions.push('تنزيل نموذج: ollama pull llama3.1:8b');
        } else {
          // اختبار إرسال رسالة
          try {
            const chatResponse = await axios.post('http://localhost:11434/api/chat', {
              model: models[0].name,
              messages: [{ role: 'user', content: 'test' }],
              stream: false
            }, { timeout: 15000 });
            
            if (chatResponse.data?.message?.content) {
              console.log('✅ Ollama يرد على الرسائل بنجاح');
            }
          } catch (error) {
            this.issues.push('Ollama لا يرد على الرسائل');
            this.solutions.push('إعادة تشغيل Ollama: ollama serve');
          }
        }
      }
    } catch (error) {
      this.status.ollama = false;
      console.log('❌ Ollama غير متصل');
      this.issues.push('Ollama غير متاح');
      this.solutions.push('تشغيل Ollama: ollama serve');
    }
  }

  // فحص OpenRouter
  async checkOpenRouter() {
    console.log('\n🌐 فحص OpenRouter...');
    
    // البحث عن مفتاح API في الملفات
    const apiKey = this.findApiKey();
    
    if (!apiKey) {
      console.log('⚠️ لم يتم العثور على مفتاح OpenRouter API');
      this.issues.push('مفتاح OpenRouter API غير موجود');
      this.solutions.push('إضافة مفتاح API في الإعدادات');
      return;
    }

    try {
      const response = await axios.get('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://ai-chat-bot.local',
          'X-Title': 'AI Chat Bot'
        },
        timeout: 10000
      });

      if (response.status === 200) {
        this.status.openrouter = true;
        console.log(`✅ OpenRouter متصل (${response.data.data?.length || 0} نماذج)`);
      }
    } catch (error) {
      this.status.openrouter = false;
      if (error.response?.status === 401) {
        console.log('❌ مفتاح OpenRouter API غير صالح');
        this.issues.push('مفتاح OpenRouter API غير صالح');
        this.solutions.push('تحديث مفتاح API من openrouter.ai');
      } else {
        console.log('❌ OpenRouter غير متصل');
        this.issues.push('مشكلة في الاتصال بـ OpenRouter');
        this.solutions.push('فحص الاتصال بالإنترنت');
      }
    }
  }

  // البحث عن مفتاح API
  findApiKey() {
    const possibleFiles = [
      'src/services/AdvancedAIService.ts',
      'src/services/OpenRouterAPI.ts',
      '.env',
      'config.json'
    ];

    for (const file of possibleFiles) {
      try {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          const match = content.match(/sk-or-v1-[a-f0-9]{64}/);
          if (match) {
            return match[0];
          }
        }
      } catch (error) {
        // تجاهل الأخطاء
      }
    }

    return null;
  }

  // فحص إعدادات التطبيق
  async checkAppConfig() {
    console.log('\n⚙️ فحص إعدادات التطبيق...');
    
    const configFiles = [
      'package.json',
      'tsconfig.json',
      'vite.config.ts',
      'src/main/main.ts'
    ];

    let configOk = true;

    for (const file of configFiles) {
      if (!fs.existsSync(file)) {
        console.log(`❌ ملف مفقود: ${file}`);
        this.issues.push(`ملف الإعداد مفقود: ${file}`);
        configOk = false;
      }
    }

    if (configOk) {
      console.log('✅ جميع ملفات الإعداد موجودة');
      this.status.config = true;
    }
  }

  // فحص بناء التطبيق
  async checkAppBuild() {
    console.log('\n🔨 فحص بناء التطبيق...');
    
    if (fs.existsSync('dist')) {
      console.log('✅ مجلد البناء موجود');
      this.status.app = true;
    } else {
      console.log('⚠️ مجلد البناء غير موجود');
      this.issues.push('التطبيق غير مبني');
      this.solutions.push('بناء التطبيق: npm run build');
    }
  }

  // إنتاج التقرير
  generateReport() {
    console.log('\n📊 تقرير التشخيص:');
    console.log('='.repeat(50));
    
    const services = [
      { name: 'Ollama', status: this.status.ollama },
      { name: 'OpenRouter', status: this.status.openrouter },
      { name: 'إعدادات التطبيق', status: this.status.config },
      { name: 'بناء التطبيق', status: this.status.app }
    ];

    services.forEach(service => {
      const icon = service.status ? '✅' : '❌';
      const status = service.status ? 'يعمل' : 'لا يعمل';
      console.log(`${icon} ${service.name}: ${status}`);
    });

    if (this.issues.length > 0) {
      console.log('\n⚠️ المشاكل المكتشفة:');
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    } else {
      console.log('\n🎉 لا توجد مشاكل! التطبيق جاهز للعمل');
    }
  }

  // تقديم الحلول
  provideSolutions() {
    if (this.solutions.length > 0) {
      console.log('\n💡 الحلول المقترحة:');
      this.solutions.forEach((solution, index) => {
        console.log(`${index + 1}. ${solution}`);
      });

      // إنشاء ملف حلول تلقائي
      this.createAutoFixScript();
    }
  }

  // إنشاء سكريبت إصلاح تلقائي
  createAutoFixScript() {
    const script = `@echo off
echo 🔧 بدء الإصلاح التلقائي...

${this.solutions.includes('تشغيل Ollama: ollama serve') ? 'echo 🦙 تشغيل Ollama...\nstart /B ollama serve\ntimeout /t 5' : ''}

${this.solutions.includes('تنزيل نموذج: ollama pull llama3.1:8b') ? 'echo 📥 تنزيل نموذج Llama...\nollama pull llama3.1:8b' : ''}

${this.solutions.includes('بناء التطبيق: npm run build') ? 'echo 🔨 بناء التطبيق...\nnpm run build' : ''}

echo ✅ تم الإصلاح التلقائي!
pause`;

    fs.writeFileSync('auto-fix.bat', script);
    console.log('\n🤖 تم إنشاء ملف الإصلاح التلقائي: auto-fix.bat');
  }
}

// تشغيل التشخيص
const diagnostic = new SmartDiagnostic();
diagnostic.runFullDiagnostic().catch(console.error);
