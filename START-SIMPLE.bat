@echo off
cls
color 0A

echo.
echo ===============================================================================
echo                    AI Chat Bot - Advanced AI Application                      
echo                          With Open Source Models                         
echo ===============================================================================
echo.

echo Checking required dependencies...

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed
    echo [INFO] Please download and install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo [OK] Node.js is available
)

REM Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available
    echo [INFO] Please reinstall Node.js
    echo.
    pause
    exit /b 1
) else (
    echo [OK] npm is available
)

REM Check node_modules
if not exist "node_modules" (
    echo [WARNING] Dependencies not installed
    echo [INFO] Installing dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        echo.
        pause
        exit /b 1
    )
) else (
    echo [OK] Dependencies are available
)

REM Check dist folder
if not exist "dist" (
    echo [INFO] Building application...
    call npm run build
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to build application
        echo.
        pause
        exit /b 1
    )
)

echo [OK] All dependencies are ready

echo.
echo ===============================================================================
echo                        Open Source AI Models - Updated                   
echo                                                                              
echo   Recommended Models for Beginners:                                        
echo   * Phi-3 Mini - Light and Fast (2.3GB) - 4GB RAM                         
echo   * Llama 3.1 8B - Best for General Use (4.7GB) - 8GB RAM           
echo                                                                              
echo   For Developers and Programmers:                                                  
echo   * CodeLlama 13B - Specialized in Programming (7.3GB) - 16GB RAM               
echo                                                                              
echo   For Advanced Performance:                                                        
echo   * Mistral 7B - Fast and Advanced (4.1GB) - 8GB RAM                        
echo   * Gemma 2 9B - Google's New Model (5.4GB) - 12GB RAM                
echo   * Qwen 2.5 7B - Multilingual, Excellent for Arabic (4.4GB) - 8GB RAM        
echo                                                                              
echo   For Powerful Hardware (Professional):                                             
echo   * Llama 3.1 70B - Most Powerful Model Available (40GB) - 64GB RAM                 
echo                                                                              
echo   New Features:                                                      
echo   * Smart model categorization by usage                                       
echo   * Detailed information about each model requirements                                     
echo   * Enhanced interface for selecting and downloading models                                     
echo   * Performance badges for each model                                        
echo   * Warnings for models that need powerful hardware                                 
echo ===============================================================================
echo.

echo Starting Open Source Models System...
echo Note: Fallback system will work if models are not loaded
echo.

REM Check Ollama
echo Checking Ollama availability...
ollama --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Ollama not installed - Only cloud models will work
    echo [INFO] To install Ollama: https://ollama.ai/download/windows
    echo [INFO] Quick start commands:
    echo   ollama pull phi3:mini        # Light model (2.3GB)
    echo   ollama pull llama3.1:8b      # Best general model (4.7GB)
    echo   ollama pull codellama:13b    # For programming (7.3GB)
) else (
    echo [OK] Ollama is available
    echo Checking loaded models...
    ollama list
)

echo.
echo Starting the application...
echo The app will open in your browser automatically at: http://localhost:5173
echo.

REM Start the application in development mode
start "AI Chat Bot Server" cmd /k "npm run dev"

REM Wait a bit for the server to start
echo Waiting for the server to start...
timeout /t 8 /nobreak >nul

echo Opening browser...
start http://localhost:5173

echo.
echo ===============================================================================
echo                        Application is Ready!                     
echo                                                                              
echo   URL: http://localhost:5173                                    
echo   Status: Active and Independent                                                    
echo   Response: Instant                                                        
echo   Privacy: Complete (no external data sent)                             
echo                                                                              
echo   Usage Tips:                                                       
echo   * Try different models from the top menu                               
echo   * Use the template library for best results                     
echo   * The system learns from your interactions                            
echo   * Works completely offline                                
echo                                                                              
echo   Example prompts to try:                                                          
echo   * "Explain React in simple terms"                                          
echo   * "Write a short story about the future"                                       
echo   * "What are the best marketing strategies?"                                     
echo   * "Explain Newton's second law"                                           
echo ===============================================================================
echo.

echo Thank you for using the Advanced AI Chat Application!
echo No need for Msty or LM Studio - everything is integrated and advanced!
echo.
pause
