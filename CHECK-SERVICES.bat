@echo off
chcp 65001 >nul
title 🔍 AI Chat Bot - فحص الخدمات

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        🔍 فحص خدمات الذكاء الاصطناعي                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص الخدمات المحلية المتاحة...
echo.

:: فحص Msty
echo ╔═══════════════════════════════════════════════════════════════════════════════
echo ║ 🟣 فحص Msty
echo ╚═══════════════════════════════════════════════════════════════════════════════
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:10000/v1/models' -TimeoutSec 5 -ErrorAction Stop; Write-Host '✅ Msty متصل على المنفذ 10000'; Write-Host '📊 عدد النماذج:' ($response.Content | ConvertFrom-Json).data.Count } catch { Write-Host '❌ Msty غير متصل على المنفذ 10000' }"
echo.

:: فحص LM Studio
echo ╔═══════════════════════════════════════════════════════════════════════════════
echo ║ 🏠 فحص LM Studio
echo ╚═══════════════════════════════════════════════════════════════════════════════
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:1234/v1/models' -TimeoutSec 5 -ErrorAction Stop; Write-Host '✅ LM Studio متصل على المنفذ 1234'; Write-Host '📊 عدد النماذج:' ($response.Content | ConvertFrom-Json).data.Count } catch { Write-Host '❌ LM Studio غير متصل على المنفذ 1234' }"
echo.

:: فحص Ollama
echo ╔═══════════════════════════════════════════════════════════════════════════════
echo ║ 🦙 فحص Ollama
echo ╚═══════════════════════════════════════════════════════════════════════════════
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:11434/api/tags' -TimeoutSec 5 -ErrorAction Stop; Write-Host '✅ Ollama متصل على المنفذ 11434'; $models = ($response.Content | ConvertFrom-Json).models; Write-Host '📊 عدد النماذج:' $models.Count; if ($models.Count -gt 0) { Write-Host '🤖 النماذج المتاحة:'; $models | ForEach-Object { Write-Host '   -' $_.name } } } catch { Write-Host '❌ Ollama غير متصل على المنفذ 11434' }"
echo.

:: فحص منافذ إضافية
echo ╔═══════════════════════════════════════════════════════════════════════════════
echo ║ 🔍 فحص منافذ إضافية
echo ╚═══════════════════════════════════════════════════════════════════════════════

:: فحص المنفذ 8080
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/v1/models' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ خدمة متاحة على المنفذ 8080' } catch { Write-Host '⚪ لا توجد خدمة على المنفذ 8080' }"

:: فحص المنفذ 3000
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/v1/models' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ خدمة متاحة على المنفذ 3000' } catch { Write-Host '⚪ لا توجد خدمة على المنفذ 3000' }"

:: فحص المنفذ 5000
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/v1/models' -TimeoutSec 3 -ErrorAction Stop; Write-Host '✅ خدمة متاحة على المنفذ 5000' } catch { Write-Host '⚪ لا توجد خدمة على المنفذ 5000' }"

echo.
echo ╔═══════════════════════════════════════════════════════════════════════════════
echo ║ 🌐 فحص الاتصال بالإنترنت
echo ╚═══════════════════════════════════════════════════════════════════════════════
powershell -Command "try { $response = Invoke-WebRequest -Uri 'https://api.openrouter.ai/api/v1/models' -TimeoutSec 10 -ErrorAction Stop; Write-Host '✅ الاتصال بالإنترنت متاح'; Write-Host '🌐 OpenRouter API متاح' } catch { Write-Host '❌ مشكلة في الاتصال بالإنترنت أو OpenRouter API' }"

echo.
echo ╔═══════════════════════════════════════════════════════════════════════════════
echo ║ 💡 التوصيات
echo ╚═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🟣 لتشغيل Msty:
echo    1. حمل من https://msty.app/
echo    2. ثبت وشغل التطبيق
echo    3. حمل نموذج من المكتبة
echo.
echo 🏠 لتشغيل LM Studio:
echo    1. حمل من https://lmstudio.ai/
echo    2. ثبت وشغل التطبيق
echo    3. حمل نموذج وشغل الخادم المحلي
echo.
echo 🦙 لتشغيل Ollama:
echo    1. حمل من https://ollama.ai/
echo    2. ثبت التطبيق
echo    3. شغل: ollama pull llama3.1:8b
echo.
echo 🌐 للخدمات السحابية:
echo    1. احصل على مفتاح API من https://openrouter.ai/
echo    2. أدخله في إعدادات التطبيق
echo.

echo ╔═══════════════════════════════════════════════════════════════════════════════
echo ║ 🚀 بدء التطبيق
echo ╚═══════════════════════════════════════════════════════════════════════════════
echo.
echo هل تريد بدء تشغيل التطبيق الآن؟
echo [1] نعم - تشغيل في وضع التطوير
echo [2] نعم - بناء للإنتاج
echo [3] لا - الخروج
echo.
set /p "choice=اختر (1-3): "

if "%choice%"=="1" (
    echo 🚀 بدء التطبيق في وضع التطوير...
    call START-DEV.bat
) else if "%choice%"=="2" (
    echo 🏗️ بناء التطبيق للإنتاج...
    call START-PROD.bat
) else (
    echo 👋 شكراً لك!
)

pause
