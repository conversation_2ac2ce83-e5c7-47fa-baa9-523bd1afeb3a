@echo off
title Build Fixed AI Chat Bot

echo ========================================
echo    Build Fixed - AI Chat Bot
echo ========================================
echo.

echo Step 1: Closing any running instances...
taskkill /F /IM "AI Chat Bot - *" /T 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Cleaning previous build...
if exist "dist\renderer" (
    echo Keeping renderer build...
) else (
    echo Building renderer...
    call npm run build:renderer
)

echo Step 3: Creating necessary directories...
if not exist "dist" mkdir dist
if not exist "dist\main" mkdir dist\main

echo Step 4: Copying main.js to dist\main...
copy main.js dist\main\main.js /Y
echo main.js copied successfully!

echo Step 5: Creating electron-builder configuration file...
echo { > build-config.json
echo   "appId": "com.aichatbot.app", >> build-config.json
echo   "productName": "AI Chat Bot - بوت الدردشة الذكي", >> build-config.json
echo   "directories": { >> build-config.json
echo     "output": "release" >> build-config.json
echo   }, >> build-config.json
echo   "files": [ >> build-config.json
echo     "dist/**/*", >> build-config.json
echo     "node_modules/**/*", >> build-config.json
echo     "assets/**/*", >> build-config.json
echo     "main.js", >> build-config.json
echo     "index.html" >> build-config.json
echo   ], >> build-config.json
echo   "extraResources": [ >> build-config.json
echo     "assets/**/*" >> build-config.json
echo   ], >> build-config.json
echo   "win": { >> build-config.json
echo     "target": [ >> build-config.json
echo       { >> build-config.json
echo         "target": "portable", >> build-config.json
echo         "arch": ["x64"] >> build-config.json
echo       } >> build-config.json
echo     ], >> build-config.json
echo     "icon": "assets/icon.ico" >> build-config.json
echo   } >> build-config.json
echo } >> build-config.json

echo Step 6: Building the application with custom config...
echo This process may take a few minutes...
call npx electron-builder --config build-config.json --win portable

echo.
echo Process completed!
echo.

if exist "release\win-unpacked" (
    echo Portable version created in release\win-unpacked folder
    echo You can run the application directly from there.
    explorer release\win-unpacked
)

echo.
pause
