// خدمة الذكاء الاصطناعي الموحدة - بناء من الصفر
import { OpenRouterAPI, AIModel, ChatMessage } from './OpenRouterAPI'
import { OllamaAPI } from './OllamaAPI'

export interface AIServiceConfig {
  openRouterApiKey?: string
  preferredProvider?: 'local' | 'ollama' | 'openrouter'
}

export interface AIResponse {
  success: boolean
  response?: string
  error?: string
  provider?: string
  model?: string
}

export class AIService {
  private openRouterAPI: OpenRouterAPI | null = null
  private ollamaAPI: OllamaAPI
  private config: AIServiceConfig = {}

  // النماذج المدمجة للكتابة العربية
  private localModels: AIModel[] = [
    {
      id: 'local/arabic-writer',
      name: 'الكاتب العربي',
      description: 'متخصص في الكتابة الأدبية والإبداعية باللغة العربية',
      provider: 'local',
      contextLength: 4096
    },
    {
      id: 'local/novelist',
      name: 'كاتب الروايات',
      description: 'خبير في كتابة الروايات وتطوير الشخصيات والحبكة',
      provider: 'local',
      contextLength: 8192
    },
    {
      id: 'local/poet',
      name: 'الشاعر العربي',
      description: 'متمكن من جميع بحور الشعر العربي والكتابة الشعرية',
      provider: 'local',
      contextLength: 2048
    },
    {
      id: 'local/general-chat',
      name: 'المحادثة العامة',
      description: 'للمحادثات العامة والأسئلة المتنوعة',
      provider: 'local',
      contextLength: 4096
    }
  ]

  private ollamaModels: AIModel[] = []
  private openRouterModels: AIModel[] = []

  constructor(config: AIServiceConfig = {}) {
    this.config = config
    this.ollamaAPI = new OllamaAPI()

    if (config.openRouterApiKey) {
      this.openRouterAPI = new OpenRouterAPI(config.openRouterApiKey)
    }
  }

  // تحديث الإعدادات
  updateConfig(config: AIServiceConfig): void {
    this.config = { ...this.config, ...config }

    if (config.openRouterApiKey) {
      this.openRouterAPI = new OpenRouterAPI(config.openRouterApiKey)
    }
  }

  // إرسال رسالة
  async sendMessage(
    message: string,
    modelId: string = 'local/general-chat',
    conversationHistory: ChatMessage[] = []
  ): Promise<AIResponse> {
    try {
      console.log(`📤 إرسال رسالة إلى النموذج: ${modelId}`)

      // تحديد نوع النموذج
      if (modelId.startsWith('local/')) {
        return await this.handleLocalModel(modelId, message, conversationHistory)
      } else if (modelId.startsWith('ollama/')) {
        return await this.handleOllamaModel(modelId, message, conversationHistory)
      } else {
        return await this.handleOpenRouterModel(modelId, message, conversationHistory)
      }
    } catch (error: any) {
      console.error('❌ خطأ في إرسال الرسالة:', error)
      return {
        success: false,
        error: `خطأ في إرسال الرسالة: ${error.message}`
      }
    }
  }

  // معالجة النماذج المحلية
  private async handleLocalModel(
    modelId: string,
    message: string,
    history: ChatMessage[]
  ): Promise<AIResponse> {
    const modelType = modelId.replace('local/', '')

    let response: string

    switch (modelType) {
      case 'arabic-writer':
        response = await this.generateArabicWriterResponse(message, history)
        break
      case 'novelist':
        response = await this.generateNovelistResponse(message, history)
        break
      case 'poet':
        response = await this.generatePoetResponse(message, history)
        break
      case 'general-chat':
        response = await this.generateGeneralChatResponse(message, history)
        break
      default:
        response = await this.generateGeneralChatResponse(message, history)
    }

    return {
      success: true,
      response,
      provider: 'local',
      model: modelId
    }
  }

  // معالجة نماذج Ollama
  private async handleOllamaModel(
    modelId: string,
    message: string,
    history: ChatMessage[]
  ): Promise<AIResponse> {
    try {
      const ollamaModelName = modelId.replace('ollama/', '')
      const response = await this.ollamaAPI.sendMessage(message, ollamaModelName, history)

      return {
        success: true,
        response,
        provider: 'ollama',
        model: modelId
      }
    } catch (error: any) {
      return {
        success: false,
        error: `خطأ في Ollama: ${error.message}`,
        provider: 'ollama'
      }
    }
  }

  // معالجة نماذج OpenRouter
  private async handleOpenRouterModel(
    modelId: string,
    message: string,
    history: ChatMessage[]
  ): Promise<AIResponse> {
    if (!this.openRouterAPI) {
      return {
        success: false,
        error: 'لم يتم تكوين مفتاح OpenRouter API'
      }
    }

    try {
      const response = await this.openRouterAPI.sendMessage(message, modelId, history)

      return {
        success: true,
        response,
        provider: 'openrouter',
        model: modelId
      }
    } catch (error: any) {
      return {
        success: false,
        error: `خطأ في OpenRouter: ${error.message}`,
        provider: 'openrouter'
      }
    }
  }

  // الحصول على جميع النماذج المتاحة
  async getAllModels(): Promise<AIModel[]> {
    const allModels: AIModel[] = []

    // النماذج المحلية (متاحة دائماً)
    allModels.push(...this.localModels)

    // نماذج Ollama
    try {
      const ollamaModels = await this.getOllamaModels()
      allModels.push(...ollamaModels)
    } catch (error) {
      console.log('⚠️ Ollama غير متاح')
    }

    // نماذج OpenRouter
    try {
      const openRouterModels = await this.getOpenRouterModels()
      allModels.push(...openRouterModels)
    } catch (error) {
      console.log('⚠️ OpenRouter غير متاح')
    }

    return allModels
  }

  // الحصول على نماذج Ollama
  private async getOllamaModels(): Promise<AIModel[]> {
    try {
      const isAvailable = await this.ollamaAPI.checkAvailability()
      if (!isAvailable) {
        return []
      }

      const models = await this.ollamaAPI.getAvailableModels()
      return models.map(model => ({
        id: `ollama/${model.name}`,
        name: model.name,
        description: `نموذج Ollama محلي - ${model.name}`,
        provider: 'ollama',
        contextLength: 4096
      }))
    } catch (error) {
      console.error('خطأ في جلب نماذج Ollama:', error)
      return []
    }
  }

  // الحصول على نماذج OpenRouter
  private async getOpenRouterModels(): Promise<AIModel[]> {
    if (!this.openRouterAPI) {
      return []
    }

    try {
      const models = await this.openRouterAPI.getAvailableModels()
      return models.filter(model =>
        // فلترة النماذج المهمة فقط
        model.id.includes('llama') ||
        model.id.includes('claude') ||
        model.id.includes('gpt') ||
        model.id.includes('gemini')
      ).slice(0, 10) // أول 10 نماذج فقط
    } catch (error) {
      console.error('خطأ في جلب نماذج OpenRouter:', error)
      return []
    }
  }

  // فحص حالة الخدمات
  async getServicesStatus(): Promise<{
    local: { available: boolean; modelsCount: number; status: string }
    ollama: { available: boolean; modelsCount: number; status: string }
    openRouter: { available: boolean; modelsCount: number; status: string }
  }> {
    const status = {
      local: {
        available: true,
        modelsCount: this.localModels.length,
        status: 'متاح دائماً'
      },
      ollama: {
        available: false,
        modelsCount: 0,
        status: 'غير متصل'
      },
      openRouter: {
        available: false,
        modelsCount: 0,
        status: 'غير مكوّن'
      }
    }

    // فحص Ollama
    try {
      const ollamaAvailable = await this.ollamaAPI.checkAvailability()
      if (ollamaAvailable) {
        const ollamaModels = await this.getOllamaModels()
        status.ollama = {
          available: true,
          modelsCount: ollamaModels.length,
          status: 'متصل'
        }
      }
    } catch (error) {
      console.log('Ollama غير متاح')
    }

    // فحص OpenRouter
    if (this.openRouterAPI) {
      try {
        const openRouterModels = await this.getOpenRouterModels()
        status.openRouter = {
          available: true,
          modelsCount: openRouterModels.length,
          status: 'متصل'
        }
      } catch (error) {
        status.openRouter.status = 'خطأ في الاتصال'
      }
    }

    return status
  }

  // دوال النماذج المحلية للكتابة العربية

  private async generateArabicWriterResponse(message: string, history: ChatMessage[]): Promise<string> {
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('قصة') || lowerMessage.includes('حكاية')) {
      return `✍️ **الكاتب العربي - قصة:**

في زمن ليس ببعيد، في مدينة تحتضن الأحلام بين أزقتها الضيقة...

كان هناك ${this.getRandomCharacter()} يحمل في قلبه حلماً كبيراً. كل صباح، كان ينظر من نافذة غرفته إلى الأفق البعيد، يتخيل المغامرات التي تنتظره.

وذات يوم، قرر أن يخرج ويحقق حلمه. فتح الباب، وخطا خطوته الأولى نحو المجهول...

*هل تريد مني إكمال هذه القصة؟ أم تفضل أن نبدأ قصة جديدة من فكرتك؟*`
    }

    if (lowerMessage.includes('مقال') || lowerMessage.includes('كتابة')) {
      return `📝 **الكاتب العربي - مقال:**

الكتابة فن وعلم، تحتاج إلى صبر وممارسة. إليك بعض النصائح للكتابة الجيدة:

**🎯 وضوح الفكرة:**
ابدأ بفكرة واضحة ومحددة. اسأل نفسك: ما الرسالة التي أريد إيصالها؟

**📚 التنظيم:**
رتب أفكارك في مقدمة وعرض وخاتمة. كل فقرة يجب أن تحمل فكرة واحدة.

**🎨 الأسلوب:**
استخدم لغة بسيطة وواضحة. تجنب التعقيد غير المبرر.

**✨ المراجعة:**
اقرأ ما كتبت أكثر من مرة. الكتابة الجيدة تحتاج إلى تنقيح.

ما الموضوع الذي تريد الكتابة عنه؟`
    }

    return `✍️ **الكاتب العربي:**

مرحباً بك في عالم الكتابة العربية! أنا هنا لمساعدتك في:

📖 **كتابة القصص** - من القصص القصيرة إلى الحكايات الطويلة
📝 **المقالات والنصوص** - كتابة مقالات أدبية وفكرية
✨ **التحرير والتنقيح** - تحسين النصوص الموجودة
🎭 **الحوار والشخصيات** - تطوير الحوار وبناء الشخصيات

ما نوع الكتابة التي تريد العمل عليها؟`
  }

  private async generateNovelistResponse(message: string, history: ChatMessage[]): Promise<string> {
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('شخصية') || lowerMessage.includes('بطل')) {
      return `👤 **كاتب الروايات - تطوير الشخصيات:**

لبناء شخصية مقنعة، فكر في هذه العناصر:

**🎭 الخلفية:**
- العمر والمهنة والخلفية الاجتماعية
- الأحداث المهمة في ماضي الشخصية
- العلاقات العائلية والاجتماعية

**💭 الشخصية الداخلية:**
- الأهداف والأحلام والمخاوف
- نقاط القوة والضعف
- القيم والمبادئ التي تؤمن بها

**🗣️ طريقة التعبير:**
- أسلوب الكلام والمفردات المستخدمة
- لغة الجسد والعادات الشخصية
- ردود الأفعال في المواقف المختلفة

**🔄 التطور:**
- كيف ستتغير الشخصية عبر الرواية؟
- ما التحديات التي ستواجهها؟

أخبرني عن الشخصية التي تريد تطويرها!`
    }

    if (lowerMessage.includes('حبكة') || lowerMessage.includes('أحداث')) {
      return `📚 **كاتب الروايات - بناء الحبكة:**

الحبكة الجيدة تحتاج إلى:

**🎬 البداية (التمهيد):**
- تقديم الشخصيات والمكان والزمان
- إثارة اهتمام القارئ من الصفحات الأولى
- زرع بذور الصراع الرئيسي

**⚡ الوسط (التصاعد):**
- تطوير الصراع وتعقيد الأحداث
- إضافة عقبات جديدة أمام البطل
- الكشف التدريجي عن المعلومات

**🎯 النهاية (الذروة والحل):**
- الذروة: أقوى لحظات التوتر
- الحل: كيف يتم حل الصراع؟
- الخاتمة: ما تأثير الأحداث على الشخصيات؟

**💡 نصائح مهمة:**
- كل فصل يجب أن ينتهي بسؤال أو تشويق
- اربط الأحداث ببعضها منطقياً
- لا تحل كل شيء بالصدفة

ما نوع الحبكة التي تعمل عليها؟`
    }

    return `📚 **كاتب الروايات:**

أهلاً بك في عالم كتابة الروايات! يمكنني مساعدتك في:

👥 **تطوير الشخصيات** - بناء شخصيات معقدة ومقنعة
📖 **بناء الحبكة** - تطوير قصة مشوقة ومترابطة
🌍 **بناء العالم** - خلق أماكن وأزمنة مقنعة
💬 **كتابة الحوار** - حوار طبيعي يكشف الشخصيات
✏️ **التقنيات السردية** - وجهات النظر والأساليب المختلفة

ما الجانب من كتابة الروايات الذي تريد التركيز عليه؟`
  }

  private async generatePoetResponse(message: string, history: ChatMessage[]): Promise<string> {
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('قصيدة') || lowerMessage.includes('شعر')) {
      const poems = [
        `🎭 **الشاعر العربي:**

في بحر الكلمات أبحر
وبالمعاني أتفكر
أكتب ما يشفي الأرق
ويروي عطش المشتاق

يا صديق القلم والورق
اكتب ما يشفي الأرق
فالشعر نبع من الوجدان
يروي عطش الإنسان`,

        `🌟 **من وحي اللحظة:**

بالحرف نرسم الأحلام
ونكتب أجمل الكلام
في عالم الشعر نسبح
كالطير في الفضاء يصدح

والقافية تنساب رقيقة
كالنهر في الروض يجري
تحمل معها الأحاسيس
وتبوح بأعمق المشاعر`
      ]

      const selectedPoem = poems[Math.floor(Math.random() * poems.length)]
      return `${selectedPoem}

*هذه أبيات من وحي اللحظة. ما الموضوع الذي تريد أن نكتب عنه شعراً؟*`
    }

    if (lowerMessage.includes('بحر') || lowerMessage.includes('وزن')) {
      return `🎵 **الشاعر العربي - البحور والأوزان:**

البحور الشعرية هي الأوزان التي يُكتب عليها الشعر العربي:

**🌊 البحور الشائعة:**

**البحر الطويل:** فعولن مفاعيلن فعولن مفاعيلن
*مثال: "أراك عصي الدمع شيمتك الصبر"*

**البحر البسيط:** مستفعلن فاعلن مستفعلن فاعلن
*مثال: "إن الذي سمك السماء بنى لنا"*

**البحر الكامل:** متفاعلن متفاعلن متفاعلن
*مثال: "أضحى التنائي بديلاً من تدانينا"*

**البحر الوافر:** مفاعلتن مفاعلتن فعولن
*مثال: "سلو قلبي غداة سلا وثابا"*

**💡 نصائح للكتابة:**
- ابدأ بالبحور البسيطة
- اقرأ الشعر بصوت عالٍ لتشعر بالإيقاع
- لا تضحي بالمعنى من أجل الوزن

أي بحر تريد أن نتدرب عليه؟`
    }

    return `🎭 **الشاعر العربي:**

أهلاً بك في عالم الشعر العربي! يمكنني مساعدتك في:

📝 **كتابة القصائد** - شعر عمودي وحر ونثري
🎵 **البحور والأوزان** - تعلم أوزان الشعر العربي
🎨 **الصور الشعرية** - استخدام الاستعارة والكناية
💫 **القوافي** - اختيار القوافي المناسبة
✨ **التحليل الشعري** - فهم وتحليل القصائد

ما نوع الشعر الذي تريد العمل عليه؟`
  }

  private async generateGeneralChatResponse(message: string, history: ChatMessage[]): Promise<string> {
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('مرحبا') || lowerMessage.includes('السلام') || lowerMessage.includes('أهلا')) {
      const greetings = [
        "مرحباً بك! أنا مساعدك الذكي للكتابة العربية. كيف يمكنني مساعدتك اليوم؟ 😊",
        "أهلاً وسهلاً! سعيد بلقائك. هل تريد العمل على مشروع كتابي معين؟ 📝",
        "السلام عليكم! أنا هنا لمساعدتك في الكتابة والإبداع. ما الذي تود أن نعمل عليه؟ ✨"
      ]
      return greetings[Math.floor(Math.random() * greetings.length)]
    }

    if (lowerMessage.includes('شكرا') || lowerMessage.includes('شكراً')) {
      const thanks = [
        "العفو! سعيد لأنني استطعت مساعدتك. هل تحتاج إلى أي شيء آخر؟ 😊",
        "لا شكر على واجب! الكتابة شغفي ومساعدتك متعة. ما التالي؟ 💪",
        "أهلاً وسهلاً! دائماً في خدمة الكتّاب والمبدعين. 🌟"
      ]
      return thanks[Math.floor(Math.random() * thanks.length)]
    }

    if (lowerMessage.includes('مساعدة') || lowerMessage.includes('ساعدني')) {
      return `🤝 **كيف يمكنني مساعدتك؟**

يمكنني مساعدتك في:

✍️ **الكتابة الإبداعية:**
- القصص والحكايات
- الروايات وتطوير الشخصيات
- الشعر بأنواعه

📝 **الكتابة العملية:**
- المقالات والنصوص
- التحرير والتنقيح
- تحسين الأسلوب

🎓 **التعلم:**
- قواعد الكتابة الجيدة
- تقنيات السرد
- أساسيات الشعر

فقط أخبرني ما الذي تريد العمل عليه!`
    }

    const responses = [
      "هذا موضوع مثير للاهتمام! كيف يمكنني مساعدتك في تطويره؟ 🤔",
      "أحب طريقة تفكيرك! دعنا نعمل على هذا معاً. ما التفاصيل؟ ✨",
      "فكرة رائعة! يمكننا بناء عليها وتطويرها. من أين نبدأ؟ 🚀",
      "شكراً لمشاركة هذا معي. كيف يمكنني مساعدتك بشكل أفضل؟ 💭"
    ]

    return responses[Math.floor(Math.random() * responses.length)]
  }

  // دالة مساعدة لتوليد شخصيات عشوائية
  private getRandomCharacter(): string {
    const characters = [
      'شاب طموح',
      'فتاة حالمة',
      'رجل حكيم',
      'امرأة قوية',
      'طفل فضولي',
      'عالم مجتهد',
      'فنان مبدع',
      'تاجر ماهر'
    ]
    return characters[Math.floor(Math.random() * characters.length)]
  }
}
